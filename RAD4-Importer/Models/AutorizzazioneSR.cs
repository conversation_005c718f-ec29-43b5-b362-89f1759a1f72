﻿using System;

namespace RAD4_Importer.Models
{
    public class AutorizzazioneSR
    {
        public int? ID_AUTORIZZAZIONE_SR { get; set; }
        public string CODICE_DOMANDA { get; set; }
        public string COGNOME { get; set; }
        public string NOME { get; set; }
        public string DEN<PERSON>INAZIONE { get; set; }
        public string CODICE_FISCALE { get; set; }
        public string SESSO { get; set; }
        public string LUOGO_NASCITA { get; set; }
        public string CODICE_LUOGO_NASCITA { get; set; }
        public DateTime? DATA_NASCITA { get; set; }
        public string REGIONE_RESIDENZA { get; set; }
        public string CODICE_REGIONE_RESIDENZA { get; set; }
        public string PROVINCIA_RESIDENZA { get; set; }
        public string CODICE_PROVINCIA_RESIDENZA { get; set; }
        public string COMUNE_RESIDENZA { get; set; }
        public string CODICE_COMUNE_RESIDENZA { get; set; }
        public string INDIRIZZO_RESIDENZA { get; set; }
        public string CAP_RESIDENZA { get; set; }
        public string ID_ANAGRAFICA_ISPETTORATO { get; set; }
        public string PEC { get; set; }
        public string EMAIL { get; set; }
        public string TELEFONO { get; set; }
        public int? ID_TIPO_ASSEGNATARIO { get; set; }
        public bool? RINNOVO { get; set; }
        public int? ID_AUTORIZZAZIONE_RINNOVATA { get; set; }
        public string NUMERO_AUTORIZZAZIONE { get; set; }
        public int? ID_TIPO_AUTORIZZAZIONE { get; set; }
        public int? ID_STATO_AUTORIZZAZIONE { get; set; }
        public DateTime? DATA_RILASCIO { get; set; }
        public DateTime? DATA_SCADENZA { get; set; }
        public int? ID_RADIOAMATORE_SR { get; set; }
        public string CODICE_PROTOCOLLO_ENTRATA { get; set; }
        public DateTime? DATA_PROTOCOLLO_ENTRATA { get; set; }
        public string CODICE_PROTOCOLLO_USCITA { get; set; }
        public DateTime? DATA_PROTOCOLLO_USCITA { get; set; }
        public string UTENTE { get; set; }
        public int? ID_STAZIONE_RIPETITRICE { get; set; }
        public DateTime? TIMESTAMP_MODIFICA { get; set; }
    }
}
