﻿using System;

namespace RAD4_Importer.Models
{
    public class StazioneRipetitrice
    {
        public int? ID_STAZIONE_RIPETITRICE { get; set; }
        public string REGIONE { get; set; }
        public string CODICE_REGIONE { get; set; }
        public string PROVINCIA { get; set; }
        public string CODICE_PROVINCIA { get; set; }
        public string COMUNE { get; set; }
        public string CODICE_COMUNE { get; set; }
        public string INDIRIZZO { get; set; }
        public string CIVICO { get; set; }
        public string CAP { get; set; }
        public string ID_ANAGRAFICA_ISPETTORATO { get; set; }
        public string LATITUDINE { get; set; }
        public string LONGITUDINE { get; set; }
        public string NOME_OPERATORE { get; set; }
        public string COGNOME_OPERATORE { get; set; }
        public string CODICE_FISCALE_OPERATORE { get; set; }
        public string NOMINATIVO_OPERATORE { get; set; }
        public string REGIONE_RESIDENZA_OPERATORE { get; set; }
        public string CODICE_REGIONE_RESIDENZA_OPERATORE { get; set; }
        public string PROVINCIA_RESIDENZA_OPERATORE { get; set; }
        public string CODICE_PROVINCIA_RESIDENZA_OPERATORE { get; set; }
        public string COMUNE_RESIDENZA_OPERATORE { get; set; }
        public string CODICE_COMUNE_RESIDENZA_OPERATORE { get; set; }
        public string INDIRIZZO_RESIDENZA_OPERATORE { get; set; }
        public string NUMERO_CIVICO_RESIDENZA_OPERATORE { get; set; }
        public string CAP_RESIDENZA_OPERATORE { get; set; }
        public string EMAIL_OPERATORE { get; set; }
        public string TELEFONO_OPERATORE { get; set; }
        public DateTime? TIMESTAMP_MODIFICA { get; set; }
    }
}
