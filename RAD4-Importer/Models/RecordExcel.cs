﻿using Inv.Fwk.Helper.DateTimeZone;
using Invitalia.Misure.Standard2.Utilities.Extensions;
using System;
using System.Collections.Generic;
using System.Reflection;

namespace RAD4_Importer.Models
{
    public class RecordExcel
    {
        private const string MAGGIORENNE = "Radioamatore (persona fisica)";
        private const string MINORE = "Radioamatore (minore)";
        private const string ASSOCIAZIONE = "Associazione";
        
        public string Regione { get; set; }
        public string Ispettorato { get; set; }
        public string Prefisso { get; set; }
        public string Suffisso_1 { get; set; }
        public string Suffisso_2 { get; set; }
        public string Suffisso_3 { get; set; }
        public string NominativoSR { get; set; }
        public string StatoNominativoSR { get; set; }
        public string DataRichiesta { get; set; }
        public string ProtocolloRichiesta { get; set; }
        public string TipologiaRichiedente { get; set; }
        public string NomeRadioamatore { get; set; }
        public string CognomeRadioamatore { get; set; }
        public string NominativoRadioamatore { get; set; }
        public string CodiceFiscale { get; set; }
        public string ComuneNascita { get; set; }
        public string ProvinciaNascita { get; set; }
        public string DataNascita { get; set; }
        public string Cittadinanza { get; set; }
        public string ComuneResidenza { get; set; }
        public string ProvinciaResidenza { get; set; }
        public string RegioneResidenza { get; set; }
        public string IndirizzoResidenza { get; set; }
        public string DenominazioneAssociazione { get; set; }
        public string CF_PartitaIVA_Associazione { get; set; }
        public string ComuneSedeAssociazione { get; set; }
        public string ProvinciaSedeAssociazione { get; set; }
        public string RegioneSedeAssociazione { get; set; }
        public string IndirizzoSedeAssociazione { get; set; }
        public string CAP_SedeAssociazione { get; set; }
        public string NomeRL { get; set; }
        public string CognomeRL { get; set; }
        public string NomeOperatore { get; set; }
        public string CognomeOperatore { get; set; }
        public string Email_PEC { get; set; }
        public string NomeMinorenne { get; set; }
        public string CognomeMinorenne { get; set; }
        public string TipoRichiesta { get; set; }
        public string DurataAutorizzazione { get; set; }
        public string ComuneImpianto { get; set; }
        public string ProvinciaImpianto { get; set; }
        public string RegioneImpianto { get; set; }
        public string IndirizzoImpianto { get; set; }
        public string DataProtocollo { get; set; }
        public string Protocollo { get; set; }
        public string CodiceComuneNascita { get; set; }
        public string CodiceComuneResidenzaPersonaFisica { get; set; }
        public string SiglaProvinciaResidenzaPersonaFisica { get; set; }
        public string SiglaRegioneResidenzaPersonaFisica { get; set; }
        public string CodiceComuneResidenzaAssociazione { get; set; }
        public string SiglaProvinciaResidenzaAssociazione { get; set; }
        public string SiglaRegioneResidenzaAssociazione { get; set; }
        public string CodiceComuneStazioneRipetitrice { get; set; }
        public string SiglaProvinciaStazioneRipetitrice { get; set; }
        public string SiglaRegioneStazioneRipetitrice { get; set; }

        private bool? _isMaggiorenne;
        public bool IsMaggiorenne => _isMaggiorenne ??= TipologiaRichiedente?.EqualsIgnoreCaseAndTrim(MAGGIORENNE) ?? true;

        private bool? _isMinorenne;
        public bool IsMinorenne => _isMinorenne ??= TipologiaRichiedente?.EqualsIgnoreCaseAndTrim(MINORE) ?? false;

        private bool? _isAssociazione;
        public bool IsAssociazione => _isAssociazione ??= TipologiaRichiedente?.EqualsIgnoreCaseAndTrim(ASSOCIAZIONE) ?? false;

        public DateTime? DataNascitaDateTime
        {
            get
            {
                if (!string.IsNullOrWhiteSpace(DataNascita) && DateTimeHelper.BeAValidDate(DataNascita))
                    return DateTimeHelper.ParseDataInput(DataNascita);
                else
                    return (DateTime?)null;
            }
        }

        public DateTime? DataProtocolloDateTime
        {
            get
            {
                if (!string.IsNullOrWhiteSpace(DataProtocollo) && DateTimeHelper.BeAValidDate(DataProtocollo))
                    return DateTimeHelper.ParseDataInput(DataProtocollo);
                else
                    return (DateTime?)null;
            }
        }
        public DateTime? DataRichiestaDateTime
        {
            get
            {
                if (!string.IsNullOrWhiteSpace(DataRichiesta) && DateTimeHelper.BeAValidDate(DataRichiesta))
                    return DateTimeHelper.ParseDataInput(DataRichiesta);
                else
                    return (DateTime?)null;
            }
        }

        public string ErrorMessage { get; set; }

        public void SetEmptyStringPropertiesToNull()
        {
            // Get all properties of the class
            PropertyInfo[] properties = GetType().GetProperties();

            foreach (var property in properties)
            {
                // Check if the property is a string and is empty or whitespace
                if (property.PropertyType == typeof(string))
                {
                    string value = (string)property.GetValue(this);

                    if (string.IsNullOrWhiteSpace(value))
                    {
                        // Set the string property to null
                        property.SetValue(this, null);
                    }
                }
            }
        }
    }
}
