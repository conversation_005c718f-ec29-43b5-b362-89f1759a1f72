﻿using Microsoft.Extensions.Configuration;
using OfficeOpenXml;
using RAD4_Importer.BL;
using RAD4_Importer.Extensions;
using RAD4_Importer.Mapper;
using RAD4_Importer.Models;
using System;
using System.Linq;
using System.Collections.Generic;
using System.IO;
using System.Text;

namespace RAD4_Importer
{
    internal class Program
    {
        static void Main(string[] args)
        {
            StringBuilder sb = new();
            var configuration = new ConfigurationBuilder()
                                    .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
                                    .Build();
            var mapper = AutomapperConfigurator.GetMapper();

            ImporterBL _bl = new(configuration, mapper, sb);

            sb.Log("Program launched\n");

            string excelFilePath = configuration.GetSection("Excel:InputFilePath").Value;

            ExcelPackage.LicenseContext = LicenseContext.NonCommercial;

            using (var package = new ExcelPackage(new FileInfo(excelFilePath)))
            {
                foreach (var sheet in package.Workbook.Worksheets)
                {
                    if (sheet.Hidden != eWorkSheetHidden.Visible)
                    {
                        sb.Log($"Sheet {sheet.Name} saltato in quanto nascosto\n");
                        continue;
                    }

                    if (!sheet.Name.StartsWith("IR", StringComparison.InvariantCultureIgnoreCase))
                    {
                        sb.Log($"Sheet {sheet.Name} saltato in quanto non inizia con IR\n");
                        continue;
                    }

                    sb.Log($"START - Import of sheet: {sheet.Name}");

                    List<RecordExcel> records = _bl.GetRows(sheet);

                    sb.Log($"Sheet {sheet.Name} has {records.Count} records");

                    var inserted = _bl.ImportRecords(records).GetAwaiter().GetResult();

                    sb.Log($"\nInserted {inserted} of {records.Count} records");

                    sb.Log($"END - Import of sheet: {sheet.Name}\n");
                }
            }

            _bl.ExportRecordInError().GetAwaiter().GetResult();
            SaveLogsToFile($"log_{DateTime.Now:ddMMyyyy_HHmm}.txt", sb.ToString());

            sb.Log("Program completed");
        }
        static void SaveLogsToFile(string filePath, string logContent)
        {
            try
            {
                using (StreamWriter writer = new StreamWriter(filePath))
                {
                    writer.Write(logContent);
                }

                Console.WriteLine($"Logs saved to {filePath} successfully.");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"An error occurred while saving logs: {ex.Message}");
            }
        }

    }
}
