﻿using AutoMapper;
using Dapper;
using Inv.Fwk.Helper.DateTimeZone;
using Invitalia.Misure.Standard2.Utilities.Extensions;
using Microsoft.Extensions.Configuration;
using OfficeOpenXml;
using OfficeOpenXml.Table.PivotTable;
using RAD4_Importer.Enums;
using RAD4_Importer.Extensions;
using RAD4_Importer.Models;
using RAD4_Importer.Utilities;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RAD4_Importer.BL
{
    public class ImporterBL
    {
        private readonly IConfigurationRoot _conf;
        private readonly IMapper _mapper;
        private readonly StringBuilder _sb;
        private readonly string _timeZoneId;

        private readonly string _insertRadioamatore;
        private readonly string _insertRadioamatoreTO;

        private readonly string _insertStazione;
        private readonly string _insertStazioneTO;

        private readonly string _insertAutorizzazione;
        private readonly string _insertAutorizzazioneTO;

        private IDbConnection _connection;
        private IDbConnection Connection
        {
            get
            {
                if (_connection == null || _connection.State != ConnectionState.Open)
                {
                    _connection = new SqlConnection(ConnectionString);
                    _connection.Open();
                }

                return _connection;
            }
        }
        private IDbTransaction _tran;

        private string ConnectionString
        {
            get
            {
                return _conf.GetConnectionString("DefaultConnection");
            }
        }

        private static List<RecordExcel> _recordsInError;
        private const string UNMATCHED_MESSAGE = "#N/A";

        public ImporterBL(IConfigurationRoot conf,
                            IMapper mapper,
                            StringBuilder sb)
        {
            _conf = conf;
            _mapper = mapper;
            _sb = sb;
            _timeZoneId = _conf.GetSection("TimeZoneId").Value;

            _insertRadioamatore = BuildInsertRadioamatore();
            _insertRadioamatoreTO = BuildInsertRadioamatoreTO();

            _insertStazione = BuildInsertStazione();
            _insertStazioneTO = BuildInsertStazioneTO();

            _insertAutorizzazione = BuildInsertAutorizzazione();
            _insertAutorizzazioneTO = BuildInsertAutorizzazioneTO();

            _recordsInError = new List<RecordExcel>();
        }

        public List<RecordExcel> GetRows(ExcelWorksheet sheet)
        {
            List<RecordExcel> records = new();

            //Iterate through each row in the sheet
            //+2 per saltare le intestazioni
            for (int row = sheet.Dimension.Start.Row + 2; row <= sheet.Dimension.End.Row; row++)
            {
                // Access data from each cell in the current row
                int colCount = 55;
                var rowData = new string[colCount];

                for (int col = 1; col <= colCount; col++)
                {
                    rowData[col - 1] = sheet.Cells[row, col].Text;
                }

                var record = MapExcelRow(rowData);
                if (!string.IsNullOrWhiteSpace(record.TipologiaRichiedente))
                {
                    records.Add(record);
                }
            }

            return records;
        }

        public RecordExcel MapExcelRow(string[] rowData)
        {
            var result = new RecordExcel();

            foreach (ColumnName columnName in Enum.GetValues(typeof(ColumnName)))
            {
                int columnIndex = (int)columnName;

                if (columnIndex >= 0 && columnIndex < rowData.Length)
                {
                    typeof(RecordExcel).GetProperty(columnName.ToString())?.SetValue(result, rowData[columnIndex].TrimSafe());
                }
            }

            return result;
        }

        public async Task<int> ImportRecords(List<RecordExcel> records)
        {
            int count = 0;

            using (Connection)
            {
                foreach (var item in records)
                {
                    try
                    {
                        if (!string.IsNullOrWhiteSpace(await GetRadioamatoreSRByCodiceNominativoAsync(item.NominativoSR)))
                        {
                            _sb.Log($" - Nominativo {item.NominativoSR} già presente");
                            continue;
                        }

                        var unmatchedProperties = GetUnmatchedProperties(item);
                        if (unmatchedProperties.Any())
                        {
                            throw new Exception($"Campi geografici non mappati: {string.Join(", ", unmatchedProperties)}");
                        }

                        item.SetEmptyStringPropertiesToNull();

                        var suffisso = $"{item.Suffisso_1.TrimSafe()}{item.Suffisso_2?.TrimSafe()}{item.Suffisso_3.TrimSafe()}";
                        var idSuffisso = await GetIdSuffissoByPrefissoAndSuffissoAsync(item.Prefisso.TrimSafe(), suffisso);

                        var radioamatore = _mapper.Map<RadioamatoreSR>(item);
                        var stazione = _mapper.Map<StazioneRipetitrice>(item);
                        var autorizzazione = _mapper.Map<AutorizzazioneSR>(item);

                        radioamatore.ID_SUFFISSO_SR = idSuffisso;

                        autorizzazione.NUMERO_AUTORIZZAZIONE = await GeneraNumeroAutorizzazioneSR();

                        using (_tran = Connection.BeginTransaction())
                        {
                            try
                            {

                                radioamatore = await InsertNominativoSRAsync(radioamatore);

                                autorizzazione.ID_RADIOAMATORE_SR = radioamatore.ID_RADIOAMATORE_SR;
                                autorizzazione.ID_STAZIONE_RIPETITRICE = await InsertStazioneRipetitriceAsync(stazione);

                                _ = await InsertAutorizzazioneSRAsync(autorizzazione);

                                _tran.Commit();
                                count++;
                            }
                            catch (Exception)
                            {
                                _tran.Rollback();
                                throw;
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        _sb.Log($" - Error: nominativo {item.NominativoSR} non inserito: {ex.Message}");
                        item.ErrorMessage = ex.Message;
                        _recordsInError.Add(item);
                        continue;
                    }
                }
            }

            return count;
        }

        private async Task<string> GeneraNumeroAutorizzazioneSR()
        {
            var seq = (await GetNextSequenceValue()).ToString();
            var anno = DateTime.Now.ToString("yyyy");
            return $"AP_{anno}_{seq}";
        }

        public async Task<long> GetNextSequenceValue()
        {
            string query = $"SELECT NEXT VALUE FOR dbo.CodiceAutorizzazioneSR";
            using (Connection)
            {
                long result = await Connection.ExecuteScalarAsync<long>(query);
                return result;
            }
        }

        private List<string> GetUnmatchedProperties(RecordExcel recordExcel)
        {
            var result = new List<string>();

            var propertiesToCheck = new List<string>()
                {
                    "CodiceComuneNascita",
                    "CodiceComuneResidenzaAssociazione",
                    "CodiceComuneResidenzaPersonaFisica",
                    "CodiceComuneStazioneRipetitrice",
                    "SiglaProvinciaResidenzaAssociazione",
                    "SiglaProvinciaResidenzaPersonaFisica",
                    "SiglaProvinciaStazioneRipetitrice",
                    "SiglaRegioneStazioneRipetitrice"
                };

            foreach (var property in propertiesToCheck)
            {
                if (typeof(RecordExcel).GetProperty(property)?.GetValue(recordExcel)?.ToString()?.Contains(UNMATCHED_MESSAGE) ?? false)
                    result.Add(property);
            }

            return result;
        }

        public async Task ExportRecordInError()
        {
            try
            {
                if (_recordsInError.Count() > 0)
                {
                    // Specify the path where you want to save the Excel file
                    string filePath = _conf.GetSection("Excel:OutputFilePath").Value;

                    // Create a new Excel package
                    using (var package = new ExcelPackage())
                    {
                        // Add a new worksheet to the Excel package
                        var worksheet = package.Workbook.Worksheets.Add("Errors");

                        var columnsNumber = 0;
                        // Add headers to the worksheet
                        foreach (ColumnName columnName in Enum.GetValues(typeof(ColumnName)))
                        {
                            worksheet.Cells[1, ((int)columnName + 1)].Value = columnName.ToString();
                            columnsNumber++;
                        }
                        worksheet.Cells[1, (columnsNumber + 1)].Value = "Error";

                        // Populate the worksheet with data from the list of objects
                        for (int i = 0; i < _recordsInError.Count; i++)
                        {
                            foreach (ColumnName columnName in Enum.GetValues(typeof(ColumnName)))
                            {
                                worksheet.Cells[i + 2, ((int)columnName + 1)].Value = typeof(RecordExcel).GetProperty(columnName.ToString())?.GetValue(_recordsInError[i]);
                            }
                            worksheet.Cells[i + 2, (columnsNumber + 1)].Value = _recordsInError[i].ErrorMessage;
                        }

                        // Save the Excel package to a file
                        await File.WriteAllBytesAsync(filePath, package.GetAsByteArray());
                    }

                    Console.WriteLine($"Excel file saved at: {filePath}");
                }
            }
            catch (Exception ex)
            {
                _sb.Log($"Exception in {nameof(ExportRecordInError)}: {ex.Message}");
            }
        }

        #region Queries

        #region StringBuilder
        private static string BuildInsertRadioamatore()
        {
            StringBuilder insertRadioamatore = new StringBuilder();

            insertRadioamatore.Append($" INSERT INTO {NomeTabella.RadioamatoriSR} ");
            insertRadioamatore.Append("(ID_SUFFISSO_SR, ");
            insertRadioamatore.Append("ID_ANAGRAFICA_ISPETTORATO, ");
            insertRadioamatore.Append("NOME, ");
            insertRadioamatore.Append("COGNOME, ");
            insertRadioamatore.Append("DENOMINAZIONE, ");
            insertRadioamatore.Append("DATA_NASCITA, ");
            insertRadioamatore.Append("CODICE_FISCALE, ");
            insertRadioamatore.Append("INDIRIZZO, ");
            insertRadioamatore.Append("REGIONE, ");
            insertRadioamatore.Append("COMUNE, ");
            insertRadioamatore.Append("PROVINCIA, ");
            insertRadioamatore.Append("CAP, ");
            insertRadioamatore.Append("CODICE_NOMINATIVO, ");
            insertRadioamatore.Append("STATO_NOMINATIVO, ");
            insertRadioamatore.Append("DATA_RILASCIO, ");
            insertRadioamatore.Append("UTENTE, ");
            insertRadioamatore.Append("DATA_AGG, ");
            insertRadioamatore.Append("PROTOCOLLO, ");
            insertRadioamatore.Append("DATA_PROTOCOLLO, ");
            insertRadioamatore.Append("NOTA, ");
            insertRadioamatore.Append("CODICE_DOMANDA_FE, ");
            insertRadioamatore.Append("DATA_CREAZIONE, ");
            insertRadioamatore.Append("PROTOCOLLO_PRESENTAZIONE_FE, ");
            insertRadioamatore.Append("DATA_PROTOCOLLO_PRESENTAZIONE_FE, ");
            insertRadioamatore.Append("TIPOLOGIA_ASSEGNATARIO, ");
            insertRadioamatore.Append("DATA_SCADENZA, ");
            insertRadioamatore.Append("CODICE_DOMANDA, ");
            insertRadioamatore.Append("PEC, ");
            insertRadioamatore.Append("EMAIL, ");
            insertRadioamatore.Append("TELEFONO) ");
            insertRadioamatore.Append($" OUTPUT INSERTED.ID_RADIOAMATORE_SR ");
            insertRadioamatore.Append(" VALUES (");

            insertRadioamatore.Append("@ID_SUFFISSO_SR, ");
            insertRadioamatore.Append("@ID_ANAGRAFICA_ISPETTORATO, ");
            insertRadioamatore.Append("@NOME, ");
            insertRadioamatore.Append("@COGNOME, ");
            insertRadioamatore.Append("@DENOMINAZIONE, ");
            insertRadioamatore.Append("@DATA_NASCITA, ");
            insertRadioamatore.Append("@CODICE_FISCALE, ");
            insertRadioamatore.Append("@INDIRIZZO, ");
            insertRadioamatore.Append("@REGIONE, ");
            insertRadioamatore.Append("@COMUNE, ");
            insertRadioamatore.Append("@PROVINCIA, ");
            insertRadioamatore.Append("@CAP, ");
            insertRadioamatore.Append("@CODICE_NOMINATIVO, ");
            insertRadioamatore.Append("@STATO_NOMINATIVO, ");
            insertRadioamatore.Append("@DATA_RILASCIO, ");
            insertRadioamatore.Append("@UTENTE, ");
            insertRadioamatore.Append("@DATA_AGG, ");
            insertRadioamatore.Append("@PROTOCOLLO, ");
            insertRadioamatore.Append("@DATA_PROTOCOLLO, ");
            insertRadioamatore.Append("@NOTA, ");
            insertRadioamatore.Append("@CODICE_DOMANDA_FE, ");
            insertRadioamatore.Append("@DATA_CREAZIONE, ");
            insertRadioamatore.Append("@PROTOCOLLO_PRESENTAZIONE_FE, ");
            insertRadioamatore.Append("@DATA_PROTOCOLLO_PRESENTAZIONE_FE, ");
            insertRadioamatore.Append("@TIPOLOGIA_ASSEGNATARIO, ");
            insertRadioamatore.Append("@DATA_SCADENZA, ");
            insertRadioamatore.Append("@CODICE_DOMANDA, ");
            insertRadioamatore.Append("@PEC, ");
            insertRadioamatore.Append("@EMAIL, ");
            insertRadioamatore.Append("@TELEFONO)");
            return insertRadioamatore.ToString();
        }
        private static string BuildInsertRadioamatoreTO()
        {
            StringBuilder insertRadioamatoreTO = new StringBuilder();

            insertRadioamatoreTO.Append($" INSERT INTO {NomeTabella.RadioamatoriSRTriggerOutput} ");
            insertRadioamatoreTO.Append("(ID_RADIOAMATORE_SR, ");
            insertRadioamatoreTO.Append("ID_SUFFISSO_SR, ");
            insertRadioamatoreTO.Append("ID_ANAGRAFICA_ISPETTORATO, ");
            insertRadioamatoreTO.Append("NOME, ");
            insertRadioamatoreTO.Append("COGNOME, ");
            insertRadioamatoreTO.Append("DENOMINAZIONE, ");
            insertRadioamatoreTO.Append("DATA_NASCITA, ");
            insertRadioamatoreTO.Append("CODICE_FISCALE, ");
            insertRadioamatoreTO.Append("INDIRIZZO, ");
            insertRadioamatoreTO.Append("REGIONE, ");
            insertRadioamatoreTO.Append("COMUNE, ");
            insertRadioamatoreTO.Append("PROVINCIA, ");
            insertRadioamatoreTO.Append("CAP, ");
            insertRadioamatoreTO.Append("CODICE_NOMINATIVO, ");
            insertRadioamatoreTO.Append("STATO_NOMINATIVO, ");
            insertRadioamatoreTO.Append("DATA_RILASCIO, ");
            insertRadioamatoreTO.Append("UTENTE, ");
            insertRadioamatoreTO.Append("DATA_AGG, ");
            insertRadioamatoreTO.Append("PROTOCOLLO, ");
            insertRadioamatoreTO.Append("DATA_PROTOCOLLO, ");
            insertRadioamatoreTO.Append("NOTA, ");
            insertRadioamatoreTO.Append("CODICE_DOMANDA_FE, ");
            insertRadioamatoreTO.Append("DATA_CREAZIONE, ");
            insertRadioamatoreTO.Append("PROTOCOLLO_PRESENTAZIONE_FE, ");
            insertRadioamatoreTO.Append("DATA_PROTOCOLLO_PRESENTAZIONE_FE, ");
            insertRadioamatoreTO.Append("TIPOLOGIA_ASSEGNATARIO, ");
            insertRadioamatoreTO.Append("DATA_SCADENZA, ");
            insertRadioamatoreTO.Append("CODICE_DOMANDA, ");
            insertRadioamatoreTO.Append("PEC, ");
            insertRadioamatoreTO.Append("EMAIL, ");
            insertRadioamatoreTO.Append("TELEFONO, ");
            insertRadioamatoreTO.Append("TIMESTAMP_MODIFICA) ");
            insertRadioamatoreTO.Append(" VALUES (");


            insertRadioamatoreTO.Append("@ID_RADIOAMATORE_SR, ");
            insertRadioamatoreTO.Append("@ID_SUFFISSO_SR, ");
            insertRadioamatoreTO.Append("@ID_ANAGRAFICA_ISPETTORATO, ");
            insertRadioamatoreTO.Append("@NOME, ");
            insertRadioamatoreTO.Append("@COGNOME, ");
            insertRadioamatoreTO.Append("@DENOMINAZIONE, ");
            insertRadioamatoreTO.Append("@DATA_NASCITA, ");
            insertRadioamatoreTO.Append("@CODICE_FISCALE, ");
            insertRadioamatoreTO.Append("@INDIRIZZO, ");
            insertRadioamatoreTO.Append("@REGIONE, ");
            insertRadioamatoreTO.Append("@COMUNE, ");
            insertRadioamatoreTO.Append("@PROVINCIA, ");
            insertRadioamatoreTO.Append("@CAP, ");
            insertRadioamatoreTO.Append("@CODICE_NOMINATIVO, ");
            insertRadioamatoreTO.Append("@STATO_NOMINATIVO, ");
            insertRadioamatoreTO.Append("@DATA_RILASCIO, ");
            insertRadioamatoreTO.Append("@UTENTE, ");
            insertRadioamatoreTO.Append("@DATA_AGG, ");
            insertRadioamatoreTO.Append("@PROTOCOLLO, ");
            insertRadioamatoreTO.Append("@DATA_PROTOCOLLO, ");
            insertRadioamatoreTO.Append("@NOTA, ");
            insertRadioamatoreTO.Append("@CODICE_DOMANDA_FE, ");
            insertRadioamatoreTO.Append("@DATA_CREAZIONE, ");
            insertRadioamatoreTO.Append("@PROTOCOLLO_PRESENTAZIONE_FE, ");
            insertRadioamatoreTO.Append("@DATA_PROTOCOLLO_PRESENTAZIONE_FE, ");
            insertRadioamatoreTO.Append("@TIPOLOGIA_ASSEGNATARIO, ");
            insertRadioamatoreTO.Append("@DATA_SCADENZA, ");
            insertRadioamatoreTO.Append("@CODICE_DOMANDA, ");
            insertRadioamatoreTO.Append("@PEC, ");
            insertRadioamatoreTO.Append("@EMAIL, ");
            insertRadioamatoreTO.Append("@TELEFONO, ");
            insertRadioamatoreTO.Append("@TIMESTAMP_MODIFICA)");

            return insertRadioamatoreTO.ToString();
        }

        private static string BuildInsertStazione()
        {
            StringBuilder insertStazione = new StringBuilder();

            insertStazione.Append($" INSERT INTO {NomeTabella.StazioniRipetitrici} (");
            insertStazione.Append("REGIONE, ");
            insertStazione.Append("CODICE_REGIONE, ");
            insertStazione.Append("PROVINCIA, ");
            insertStazione.Append("CODICE_PROVINCIA, ");
            insertStazione.Append("COMUNE, ");
            insertStazione.Append("CODICE_COMUNE, ");
            insertStazione.Append("INDIRIZZO, ");
            insertStazione.Append("CIVICO, ");
            insertStazione.Append("CAP, ");
            insertStazione.Append("ID_ANAGRAFICA_ISPETTORATO, ");
            insertStazione.Append("LATITUDINE, ");
            insertStazione.Append("LONGITUDINE, ");
            insertStazione.Append("NOME_OPERATORE, ");
            insertStazione.Append("COGNOME_OPERATORE, ");
            insertStazione.Append("CODICE_FISCALE_OPERATORE, ");
            insertStazione.Append("NOMINATIVO_OPERATORE, ");
            insertStazione.Append("REGIONE_RESIDENZA_OPERATORE, ");
            insertStazione.Append("CODICE_REGIONE_RESIDENZA_OPERATORE, ");
            insertStazione.Append("PROVINCIA_RESIDENZA_OPERATORE, ");
            insertStazione.Append("CODICE_PROVINCIA_RESIDENZA_OPERATORE, ");
            insertStazione.Append("COMUNE_RESIDENZA_OPERATORE, ");
            insertStazione.Append("CODICE_COMUNE_RESIDENZA_OPERATORE, ");
            insertStazione.Append("INDIRIZZO_RESIDENZA_OPERATORE, ");
            insertStazione.Append("NUMERO_CIVICO_RESIDENZA_OPERATORE, ");
            insertStazione.Append("CAP_RESIDENZA_OPERATORE, ");
            insertStazione.Append("EMAIL_OPERATORE, ");
            insertStazione.Append("TELEFONO_OPERATORE) ");
            insertStazione.Append(" OUTPUT INSERTED.ID_STAZIONE_RIPETITRICE ");
            insertStazione.Append(" VALUES (");

            insertStazione.Append("@REGIONE, ");
            insertStazione.Append("@CODICE_REGIONE, ");
            insertStazione.Append("@PROVINCIA, ");
            insertStazione.Append("@CODICE_PROVINCIA, ");
            insertStazione.Append("@COMUNE, ");
            insertStazione.Append("@CODICE_COMUNE, ");
            insertStazione.Append("@INDIRIZZO, ");
            insertStazione.Append("@CIVICO, ");
            insertStazione.Append("@CAP, ");
            insertStazione.Append("@ID_ANAGRAFICA_ISPETTORATO, ");
            insertStazione.Append("@LATITUDINE, ");
            insertStazione.Append("@LONGITUDINE, ");
            insertStazione.Append("@NOME_OPERATORE, ");
            insertStazione.Append("@COGNOME_OPERATORE, ");
            insertStazione.Append("@CODICE_FISCALE_OPERATORE, ");
            insertStazione.Append("@NOMINATIVO_OPERATORE, ");
            insertStazione.Append("@REGIONE_RESIDENZA_OPERATORE, ");
            insertStazione.Append("@CODICE_REGIONE_RESIDENZA_OPERATORE, ");
            insertStazione.Append("@PROVINCIA_RESIDENZA_OPERATORE, ");
            insertStazione.Append("@CODICE_PROVINCIA_RESIDENZA_OPERATORE, ");
            insertStazione.Append("@COMUNE_RESIDENZA_OPERATORE, ");
            insertStazione.Append("@CODICE_COMUNE_RESIDENZA_OPERATORE, ");
            insertStazione.Append("@INDIRIZZO_RESIDENZA_OPERATORE, ");
            insertStazione.Append("@NUMERO_CIVICO_RESIDENZA_OPERATORE, ");
            insertStazione.Append("@CAP_RESIDENZA_OPERATORE, ");
            insertStazione.Append("@EMAIL_OPERATORE, ");
            insertStazione.Append("@TELEFONO_OPERATORE) ");

            return insertStazione.ToString();
        }
        private static string BuildInsertStazioneTO()
        {
            StringBuilder insertStazioneTO = new StringBuilder();

            insertStazioneTO.Append($" INSERT INTO {NomeTabella.StazioniRipetitriciTriggerOutput} (");
            insertStazioneTO.Append("ID_STAZIONE_RIPETITRICE, ");
            insertStazioneTO.Append("REGIONE, ");
            insertStazioneTO.Append("CODICE_REGIONE, ");
            insertStazioneTO.Append("PROVINCIA, ");
            insertStazioneTO.Append("CODICE_PROVINCIA, ");
            insertStazioneTO.Append("COMUNE, ");
            insertStazioneTO.Append("CODICE_COMUNE, ");
            insertStazioneTO.Append("INDIRIZZO, ");
            insertStazioneTO.Append("CIVICO, ");
            insertStazioneTO.Append("CAP, ");
            insertStazioneTO.Append("ID_ANAGRAFICA_ISPETTORATO, ");
            insertStazioneTO.Append("LATITUDINE, ");
            insertStazioneTO.Append("LONGITUDINE, ");
            insertStazioneTO.Append("NOME_OPERATORE, ");
            insertStazioneTO.Append("COGNOME_OPERATORE, ");
            insertStazioneTO.Append("CODICE_FISCALE_OPERATORE, ");
            insertStazioneTO.Append("NOMINATIVO_OPERATORE, ");
            insertStazioneTO.Append("REGIONE_RESIDENZA_OPERATORE, ");
            insertStazioneTO.Append("CODICE_REGIONE_RESIDENZA_OPERATORE, ");
            insertStazioneTO.Append("PROVINCIA_RESIDENZA_OPERATORE, ");
            insertStazioneTO.Append("CODICE_PROVINCIA_RESIDENZA_OPERATORE, ");
            insertStazioneTO.Append("COMUNE_RESIDENZA_OPERATORE, ");
            insertStazioneTO.Append("CODICE_COMUNE_RESIDENZA_OPERATORE, ");
            insertStazioneTO.Append("INDIRIZZO_RESIDENZA_OPERATORE, ");
            insertStazioneTO.Append("NUMERO_CIVICO_RESIDENZA_OPERATORE, ");
            insertStazioneTO.Append("CAP_RESIDENZA_OPERATORE, ");
            insertStazioneTO.Append("EMAIL_OPERATORE, ");
            insertStazioneTO.Append("TELEFONO_OPERATORE, ");
            insertStazioneTO.Append("TIMESTAMP_MODIFICA)");
            insertStazioneTO.Append(" VALUES (");

            insertStazioneTO.Append("@ID_STAZIONE_RIPETITRICE, ");
            insertStazioneTO.Append("@REGIONE, ");
            insertStazioneTO.Append("@CODICE_REGIONE, ");
            insertStazioneTO.Append("@PROVINCIA, ");
            insertStazioneTO.Append("@CODICE_PROVINCIA, ");
            insertStazioneTO.Append("@COMUNE, ");
            insertStazioneTO.Append("@CODICE_COMUNE, ");
            insertStazioneTO.Append("@INDIRIZZO, ");
            insertStazioneTO.Append("@CIVICO, ");
            insertStazioneTO.Append("@CAP, ");
            insertStazioneTO.Append("@ID_ANAGRAFICA_ISPETTORATO, ");
            insertStazioneTO.Append("@LATITUDINE, ");
            insertStazioneTO.Append("@LONGITUDINE, ");
            insertStazioneTO.Append("@NOME_OPERATORE, ");
            insertStazioneTO.Append("@COGNOME_OPERATORE, ");
            insertStazioneTO.Append("@CODICE_FISCALE_OPERATORE, ");
            insertStazioneTO.Append("@NOMINATIVO_OPERATORE, ");
            insertStazioneTO.Append("@REGIONE_RESIDENZA_OPERATORE, ");
            insertStazioneTO.Append("@CODICE_REGIONE_RESIDENZA_OPERATORE, ");
            insertStazioneTO.Append("@PROVINCIA_RESIDENZA_OPERATORE, ");
            insertStazioneTO.Append("@CODICE_PROVINCIA_RESIDENZA_OPERATORE, ");
            insertStazioneTO.Append("@COMUNE_RESIDENZA_OPERATORE, ");
            insertStazioneTO.Append("@CODICE_COMUNE_RESIDENZA_OPERATORE, ");
            insertStazioneTO.Append("@INDIRIZZO_RESIDENZA_OPERATORE, ");
            insertStazioneTO.Append("@NUMERO_CIVICO_RESIDENZA_OPERATORE, ");
            insertStazioneTO.Append("@CAP_RESIDENZA_OPERATORE, ");
            insertStazioneTO.Append("@EMAIL_OPERATORE, ");
            insertStazioneTO.Append("@TELEFONO_OPERATORE, ");
            insertStazioneTO.Append("@TIMESTAMP_MODIFICA)");

            return insertStazioneTO.ToString();
        }

        private static string BuildInsertAutorizzazione()
        {
            StringBuilder insertAutorizzazione = new StringBuilder();

            insertAutorizzazione.Append($" INSERT INTO {NomeTabella.AutorizzazioniSR} (");
            insertAutorizzazione.Append("CODICE_DOMANDA, ");
            insertAutorizzazione.Append("COGNOME, ");
            insertAutorizzazione.Append("NOME, ");
            insertAutorizzazione.Append("DENOMINAZIONE, ");
            insertAutorizzazione.Append("CODICE_FISCALE, ");
            insertAutorizzazione.Append("SESSO, ");
            insertAutorizzazione.Append("LUOGO_NASCITA, ");
            insertAutorizzazione.Append("CODICE_LUOGO_NASCITA, ");
            insertAutorizzazione.Append("DATA_NASCITA, ");
            insertAutorizzazione.Append("REGIONE_RESIDENZA, ");
            insertAutorizzazione.Append("CODICE_REGIONE_RESIDENZA, ");
            insertAutorizzazione.Append("PROVINCIA_RESIDENZA, ");
            insertAutorizzazione.Append("CODICE_PROVINCIA_RESIDENZA, ");
            insertAutorizzazione.Append("COMUNE_RESIDENZA, ");
            insertAutorizzazione.Append("CODICE_COMUNE_RESIDENZA, ");
            insertAutorizzazione.Append("INDIRIZZO_RESIDENZA, ");
            insertAutorizzazione.Append("CAP_RESIDENZA, ");
            insertAutorizzazione.Append("ID_ANAGRAFICA_ISPETTORATO, ");
            insertAutorizzazione.Append("PEC, ");
            insertAutorizzazione.Append("EMAIL, ");
            insertAutorizzazione.Append("TELEFONO, ");
            insertAutorizzazione.Append("ID_TIPO_ASSEGNATARIO, ");
            insertAutorizzazione.Append("RINNOVO, ");
            insertAutorizzazione.Append("ID_AUTORIZZAZIONE_RINNOVATA, ");
            insertAutorizzazione.Append("NUMERO_AUTORIZZAZIONE, ");
            insertAutorizzazione.Append("ID_TIPO_AUTORIZZAZIONE, ");
            insertAutorizzazione.Append("ID_STATO_AUTORIZZAZIONE, ");
            insertAutorizzazione.Append("DATA_RILASCIO, ");
            insertAutorizzazione.Append("DATA_SCADENZA, ");
            insertAutorizzazione.Append("ID_RADIOAMATORE_SR, ");
            insertAutorizzazione.Append("CODICE_PROTOCOLLO_ENTRATA, ");
            insertAutorizzazione.Append("DATA_PROTOCOLLO_ENTRATA, ");
            insertAutorizzazione.Append("CODICE_PROTOCOLLO_USCITA, ");
            insertAutorizzazione.Append("DATA_PROTOCOLLO_USCITA, ");
            insertAutorizzazione.Append("UTENTE, ");
            insertAutorizzazione.Append("ID_STAZIONE_RIPETITRICE)");
            insertAutorizzazione.Append(" OUTPUT INSERTED.ID_AUTORIZZAZIONE_SR ");
            insertAutorizzazione.Append(" VALUES ( ");

            insertAutorizzazione.Append("@CODICE_DOMANDA, ");
            insertAutorizzazione.Append("@COGNOME, ");
            insertAutorizzazione.Append("@NOME, ");
            insertAutorizzazione.Append("@DENOMINAZIONE, ");
            insertAutorizzazione.Append("@CODICE_FISCALE, ");
            insertAutorizzazione.Append("@SESSO, ");
            insertAutorizzazione.Append("@LUOGO_NASCITA, ");
            insertAutorizzazione.Append("@CODICE_LUOGO_NASCITA, ");
            insertAutorizzazione.Append("@DATA_NASCITA, ");
            insertAutorizzazione.Append("@REGIONE_RESIDENZA, ");
            insertAutorizzazione.Append("@CODICE_REGIONE_RESIDENZA, ");
            insertAutorizzazione.Append("@PROVINCIA_RESIDENZA, ");
            insertAutorizzazione.Append("@CODICE_PROVINCIA_RESIDENZA, ");
            insertAutorizzazione.Append("@COMUNE_RESIDENZA, ");
            insertAutorizzazione.Append("@CODICE_COMUNE_RESIDENZA, ");
            insertAutorizzazione.Append("@INDIRIZZO_RESIDENZA, ");
            insertAutorizzazione.Append("@CAP_RESIDENZA, ");
            insertAutorizzazione.Append("@ID_ANAGRAFICA_ISPETTORATO, ");
            insertAutorizzazione.Append("@PEC, ");
            insertAutorizzazione.Append("@EMAIL, ");
            insertAutorizzazione.Append("@TELEFONO, ");
            insertAutorizzazione.Append("@ID_TIPO_ASSEGNATARIO, ");
            insertAutorizzazione.Append("@RINNOVO, ");
            insertAutorizzazione.Append("@ID_AUTORIZZAZIONE_RINNOVATA, ");
            insertAutorizzazione.Append("@NUMERO_AUTORIZZAZIONE, ");
            insertAutorizzazione.Append("@ID_TIPO_AUTORIZZAZIONE, ");
            insertAutorizzazione.Append("@ID_STATO_AUTORIZZAZIONE, ");
            insertAutorizzazione.Append("@DATA_RILASCIO, ");
            insertAutorizzazione.Append("@DATA_SCADENZA, ");
            insertAutorizzazione.Append("@ID_RADIOAMATORE_SR, ");
            insertAutorizzazione.Append("@CODICE_PROTOCOLLO_ENTRATA, ");
            insertAutorizzazione.Append("@DATA_PROTOCOLLO_ENTRATA, ");
            insertAutorizzazione.Append("@CODICE_PROTOCOLLO_USCITA, ");
            insertAutorizzazione.Append("@DATA_PROTOCOLLO_USCITA, ");
            insertAutorizzazione.Append("@UTENTE, ");
            insertAutorizzazione.Append("@ID_STAZIONE_RIPETITRICE) ");

            return insertAutorizzazione.ToString();
        }
        private static string BuildInsertAutorizzazioneTO()
        {
            StringBuilder insertAutorizzazioneTO = new StringBuilder();

            insertAutorizzazioneTO.Append($" INSERT INTO {NomeTabella.AutorizzazioniSRTriggerOutput} (");
            insertAutorizzazioneTO.Append("ID_AUTORIZZAZIONE_SR, ");
            insertAutorizzazioneTO.Append("CODICE_DOMANDA, ");
            insertAutorizzazioneTO.Append("COGNOME, ");
            insertAutorizzazioneTO.Append("NOME, ");
            insertAutorizzazioneTO.Append("DENOMINAZIONE, ");
            insertAutorizzazioneTO.Append("CODICE_FISCALE, ");
            insertAutorizzazioneTO.Append("SESSO, ");
            insertAutorizzazioneTO.Append("LUOGO_NASCITA, ");
            insertAutorizzazioneTO.Append("CODICE_LUOGO_NASCITA, ");
            insertAutorizzazioneTO.Append("DATA_NASCITA, ");
            insertAutorizzazioneTO.Append("REGIONE_RESIDENZA, ");
            insertAutorizzazioneTO.Append("CODICE_REGIONE_RESIDENZA, ");
            insertAutorizzazioneTO.Append("PROVINCIA_RESIDENZA, ");
            insertAutorizzazioneTO.Append("CODICE_PROVINCIA_RESIDENZA, ");
            insertAutorizzazioneTO.Append("COMUNE_RESIDENZA, ");
            insertAutorizzazioneTO.Append("CODICE_COMUNE_RESIDENZA, ");
            insertAutorizzazioneTO.Append("INDIRIZZO_RESIDENZA, ");
            insertAutorizzazioneTO.Append("CAP_RESIDENZA, ");
            insertAutorizzazioneTO.Append("ID_ANAGRAFICA_ISPETTORATO, ");
            insertAutorizzazioneTO.Append("PEC, ");
            insertAutorizzazioneTO.Append("EMAIL, ");
            insertAutorizzazioneTO.Append("TELEFONO, ");
            insertAutorizzazioneTO.Append("ID_TIPO_ASSEGNATARIO, ");
            insertAutorizzazioneTO.Append("RINNOVO, ");
            insertAutorizzazioneTO.Append("ID_AUTORIZZAZIONE_RINNOVATA, ");
            insertAutorizzazioneTO.Append("NUMERO_AUTORIZZAZIONE, ");
            insertAutorizzazioneTO.Append("ID_TIPO_AUTORIZZAZIONE, ");
            insertAutorizzazioneTO.Append("ID_STATO_AUTORIZZAZIONE, ");
            insertAutorizzazioneTO.Append("DATA_RILASCIO, ");
            insertAutorizzazioneTO.Append("DATA_SCADENZA, ");
            insertAutorizzazioneTO.Append("ID_RADIOAMATORE_SR, ");
            insertAutorizzazioneTO.Append("CODICE_PROTOCOLLO_ENTRATA, ");
            insertAutorizzazioneTO.Append("DATA_PROTOCOLLO_ENTRATA, ");
            insertAutorizzazioneTO.Append("CODICE_PROTOCOLLO_USCITA, ");
            insertAutorizzazioneTO.Append("DATA_PROTOCOLLO_USCITA, ");
            insertAutorizzazioneTO.Append("UTENTE, ");
            insertAutorizzazioneTO.Append("ID_STAZIONE_RIPETITRICE, ");
            insertAutorizzazioneTO.Append("TIMESTAMP_MODIFICA)");

            insertAutorizzazioneTO.Append(" VALUES ( ");


            insertAutorizzazioneTO.Append("@ID_AUTORIZZAZIONE_SR, ");
            insertAutorizzazioneTO.Append("@CODICE_DOMANDA, ");
            insertAutorizzazioneTO.Append("@COGNOME, ");
            insertAutorizzazioneTO.Append("@NOME, ");
            insertAutorizzazioneTO.Append("@DENOMINAZIONE, ");
            insertAutorizzazioneTO.Append("@CODICE_FISCALE, ");
            insertAutorizzazioneTO.Append("@SESSO, ");
            insertAutorizzazioneTO.Append("@LUOGO_NASCITA, ");
            insertAutorizzazioneTO.Append("@CODICE_LUOGO_NASCITA, ");
            insertAutorizzazioneTO.Append("@DATA_NASCITA, ");
            insertAutorizzazioneTO.Append("@REGIONE_RESIDENZA, ");
            insertAutorizzazioneTO.Append("@CODICE_REGIONE_RESIDENZA, ");
            insertAutorizzazioneTO.Append("@PROVINCIA_RESIDENZA, ");
            insertAutorizzazioneTO.Append("@CODICE_PROVINCIA_RESIDENZA, ");
            insertAutorizzazioneTO.Append("@COMUNE_RESIDENZA, ");
            insertAutorizzazioneTO.Append("@CODICE_COMUNE_RESIDENZA, ");
            insertAutorizzazioneTO.Append("@INDIRIZZO_RESIDENZA, ");
            insertAutorizzazioneTO.Append("@CAP_RESIDENZA, ");
            insertAutorizzazioneTO.Append("@ID_ANAGRAFICA_ISPETTORATO, ");
            insertAutorizzazioneTO.Append("@PEC, ");
            insertAutorizzazioneTO.Append("@EMAIL, ");
            insertAutorizzazioneTO.Append("@TELEFONO, ");
            insertAutorizzazioneTO.Append("@ID_TIPO_ASSEGNATARIO, ");
            insertAutorizzazioneTO.Append("@RINNOVO, ");
            insertAutorizzazioneTO.Append("@ID_AUTORIZZAZIONE_RINNOVATA, ");
            insertAutorizzazioneTO.Append("@NUMERO_AUTORIZZAZIONE, ");
            insertAutorizzazioneTO.Append("@ID_TIPO_AUTORIZZAZIONE, ");
            insertAutorizzazioneTO.Append("@ID_STATO_AUTORIZZAZIONE, ");
            insertAutorizzazioneTO.Append("@DATA_RILASCIO, ");
            insertAutorizzazioneTO.Append("@DATA_SCADENZA, ");
            insertAutorizzazioneTO.Append("@ID_RADIOAMATORE_SR, ");
            insertAutorizzazioneTO.Append("@CODICE_PROTOCOLLO_ENTRATA, ");
            insertAutorizzazioneTO.Append("@DATA_PROTOCOLLO_ENTRATA, ");
            insertAutorizzazioneTO.Append("@CODICE_PROTOCOLLO_USCITA, ");
            insertAutorizzazioneTO.Append("@DATA_PROTOCOLLO_USCITA, ");
            insertAutorizzazioneTO.Append("@UTENTE, ");
            insertAutorizzazioneTO.Append("@ID_STAZIONE_RIPETITRICE, ");
            insertAutorizzazioneTO.Append("@TIMESTAMP_MODIFICA)");

            return insertAutorizzazioneTO.ToString();
        }

        #endregion

        #region TB_ANAGRAFICA_ISPETTORATI

        public async Task<List<AnagraficaIspettorato>> GetAnagraficaIspettoratiAsync()
        {
            var query = new StringBuilder();
            query.Append($" SELECT * FROM {NomeTabella.AnagraficaIspettorati} ORDER BY {nameof(AnagraficaIspettorato.ORDINE)}");

            using (Connection)
            {
                return (await Connection.QueryAsync<AnagraficaIspettorato>(query.ToString())).ToList();
            }
        }

        #endregion

        #region TB_SUFFISSO_SR

        public async Task<int> GetIdSuffissoByPrefissoAndSuffissoAsync(string prefisso, string suffisso)
        {
            var query = new StringBuilder();
            query.Append($"SELECT TOP 1 {nameof(SuffissoSR.ID_SUFFISSO_SR)} from {NomeTabella.SuffissoSR} WHERE {nameof(SuffissoSR.PREFISSO)} = @prefisso AND {nameof(SuffissoSR.SUFFISSO)} = @suffisso ");
            return (await Connection.QueryAsync<int>(query.ToString(), new { prefisso, suffisso })).FirstOrDefault();
        }

        public async Task ImpegnaSuffissoSRAsync(int idSuffisso)
        {
            var query = new StringBuilder();
            query.Append($"UPDATE {NomeTabella.SuffissoSR} SET DISPONIBILE = @disponibile WHERE ID_SUFFISSO_SR = @id");
            await Connection.ExecuteScalarAsync<int>(query.ToString(), new { id = idSuffisso, disponibile = false }, _tran);
            return;
        }

        #endregion

        #region TB_RADIOAMATORE_SR

        public async Task<string> GetRadioamatoreSRByCodiceNominativoAsync(string codiceNominativo)
        {
            var query = new StringBuilder();

            query.Append($" SELECT r.{nameof(RadioamatoreSR.CODICE_NOMINATIVO)} ");
            query.Append($" FROM {NomeTabella.RadioamatoriSR} as r ");
            query.Append($" WHERE r.{nameof(RadioamatoreSR.CODICE_NOMINATIVO)} = @CodiceNominativo");

            return (await Connection.QueryAsync<string>(query.ToString(),
                        param: new
                        {
                            CodiceNominativo = codiceNominativo
                        }
                    )).FirstOrDefault();
        }

        public async Task<RadioamatoreSR> InsertNominativoSRAsync(RadioamatoreSR entity)
        {
            var now = DateTimeHelper.GetZoneDateTimeNow(_timeZoneId);

            var param = new
            {
                entity.ID_SUFFISSO_SR,
                entity.ID_ANAGRAFICA_ISPETTORATO,
                entity.NOME,
                entity.COGNOME,
                entity.DENOMINAZIONE,
                entity.DATA_NASCITA,
                entity.CODICE_FISCALE,
                entity.INDIRIZZO,
                entity.REGIONE,
                entity.COMUNE,
                entity.PROVINCIA,
                entity.CAP,
                entity.CODICE_NOMINATIVO,
                entity.STATO_NOMINATIVO,
                entity.DATA_RILASCIO,
                entity.UTENTE,
                entity.DATA_AGG,
                entity.PROTOCOLLO,
                entity.DATA_PROTOCOLLO,
                entity.NOTA,
                entity.CODICE_DOMANDA_FE,
                DATA_CREAZIONE = now,
                entity.PROTOCOLLO_PRESENTAZIONE_FE,
                entity.DATA_PROTOCOLLO_PRESENTAZIONE_FE,
                entity.TIPOLOGIA_ASSEGNATARIO,
                entity.DATA_SCADENZA,
                entity.CODICE_DOMANDA,
                entity.PEC,
                entity.EMAIL,
                entity.TELEFONO
            };

            var idNominativo = await Connection.QuerySingleAsync<int>(_insertRadioamatore, param, _tran);
            if (entity.ID_SUFFISSO_SR.HasValue)
            {
                await ImpegnaSuffissoSRAsync(entity.ID_SUFFISSO_SR.Value);
            }

            var paramTO = new
            {
                ID_RADIOAMATORE_SR = idNominativo,
                entity.ID_SUFFISSO_SR,
                entity.ID_ANAGRAFICA_ISPETTORATO,
                entity.NOME,
                entity.COGNOME,
                entity.DENOMINAZIONE,
                entity.DATA_NASCITA,
                entity.CODICE_FISCALE,
                entity.INDIRIZZO,
                entity.REGIONE,
                entity.COMUNE,
                entity.PROVINCIA,
                entity.CAP,
                entity.CODICE_NOMINATIVO,
                entity.STATO_NOMINATIVO,
                entity.DATA_RILASCIO,
                entity.UTENTE,
                entity.DATA_AGG,
                entity.PROTOCOLLO,
                entity.DATA_PROTOCOLLO,
                entity.NOTA,
                entity.CODICE_DOMANDA_FE,
                DATA_CREAZIONE = now,
                entity.PROTOCOLLO_PRESENTAZIONE_FE,
                entity.DATA_PROTOCOLLO_PRESENTAZIONE_FE,
                entity.TIPOLOGIA_ASSEGNATARIO,
                entity.DATA_SCADENZA,
                entity.CODICE_DOMANDA,
                entity.PEC,
                entity.EMAIL,
                entity.TELEFONO,
                TIMESTAMP_MODIFICA = DateTime.Now
            };
            await Connection.ExecuteScalarAsync(_insertRadioamatoreTO, paramTO, _tran);

            entity.ID_RADIOAMATORE_SR = idNominativo;
            return entity;
        }

        #endregion

        #region TB_STAZIONI_RIPETITRICI

        public async Task<int> InsertStazioneRipetitriceAsync(StazioneRipetitrice entity)
        {
            int idStazioneRipetitrice = await Connection.ExecuteScalarAsync<int>(_insertStazione, entity, _tran);

            entity.ID_STAZIONE_RIPETITRICE = idStazioneRipetitrice;
            entity.TIMESTAMP_MODIFICA = DateTime.Now;

            await Connection.ExecuteScalarAsync(_insertStazioneTO.ToString(), entity, _tran);

            return idStazioneRipetitrice;
        }


        #endregion

        #region TB_AUTORIZZAZIONI_SR

        public async Task<int> InsertAutorizzazioneSRAsync(AutorizzazioneSR entity)
        {
            int idAutorizzazione = await Connection.ExecuteScalarAsync<int>(_insertAutorizzazione, entity, _tran);

            entity.ID_AUTORIZZAZIONE_SR = idAutorizzazione;
            entity.TIMESTAMP_MODIFICA = DateTime.Now;

            await Connection.ExecuteScalarAsync(_insertAutorizzazioneTO, entity, _tran);

            return idAutorizzazione;
        }

        #endregion

        #endregion
    }
}
