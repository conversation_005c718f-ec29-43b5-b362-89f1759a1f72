﻿namespace RAD4_Importer.Utilities
{
    public class NomeTabella
    {
        //DEC
        public const string AnagraficaContest = "dec.TB_ANAGRAFICA_CONTEST";
        public const string AnagraficaIspettorati = "dec.TB_ANAGRAFICA_ISPETTORATI";
        public const string CodiciZone = "dec.TB_CODICI_ZONE";
        public const string IspettoratiRegioni = "dec.TB_ISPETTORATI_REGIONE";
        public const string PrefissoNominativoSpeciale = "dec.TB_PREFISSI_SPECIALI";
        public const string PrefissoSR = "dec.TB_PREFISSI_SR";
        public const string Regioni = "dec.TB_REGIONI";
        public const string StatiAutorizzazione = "dec.TB_STATI_AUTORIZZAZIONE";
        public const string StatiIscrizione = "dec.TB_STATI_ISCRIZIONE_SWL";
        public const string StatiNominativo = "dec.TB_STATI_NOMINATIVO";
        public const string StatiPatente = "dec.TB_STATI_PATENTE";
        public const string Suffissi = "dbo.TB_SUFFISSI";
        public const string TipoAssegnatario = "dec.TB_TIPO_ASSEGNATARIO";
        public const string TipoAutorizzazione = "dec.TB_TIPO_AUTORIZZAZIONE";
        public const string TipoManifestazione = "dec.TB_TIPO_MANIFESTAZIONE";
        public const string TipiNominativo = "dec.TB_TIPI_NOMINATIVO";
        public const string ZoneMapping = "dec.TB_ZONE_MAPPING";

        //DBO
        public const string Autorizzazioni = "dbo.TB_AUTORIZZAZIONI";
        public const string AutorizzazioniTriggerOutput = "dbo.TB_AUTORIZZAZIONI_TRIGGER_OUTPUT";
        public const string AutorizzazioniSR = "dbo.TB_AUTORIZZAZIONI_SR";
        public const string AutorizzazioniSRTriggerOutput = "dbo.TB_AUTORIZZAZIONI_SR_TRIGGER_OUTPUT";
        public const string Contest = "dbo.TB_CONTEST";
        public const string IscrizioniSWL = "dbo.TB_ISCRIZIONI_SWL";
        public const string IscrizioniSWLTriggerOutput = "dbo.TB_ISCRIZIONI_SWL_TRIGGER_OUTPUT";
        public const string Manifestazione = "dbo.TB_MANIFESTAZIONI";
        public const string Patenti = "dbo.TB_PATENTI";
        public const string Radioamatori = "dbo.TB_RADIOAMATORI";
        public const string RadioamatoriTriggerOutput = "dbo.TB_RADIOAMATORI_TRIGGER_OUTPUT";
        public const string RadioamatoriSR = "dbo.TB_RADIOAMATORI_SR";
        public const string RadioamatoriSRTriggerOutput = "dbo.TB_RADIOAMATORI_SR_TRIGGER_OUTPUT";
        public const string StazioniRipetitrici = "dbo.TB_STAZIONI_RIPETITRICI";
        public const string StazioniRipetitriciTriggerOutput = "dbo.TB_STAZIONI_RIPETITRICI_TRIGGER_OUTPUT";
        public const string SuffissoSR = "dbo.TB_SUFFISSI_SR";

    }
}
