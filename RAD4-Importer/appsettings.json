{
  "Excel": {
    "InputFilePath": "C:\\RAD4_Import\\input.xlsx",
    "OutputFilePath": "C:\\RAD4_Import\\output.xlsx"
  },
  "ConnectionStrings": {
    // Dev
    //"DefaultConnection": "Server=tcp:invsqlsvi001.database.windows.net,1433;Initial Catalog=RadioamatoriDgat-Dev;Persist Security Info=False;User ID=radioamatori_col;Password=****************;MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;"

    // Coll
    //"DefaultConnection": "Server=tcp:invsqlsvi001.database.windows.net,1433;Initial Catalog=RadioamatoriDgat-Col;Persist Security Info=False;User ID=radioamatori_col;Password=****************;MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;"

    //Test import
    "DefaultConnection": "Server=tcp:invsqlsvi001.database.windows.net,1433;Initial Catalog=RadioamatoriDgat-Col_20231211T1406Z;Persist Security Info=False;User ID=radioamatori_col;Password=****************;MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;"
  },
  "TimeZoneId": "Central Europe Standard Time" // CET per Docker/Linux
}
