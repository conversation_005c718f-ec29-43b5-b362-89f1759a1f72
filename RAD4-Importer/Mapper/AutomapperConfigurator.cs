﻿using AutoMapper;
using RAD4_Importer.Models;
using System;

namespace RAD4_Importer.Mapper
{
    public static class AutomapperConfigurator
    {
        public static IMapper GetMapper()
        {
            var config = new MapperConfiguration(cfg =>
            {
                cfg.CreateMap<RecordExcel, RadioamatoreSR>()
                    .ForMember(dest => dest.NOME, opt => opt.MapFrom(src => src.IsMaggiorenne ? src.NomeRadioamatore : (src.IsMinorenne ? src.NomeMinorenne : null)))
                    .ForMember(dest => dest.COGNOME, opt => opt.MapFrom(src => src.IsMaggiorenne ? src.CognomeRadioamatore : (src.IsMinorenne ? src.CognomeMinorenne : null)))
                    .ForMember(dest => dest.DENOMINAZIONE, opt => opt.MapFrom(src => src.IsAssociazione ? src.DenominazioneAssociazione : null))
                    .ForMember(dest => dest.DATA_NASCITA, opt => opt.MapFrom(src => src.IsMaggiorenne ? src.DataNascitaDateTime : (DateTime?)null))
                    .ForMember(dest => dest.CODICE_FISCALE, opt => opt.MapFrom(src => src.IsAssociazione ? src.CF_PartitaIVA_Associazione : (src.IsMaggiorenne ? src.CodiceFiscale : null)))
                    .ForMember(dest => dest.INDIRIZZO, opt => opt.MapFrom(src => src.IsAssociazione ? src.IndirizzoSedeAssociazione : (src.IsMaggiorenne ? src.IndirizzoResidenza : null)))
                    .ForMember(dest => dest.REGIONE, opt => opt.MapFrom(src => src.IsAssociazione ? src.RegioneSedeAssociazione : (src.IsMaggiorenne ? src.RegioneResidenza : null)))
                    .ForMember(dest => dest.COMUNE, opt => opt.MapFrom(src => src.IsAssociazione ? src.ComuneSedeAssociazione : (src.IsMaggiorenne ? src.ComuneResidenza : null)))
                    .ForMember(dest => dest.PROVINCIA, opt => opt.MapFrom(src => src.IsAssociazione ? src.ProvinciaSedeAssociazione : (src.IsMaggiorenne ? src.ProvinciaResidenza : null)))
                    .ForMember(dest => dest.CAP, opt => opt.MapFrom(src => src.IsAssociazione ? src.CAP_SedeAssociazione : null))
                    .ForMember(dest => dest.CODICE_NOMINATIVO, opt => opt.MapFrom(src => src.NominativoSR))
                    .ForMember(dest => dest.STATO_NOMINATIVO, opt => opt.MapFrom(src => "ATTIVO"))
                    .ForMember(dest => dest.DATA_RILASCIO, opt => opt.MapFrom(src => src.DataProtocolloDateTime))
                    .ForMember(dest => dest.UTENTE, opt => opt.MapFrom(src => "IMPORT"))
                    .ForMember(dest => dest.PROTOCOLLO_PRESENTAZIONE_FE, opt => opt.MapFrom(src => src.ProtocolloRichiesta))
                    .ForMember(dest => dest.DATA_PROTOCOLLO_PRESENTAZIONE_FE, opt => opt.MapFrom(src => src.DataRichiestaDateTime))
                    .ForMember(dest => dest.PROTOCOLLO, opt => opt.MapFrom(src => src.Protocollo))
                    .ForMember(dest => dest.DATA_PROTOCOLLO, opt => opt.MapFrom(src => src.DataProtocolloDateTime))
                    .ForMember(dest => dest.TIPOLOGIA_ASSEGNATARIO, opt => opt.MapFrom(src => src.IsMaggiorenne ? "RADIOAMATORE" : (src.IsMinorenne ? "RADIOAMATORE MINORENNE" : "ASSOCIAZIONI O SEZIONI DELLE ASSOCIAZIONI DEI RADIOAMATORI LEGALMENTE COSTITUITE DI CUI ALL’ART. 143, COMMA 1, LETTERA D) DEL CDC")))
                    .ForMember(dest => dest.DATA_SCADENZA, opt => opt.MapFrom(src => src.DataRichiestaDateTime.HasValue ? new DateTime(src.DataRichiestaDateTime.Value.Year, 12, 31).AddYears(10) : (src.DataProtocolloDateTime.HasValue ? new DateTime(src.DataProtocolloDateTime.Value.Year, 12, 31).AddYears(10) : (DateTime?)null)))
                    .ForMember(dest => dest.PEC, opt => opt.MapFrom(src => src.Email_PEC))
                    .ForMember(dest => dest.EMAIL, opt => opt.MapFrom(src => src.Email_PEC))
                    .ForMember(dest => dest.ID_ANAGRAFICA_ISPETTORATO, opt => opt.MapFrom(src => src.Ispettorato));

                cfg.CreateMap<RecordExcel, StazioneRipetitrice>()
                    .ForMember(dest => dest.REGIONE, opt => opt.MapFrom(src => src.RegioneImpianto))
                    .ForMember(dest => dest.CODICE_REGIONE, opt => opt.MapFrom(src => src.SiglaRegioneStazioneRipetitrice))
                    .ForMember(dest => dest.PROVINCIA, opt => opt.MapFrom(src => src.ProvinciaImpianto))
                    .ForMember(dest => dest.CODICE_PROVINCIA, opt => opt.MapFrom(src => src.SiglaProvinciaStazioneRipetitrice))
                    .ForMember(dest => dest.COMUNE, opt => opt.MapFrom(src => src.ComuneImpianto))
                    .ForMember(dest => dest.CODICE_COMUNE, opt => opt.MapFrom(src => src.CodiceComuneStazioneRipetitrice))
                    .ForMember(dest => dest.INDIRIZZO, opt => opt.MapFrom(src => src.IndirizzoImpianto))
                    .ForMember(dest => dest.NOME_OPERATORE, opt => opt.MapFrom(src => src.IsMaggiorenne ? src.NomeRadioamatore : (src.IsMinorenne ? src.NomeMinorenne : src.NomeOperatore)))
                    .ForMember(dest => dest.COGNOME_OPERATORE, opt => opt.MapFrom(src => src.IsMaggiorenne ? src.CognomeRadioamatore : (src.IsMinorenne ? src.CognomeMinorenne : src.CognomeOperatore)))
                    .ForMember(dest => dest.INDIRIZZO_RESIDENZA_OPERATORE, opt => opt.MapFrom(src => src.IsMaggiorenne ? src.IndirizzoResidenza : null))
                    .ForMember(dest => dest.REGIONE_RESIDENZA_OPERATORE, opt => opt.MapFrom(src => src.IsMaggiorenne ? src.RegioneResidenza : null))
                    .ForMember(dest => dest.CODICE_REGIONE_RESIDENZA_OPERATORE, opt => opt.MapFrom(src => src.IsMaggiorenne? src.SiglaRegioneResidenzaPersonaFisica : null))
                    .ForMember(dest => dest.COMUNE_RESIDENZA_OPERATORE, opt => opt.MapFrom(src => src.IsMaggiorenne ? src.ComuneResidenza : null))
                    .ForMember(dest => dest.CODICE_COMUNE_RESIDENZA_OPERATORE, opt => opt.MapFrom(src => src.IsMaggiorenne ? src.CodiceComuneResidenzaPersonaFisica : null))
                    .ForMember(dest => dest.PROVINCIA_RESIDENZA_OPERATORE, opt => opt.MapFrom(src => src.IsMaggiorenne ? src.ProvinciaResidenza : null))
                    .ForMember(dest => dest.CODICE_PROVINCIA_RESIDENZA_OPERATORE, opt => opt.MapFrom(src => src.IsMaggiorenne ? src.SiglaRegioneResidenzaPersonaFisica : null))
                    .ForMember(dest => dest.ID_ANAGRAFICA_ISPETTORATO, opt => opt.MapFrom(src => src.Ispettorato));

                cfg.CreateMap<RecordExcel, AutorizzazioneSR>()
                    .ForMember(dest => dest.NOME, opt => opt.MapFrom(src => src.IsMaggiorenne ? src.NomeRadioamatore : (src.IsMinorenne ? src.NomeMinorenne : null)))
                    .ForMember(dest => dest.COGNOME, opt => opt.MapFrom(src => src.IsMaggiorenne ? src.CognomeRadioamatore : (src.IsMinorenne ? src.CognomeMinorenne : null)))
                    .ForMember(dest => dest.DENOMINAZIONE, opt => opt.MapFrom(src => src.IsAssociazione ? src.DenominazioneAssociazione : null))
                    .ForMember(dest => dest.CODICE_FISCALE, opt => opt.MapFrom(src => src.IsAssociazione ? src.CF_PartitaIVA_Associazione : (src.IsMaggiorenne ? src.CodiceFiscale : null)))
                    .ForMember(dest => dest.LUOGO_NASCITA, opt => opt.MapFrom(src => src.IsMaggiorenne ? src.ComuneNascita : null))
                    .ForMember(dest => dest.CODICE_LUOGO_NASCITA, opt => opt.MapFrom(src => src.IsMaggiorenne ? src.CodiceComuneNascita : null))
                    .ForMember(dest => dest.DATA_NASCITA, opt => opt.MapFrom(src => src.IsMaggiorenne ? src.DataNascitaDateTime : (DateTime?)null))
                    .ForMember(dest => dest.CODICE_FISCALE, opt => opt.MapFrom(src => src.IsAssociazione ? src.CF_PartitaIVA_Associazione : (src.IsMaggiorenne ? src.CodiceFiscale : null)))
                    .ForMember(dest => dest.INDIRIZZO_RESIDENZA, opt => opt.MapFrom(src => src.IsAssociazione ? src.IndirizzoSedeAssociazione : (src.IsMaggiorenne ? src.IndirizzoResidenza : null)))
                    .ForMember(dest => dest.REGIONE_RESIDENZA, opt => opt.MapFrom(src => src.IsAssociazione ? src.RegioneSedeAssociazione : (src.IsMaggiorenne ? src.RegioneResidenza : null)))
                    .ForMember(dest => dest.CODICE_REGIONE_RESIDENZA, opt => opt.MapFrom(src => src.IsAssociazione ? src.SiglaRegioneResidenzaAssociazione : (src.IsMaggiorenne ? src.SiglaRegioneResidenzaPersonaFisica : null)))
                    .ForMember(dest => dest.COMUNE_RESIDENZA, opt => opt.MapFrom(src => src.IsAssociazione ? src.ComuneSedeAssociazione : (src.IsMaggiorenne ? src.ComuneResidenza : null)))
                    .ForMember(dest => dest.CODICE_COMUNE_RESIDENZA, opt => opt.MapFrom(src => src.IsAssociazione ? src.CodiceComuneResidenzaAssociazione : (src.IsMaggiorenne ? src.CodiceComuneResidenzaPersonaFisica : null)))
                    .ForMember(dest => dest.PROVINCIA_RESIDENZA, opt => opt.MapFrom(src => src.IsAssociazione ? src.ProvinciaSedeAssociazione : (src.IsMaggiorenne ? src.ProvinciaResidenza : null)))
                    .ForMember(dest => dest.CODICE_PROVINCIA_RESIDENZA, opt => opt.MapFrom(src => src.IsAssociazione ? src.SiglaProvinciaResidenzaAssociazione : (src.IsMaggiorenne ? src.SiglaProvinciaResidenzaPersonaFisica : null)))
                    .ForMember(dest => dest.CAP_RESIDENZA, opt => opt.MapFrom(src => src.IsAssociazione ? src.CAP_SedeAssociazione : null))
                    .ForMember(dest => dest.PEC, opt => opt.MapFrom(src => src.Email_PEC))
                    .ForMember(dest => dest.EMAIL, opt => opt.MapFrom(src => src.Email_PEC))
                    .ForMember(dest => dest.ID_TIPO_ASSEGNATARIO, opt => opt.MapFrom(src => src.IsMaggiorenne ? 1 : (src.IsMinorenne ? 2 : 3)))
                    .ForMember(dest => dest.RINNOVO, opt => opt.MapFrom(src => false))
                    .ForMember(dest => dest.ID_TIPO_AUTORIZZAZIONE, opt => opt.MapFrom(src => 2))
                    .ForMember(dest => dest.ID_STATO_AUTORIZZAZIONE, opt => opt.MapFrom(src => 2))
                    .ForMember(dest => dest.DATA_RILASCIO, opt => opt.MapFrom(src => src.DataProtocolloDateTime))
                    .ForMember(dest => dest.CODICE_PROTOCOLLO_ENTRATA, opt => opt.MapFrom(src => src.ProtocolloRichiesta))
                    .ForMember(dest => dest.DATA_PROTOCOLLO_ENTRATA, opt => opt.MapFrom(src => src.DataRichiestaDateTime))
                    .ForMember(dest => dest.CODICE_PROTOCOLLO_USCITA, opt => opt.MapFrom(src => src.Protocollo))
                    .ForMember(dest => dest.DATA_PROTOCOLLO_USCITA, opt => opt.MapFrom(src => src.DataProtocolloDateTime))
                    .ForMember(dest => dest.DATA_SCADENZA, opt => opt.MapFrom(src => src.DataRichiestaDateTime.HasValue ? new DateTime(src.DataRichiestaDateTime.Value.Year, 12, 31).AddYears(9) : (src.DataProtocolloDateTime.HasValue ? new DateTime(src.DataProtocolloDateTime.Value.Year, 12, 31).AddYears(9) : (DateTime?)null)))
                    .ForMember(dest => dest.UTENTE, opt => opt.MapFrom(src => "IMPORT"))
                    .ForMember(dest => dest.ID_ANAGRAFICA_ISPETTORATO, opt => opt.MapFrom(src => src.Ispettorato));
            });

            return config.CreateMapper();
        }
    }
}
