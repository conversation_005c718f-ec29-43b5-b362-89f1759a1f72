﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RAD4_Importer.Enums
{
    public enum ColumnName
    {
        Regione,
        Ispettorato,
        Prefisso,
        Suffisso_1,
        Suffisso_2,
        Suffisso_3,
        NominativoSR,
        StatoNominativoSR,
        DataRichiesta,
        ProtocolloRichiesta,
        TipologiaRichiedente,
        NomeRadioamatore,
        CognomeRadioamatore,
        NominativoRadioamatore,
        CodiceFiscale,
        ComuneNascita,
        ProvinciaNascita,
        DataNascita,
        Cittadinanza,
        ComuneResidenza,
        ProvinciaResidenza,
        RegioneResidenza,
        IndirizzoResidenza,
        DenominazioneAssociazione,
        CF_PartitaIVA_Associazione,
        ComuneSedeAssociazione,
        ProvinciaSedeAssociazione,
        RegioneSedeAssociazione,
        IndirizzoSedeAssociazione,
        CAP_SedeAssociazione,
        NomeRL,
        CognomeRL,
        NomeOperatore, 
        CognomeOperatore,
        Email_PEC,
        NomeMinorenne,
        CognomeMinorenne,
        TipoRichiesta,
        DurataAutorizzazione, 
        ComuneImpianto,
        ProvinciaImpianto,
        RegioneImpianto,
        IndirizzoImpianto,
        DataProtocollo,
        Protocollo,
        CodiceComuneNascita,
        CodiceComuneResidenzaPersonaFisica,
        SiglaProvinciaResidenzaPersonaFisica,
        SiglaRegioneResidenzaPersonaFisica,
        CodiceComuneResidenzaAssociazione,
        SiglaProvinciaResidenzaAssociazione,
        SiglaRegioneResidenzaAssociazione,
        CodiceComuneStazioneRipetitrice,
        SiglaProvinciaStazioneRipetitrice,
        SiglaRegioneStazioneRipetitrice
    }
}
