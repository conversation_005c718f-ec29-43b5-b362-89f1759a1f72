#See https://aka.ms/containerfastmode to understand how Visual Studio uses this Dockerfile to build your images for faster debugging.

FROM mcr.microsoft.com/dotnet/aspnet:5.0 AS base
WORKDIR /app
EXPOSE 80
EXPOSE 443

FROM mcr.microsoft.com/dotnet/sdk:5.0 AS build
WORKDIR /src
COPY ["MicroSV-Radioamatori/MicroSV-Radioamatori.csproj", "MicroSV-Radioamatori/"]
COPY ["NuGet.Config", "NuGet.Config"]
RUN dotnet restore "MicroSV-Radioamatori/MicroSV-Radioamatori.csproj" --configfile NuGet.Config -nowarn:msb3202,nu1503
COPY . .
WORKDIR "/src/MicroSV-Radioamatori"
RUN dotnet build "MicroSV-Radioamatori.csproj" -c Release -o /app/build

FROM build AS publish
RUN dotnet publish "MicroSV-Radioamatori.csproj" -c Release -o /app/publish

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "MicroSV-Radioamatori.dll"]