﻿using FluentValidation;
using Inv.Framework.Api.DTO;
using Invitalia.Misure.FiltriAPI;
using Invitalia.Misure.FiltriAPI.Constants;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using MicroSV_Radioamatori.BL.Interfaces;
using Newtonsoft.Json.Linq;
using System;
using System.Threading.Tasks;
using MicroSV_Radioamatori.BL.IncomingDTOs;
using System.Collections.Generic;
using System.Linq;
using MicroSV_Radioamatori.BL.DTOs;
using Microsoft.AspNetCore.Http;
using MicroSV_Radioamatori.API.Validators;
using Microsoft.AspNetCore.Components.Forms;
using MicroSV_Radioamatori.BL.Mapping;
using MicroSV_Radioamatori.DAL.Entities;

namespace MicroSV_Radioamatori.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [ServiceFilter(typeof(TraceControllerActionsFilter))]
    public class ControlloEGenerazioneNominativoController : ControllerBase
    {

        private readonly IConfiguration _conf;
        private readonly IRadiamatoreBl _radioamatoreBl;

        public ControlloEGenerazioneNominativoController(IConfiguration conf, IRadiamatoreBl radioamatoreBl)
        {
            this._conf = conf;
            this._radioamatoreBl = radioamatoreBl;
        }


        [HttpPost("",
            Name = "ControlloEGenerazioneNominativo")]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        [ProducesResponseType(typeof(EsitoControlloEGenerazioneDto), StatusCodes.Status201Created)]
        [ProducesResponseType(typeof(EsitoControlloEGenerazioneDto), StatusCodes.Status409Conflict)]
        [ProducesResponseType(typeof(EsitoControlloEGenerazioneDto), StatusCodes.Status202Accepted)]
        [ProducesResponseType(typeof(ValidationException), StatusCodes.Status400BadRequest)]
        public async Task<IActionResult> ControllaEGeneraNominativoStandardAsync(
            [FromHeader(Name = HttpHeaderParamsName.HEADER_PARAM_TIPO_APPLICAZIONE)] string tipoApplicazione,
            [FromBody] RadioamatoreDaGenerareDto radioamatoreDaGenerare,
            [FromQuery] bool isStandard, [FromQuery] bool isContest, [FromQuery] bool isManifestazione, [FromQuery] bool isManifestazioneArt44,
            [FromQuery] bool isProsecuzionePerOmonimo)
        {
            try
            {
                if (!radioamatoreDaGenerare.IsGenerazioneNominativo.HasValue)
                    throw new ValidationException($"{nameof(radioamatoreDaGenerare.IsGenerazioneNominativo)} deve essere valorizzato");

                return await GeneraNominativoAsync(tipoApplicazione, radioamatoreDaGenerare, isStandard, isContest, isManifestazione, isManifestazioneArt44, isProsecuzionePerOmonimo);

            }
            catch (ValidationException ex)
            {
                return BadRequest(ex);
            }
            catch (Refit.ApiException ex)
            {
                throw new APIException(ex.Message, ex.StackTrace, ex.Uri.AbsoluteUri, ex.Content, ex.Headers);
            }
            catch (Exception ex)
            {
                // Per recuperare le eccezioni interne alla API Common invocata dalla corrente API 
                if (ex is APIException) throw;

                throw new APIException(ex.Message, ex.StackTrace, ex.InnerException?.Message, ex.InnerException?.StackTrace);
            }
        }

        private async Task<IActionResult> GeneraNominativoAsync(string tipoApplicazione, RadioamatoreDaGenerareDto radioamatoreDaGenerare, bool isStandard, bool isContest, bool isManifestazione, bool isManifestazioneArt44, bool isProsecuzionePerOmonimo)
        {
            #region Validazione Input
            if (radioamatoreDaGenerare == null)
            {
                throw new ValidationException($"{nameof(radioamatoreDaGenerare)} non valorizzato");
            }
            var listaBool = new List<bool>()
                {
                    isContest, isManifestazione, isManifestazioneArt44, isStandard
                };
            if (listaBool.Count(b => b == true) != 1)
            {
                throw new ValidationException($"E' necessario valorizzare a true un solo booleano tra quelli indicanti il tipo di generazione");
            }
            #endregion

            var controllo = await _radioamatoreBl.ControllaNominativoPerGenerazioneAsync(radioamatoreDaGenerare, isStandard, isContest, isManifestazione, isManifestazioneArt44);

            if (!controllo.Successo)
            {
                if (controllo.ProseguiSuFEAbilitato)
                {
                    if (isProsecuzionePerOmonimo)
                    {
                        //genera nominativo
                        if (!radioamatoreDaGenerare.IsGenerazioneNominativo.GetValueOrDefault())
                        {
                            if (!isStandard)
                                throw new ValidationException("Attenzione: è possibile censire un nominativo esistente solo se di tipologia STANDARD");

                            var validator = new RadioamatoreDaGenerareDtoValidator(_conf, _radioamatoreBl);
                            var validationResult = validator.Validate(radioamatoreDaGenerare);
                            if (!validationResult.IsValid)
                            {
                                return Conflict(
                                   new EsitoControlloEGenerazioneDto()
                                   {
                                       SuccessoControlli = false,
                                       ErroriControlli = validationResult.Errors.Select(s => s.ErrorMessage).ToList(),
                                       ProseguiSuFEAbilitato = controllo.ProseguiSuFEAbilitato,
                                       SuccessoGenerazione = false
                                   });
                            }

                            var nominativo = await _radioamatoreBl.InserisciNominativoStandardAsync(radioamatoreDaGenerare);

                            return Ok(
                              new EsitoControlloEGenerazioneDto()
                              {
                                  SuccessoControlli = controllo.Successo,
                                  ErroriControlli = controllo.Errori,
                                  ProseguiSuFEAbilitato = controllo.ProseguiSuFEAbilitato,
                                  SuccessoGenerazione = true
                              });
                        }
                        else
                        {

                            var nominativo = await _radioamatoreBl.GeneraNominativoGeaAsync
                                (tipoApplicazione, radioamatoreDaGenerare, isStandard, isContest, isManifestazione, isManifestazioneArt44);

                            return Created($"api/Radioamatore/codicenominativo/{nominativo.CodiceNominativoGenerato}",
                                new EsitoControlloEGenerazioneDto()
                                {
                                    SuccessoControlli = controllo.Successo,
                                    ErroriControlli = controllo.Errori,
                                    ProseguiSuFEAbilitato = controllo.ProseguiSuFEAbilitato,
                                    SuccessoGenerazione = true,
                                    NominativoGenerato = nominativo
                                }
                                );
                        }

                    }
                    else
                    {
                        return Accepted(
                            new EsitoControlloEGenerazioneDto()
                            {
                                SuccessoControlli = controllo.Successo,
                                ErroriControlli = controllo.Errori,
                                ProseguiSuFEAbilitato = controllo.ProseguiSuFEAbilitato,
                                SuccessoGenerazione = false
                            }
                        );
                    }
                }
                else
                {
                    return Conflict(
                        new EsitoControlloEGenerazioneDto()
                        {
                            SuccessoControlli = controllo.Successo,
                            ErroriControlli = controllo.Errori,
                            ProseguiSuFEAbilitato = controllo.ProseguiSuFEAbilitato,
                            SuccessoGenerazione = false
                        }
                    );
                }

            }
            else //CONTROLLI OK
            {
                if (radioamatoreDaGenerare.IsGenerazioneNominativo.GetValueOrDefault() == false)
                {
                    if (!isStandard)
                        throw new ValidationException("Attenzione: è possibile censire un nominativo esistente solo se di tipologia STANDARD");

                    var validator = new RadioamatoreDaGenerareDtoValidator(_conf, _radioamatoreBl);
                    var validationResult = validator.Validate(radioamatoreDaGenerare);
                    if (!validationResult.IsValid)
                    {
                        return Conflict(
                           new EsitoControlloEGenerazioneDto()
                           {
                               SuccessoControlli = false,
                               ErroriControlli = validationResult.Errors.Select(s => s.ErrorMessage).ToList(),
                               ProseguiSuFEAbilitato = controllo.ProseguiSuFEAbilitato,
                               SuccessoGenerazione = false
                           });
                    }

                    var nominativo = await _radioamatoreBl.InserisciNominativoStandardAsync(radioamatoreDaGenerare);

                    return Ok(
                      new EsitoControlloEGenerazioneDto()
                      {
                          SuccessoControlli = controllo.Successo,
                          ErroriControlli = controllo.Errori,
                          ProseguiSuFEAbilitato = controllo.ProseguiSuFEAbilitato,
                          SuccessoGenerazione = true
                      });
                }
                else //devo generare il nominativo
                {

                    var nominativo = await _radioamatoreBl.GeneraNominativoGeaAsync
                        (tipoApplicazione, radioamatoreDaGenerare, isStandard, isContest, isManifestazione, isManifestazioneArt44);

                    return Created($"api/Radioamatore/codicenominativo/{nominativo.CodiceNominativoGenerato}",
                        new EsitoControlloEGenerazioneDto()
                        {
                            SuccessoControlli = controllo.Successo,
                            ErroriControlli = controllo.Errori,
                            ProseguiSuFEAbilitato = controllo.ProseguiSuFEAbilitato,
                            SuccessoGenerazione = true,
                            NominativoGenerato = nominativo
                        }
                        );
                }


            }
        }


    }
}
