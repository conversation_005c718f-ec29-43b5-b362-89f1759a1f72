﻿using FluentValidation;
using Inv.Framework.Api.DTO;
using Invitalia.Misure.FiltriAPI;
using Invitalia.Misure.FiltriAPI.Constants;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using MicroSV_Radioamatori.BL.Interfaces;
using Newtonsoft.Json.Linq;
using System;
using System.Threading.Tasks;

namespace MicroSV_Radioamatori.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [ServiceFilter(typeof(TraceControllerActionsFilter))]
    public class GenerazioneNominativoController : ControllerBase
    {

        private readonly IConfiguration _conf;
        private readonly IRadiamatoreBl _radioamatoreBl;

        public GenerazioneNominativoController(IConfiguration conf, IRadiamatoreBl radioamatoreBl)
        {
            this._conf = conf;
            this._radioamatoreBl = radioamatoreBl;
        }

        [HttpPost("", Name = "GeneraNominativo")]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        public async Task<IActionResult> GeneraNominativoStandardAsync(
            [FromHeader(Name = HttpHeaderParamsName.HEADER_PARAM_TIPO_APPLICAZIONE)] string tipoApplicazione,
            [FromBody] JObject domanda)
        {
            try
            {
                #region Validazione Input
                if (domanda == null)
                {
                    throw new ValidationException($"{nameof(domanda)} non valorizzato");
                }


                #endregion

                string protocollo = (string)domanda.SelectToken("Dati.Protocollo");
                bool isStandard = Convert.ToBoolean(domanda.SelectToken("Dati.IsStandard"));
                bool isStandardMinorenne = Convert.ToBoolean(domanda.SelectToken("Dati.IsStandardMinorenne"));
                bool isStandardArt144Cdc = Convert.ToBoolean(domanda.SelectToken("Dati.IsStandardArt144Cdc"));
                string IdTipologiaSoggettoImpresa = (string)domanda.SelectToken("Dati.IdTipologiaSoggettoImpresa");
                bool isSpecialePerContest = Convert.ToBoolean(domanda.SelectToken("Dati.IsSpecialePerContest"));
                bool isSpecialePerManifestazione = Convert.ToBoolean(domanda.SelectToken("Dati.IsSpecialePerManifestazione"));
                bool isSpecialePerManifestazioneArt144Cdc = Convert.ToBoolean(domanda.SelectToken("Dati.IsSpecialePerManifestazioneArt144Cdc"));

                if (isStandard || isStandardMinorenne || isStandardArt144Cdc)
                {
                    if (IdTipologiaSoggettoImpresa == "8")//Associazioni o sezioni delle associazioni dei radioamatori legalmente costituite
                    {
                        return Ok(await _radioamatoreBl.GeneraNominativoAssociazioneAsync(tipoApplicazione, domanda));
                    }
                    else
                    {
                        throw new ValidationException("La Richiesta di Nominativo Standard non è piu prevista da RAD1");
                    }

                }
                else if (isSpecialePerManifestazione || isSpecialePerManifestazioneArt144Cdc)
                {
                    //if (IdTipologiaSoggettoImpresa == "8")//Associazioni o sezioni delle associazioni dei radioamatori legalmente costituite
                    //{
                    //    return Ok(await _radioamatoreBl.GeneraNominativoAssociazioneAsync(tipoApplicazione, domanda));
                    //}
                    //else
                    //{
                    return Ok(await _radioamatoreBl.GeneraNominativoManifestazioneAsync(tipoApplicazione, domanda));
                    //}

                }
                else if (isSpecialePerContest)
                {
                    return Ok(await _radioamatoreBl.GeneraNominativoContestAsync(tipoApplicazione, domanda));
                }

                throw new Exception($"Richiesta di nominativo {protocollo} non ricducibile a un tipologia.");
            }
            catch (ValidationException ex)
            {
                return BadRequest(ex);
            }
            catch (Refit.ApiException ex)
            {
                throw new APIException(ex.Message, ex.StackTrace, ex.Uri.AbsoluteUri, ex.Content, ex.Headers);
            }
            catch (Exception ex)
            {
                // Per recuperare le eccezioni interne alla API Common invocata dalla corrente API 
                if (ex is APIException) throw;

                throw new APIException(ex.Message, ex.StackTrace, ex.InnerException?.Message, ex.InnerException?.StackTrace, new { domanda });
            }
        }

        [HttpPost("GeneraNominativoAutorizzazioni", Name = "GeneraNominativoAutorizzazioni")]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        public async Task<IActionResult> GeneraNominativoAutorizzazioniStandardAsync(
           [FromHeader(Name = HttpHeaderParamsName.HEADER_PARAM_TIPO_APPLICAZIONE)] string tipoApplicazione,
           [FromBody] JObject domanda)
        {
            try
            {
                #region Validazione Input
                if (domanda == null)
                {
                    throw new ValidationException($"{nameof(domanda)} non valorizzato");
                }


                #endregion

                string nominativo = (string)domanda.SelectToken("Assegnatario.Nominativo");


                if (string.IsNullOrWhiteSpace(nominativo))
                {
                    return Ok(await _radioamatoreBl.GeneraNominativoStandardAsync(tipoApplicazione, domanda));
                }

                throw new Exception($"Richiesta di nominativo già è stato creato");
            }
            catch (ValidationException ex)
            {
                return BadRequest(ex);
            }
            catch (Refit.ApiException ex)
            {
                throw new APIException(ex.Message, ex.StackTrace, ex.Uri.AbsoluteUri, ex.Content, ex.Headers);
            }
            catch (Exception ex)
            {
                // Per recuperare le eccezioni interne alla API Common invocata dalla corrente API 
                if (ex is APIException) throw;

                throw new APIException(ex.Message, ex.StackTrace, ex.InnerException?.Message, ex.InnerException?.StackTrace, new { domanda });
            }
        }
    }
}
