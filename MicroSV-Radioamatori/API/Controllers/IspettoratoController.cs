﻿using FluentValidation;
using Inv.Framework.Api.DTO;
using Invitalia.Misure.FiltriAPI;
using Invitalia.Misure.FiltriAPI.Constants;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using MicroSV_Radioamatori.BL.Interfaces;
using System;
using System.Threading.Tasks;

namespace MicroSV_Radioamatori.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [ServiceFilter(typeof(TraceControllerActionsFilter))]
    public class IspettoratoController : ControllerBase
    {
        private readonly IConfiguration _conf;
        private readonly IRadiamatoreBl _radioamatoreBl;

        public IspettoratoController(IConfiguration conf, IRadiamatoreBl radioamatoreBl)
        {
            this._conf = conf;
            this._radioamatoreBl = radioamatoreBl;
        }

        /// <summary>
        /// GetAnagraficaIspettoratoBySiglaRegione
        /// </summary>
        /// <param name="siglaRegione"></param>
        /// <returns></returns>
        /// <exception cref="APIException"></exception>
        [HttpGet("siglaRegione/{siglaRegione}")]
        public async Task<IActionResult> GetAnagraficaIspettoratoBySiglaRegione(
            [FromHeader(Name = HttpHeaderParamsName.HEADER_PARAM_TIPO_APPLICAZIONE)] string tipoApplicazione,
            [FromRoute] string siglaRegione)
        {
            try
            {
                #region Validazione Input
                if (string.IsNullOrWhiteSpace(siglaRegione))
                {
                    throw new ValidationException($"{nameof(siglaRegione)} non valorizzato");
                }
                #endregion

                var dto = await _radioamatoreBl.GetIspettoratoBySiglaRegioneAsync(siglaRegione);
                return Ok(dto);
            }
            catch (ValidationException ex)
            {
                return BadRequest(ex);
            }
            catch (Refit.ApiException ex)
            {
                throw new APIException(ex.Message, ex.StackTrace, ex.Uri.AbsoluteUri, ex.Content, ex.Headers);
            }
            catch (Exception ex)
            {
                // Per recuperare le eccezioni interne alla API Common invocata dalla corrente API 
                if (ex is APIException) throw;

                throw new APIException(ex.Message, ex.StackTrace, ex.InnerException?.Message, ex.InnerException?.StackTrace, new { siglaRegione });
            }
        }

        /// <summary>
        /// GetAnagraficaIspettorati
        /// </summary>
        /// <returns></returns>
        /// <exception cref="APIException"></exception>
        [HttpGet]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        public async Task<IActionResult> GetAnagraficaIspettorati(
            [FromHeader(Name = HttpHeaderParamsName.HEADER_PARAM_TIPO_APPLICAZIONE)] string tipoApplicazione,
            [FromHeader(Name = "x-visualizza-anche-dismessi")] bool? visualizzaAncheDismessi)
        {

            try
            {
                var objCf = await _radioamatoreBl.GetAnagraficaIspettoratiAsync(visualizzaAncheDismessi);
                return Ok(objCf);
            }
            catch (ValidationException ex)
            {
                return BadRequest(ex);
            }
            catch (Refit.ApiException ex)
            {
                throw new APIException(ex.Message, ex.StackTrace, ex.Uri.AbsoluteUri, ex.Content, ex.Headers); 
            }
            catch (Exception ex)
            {
                // Per recuperare le eccezioni interne alla API Common invocata dalla corrente API 
                if (ex is APIException) throw;

                throw new APIException(ex.Message, ex.StackTrace, ex.InnerException?.Message, ex.InnerException?.StackTrace);
            }
        }
    }
}
