﻿using FluentValidation;
using FluentValidation.Results;
using Inv.Framework.Api.DTO;
using Invitalia.Misure.FiltriAPI;
using Invitalia.Misure.FiltriAPI.Constants;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using MicroSV_Radioamatori.API.Validators;
using MicroSV_Radioamatori.BL.Common;
using MicroSV_Radioamatori.BL.DTOs;
using MicroSV_Radioamatori.BL.Enums;
using MicroSV_Radioamatori.BL.IncomingDTOs;
using MicroSV_Radioamatori.BL.Interfaces;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Threading.Tasks;

namespace MicroSV_Radioamatori.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [ServiceFilter(typeof(TraceControllerActionsFilter))]
    public class RadioamatoreController : ControllerBase
    {
        private readonly IConfiguration _conf;
        private readonly IRadiamatoreBl _radioamatoreBl;

        public RadioamatoreController(IConfiguration conf, IRadiamatoreBl radioamatoreBl)
        {
            this._conf = conf;
            this._radioamatoreBl = radioamatoreBl;
        }

        #region GET

        /// <summary>
        /// GetRadioamatoriByCF
        /// </summary>
        /// <param name="codiceFiscale"></param>
        /// <returns></returns>
        /// <exception cref="APIException"></exception>
        [HttpGet("codicefiscale/{codiceFiscale}", Name = "GetRadioamatoriByCF")]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        public async Task<IActionResult> GetRadioamatoriByCF(
            [FromHeader(Name = HttpHeaderParamsName.HEADER_PARAM_TIPO_APPLICAZIONE)] string tipoApplicazione,
            [FromRoute] string codiceFiscale)
        {

            try
            {
                #region Validazione Input
                if (string.IsNullOrWhiteSpace(codiceFiscale))
                {
                    throw new ValidationException($"{nameof(codiceFiscale)} non valorizzato");
                }

                #endregion
                if (!string.IsNullOrWhiteSpace(codiceFiscale))
                {
                    List<RadioamatoreDto> objCf = await _radioamatoreBl.GetRadioamatoriByCodiceFiscaleAsync(codiceFiscale);
                    if (objCf != null && objCf.Any())
                    {
                        return Ok(objCf);
                    }
                }

                return Ok(new List<RadioamatoreDto>());

            }
            catch (ValidationException ex)
            {
                return BadRequest(ex);
            }
            catch (Refit.ApiException ex)
            {
                throw new APIException(ex.Message, ex.StackTrace, ex.Uri.AbsoluteUri, ex.Content, ex.Headers);
            }
            catch (Exception ex)
            {
                // Per recuperare le eccezioni interne alla API Common invocata dalla corrente API 
                if (ex is APIException) throw;

                throw new APIException(ex.Message, ex.StackTrace, ex.InnerException?.Message, ex.InnerException?.StackTrace, new { codiceFiscale });
            }
        }

        /// <summary>
        /// GetRadioamatori
        /// </summary>
        /// <returns></returns>
        /// <exception cref="APIException"></exception>
        [HttpGet(Name = "GetRadioamatori")]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        public async Task<IActionResult> GetRadioamatori(
            [FromHeader(Name = HttpHeaderParamsName.HEADER_PARAM_TIPO_APPLICAZIONE)] string tipoApplicazione)
        {

            try
            {
                var objCf = await _radioamatoreBl.GetRadioamatoriAsync();
                return Ok(objCf);
            }
            catch (ValidationException ex)
            {
                return BadRequest(ex);
            }
            catch (Refit.ApiException ex)
            {
                throw new APIException(ex.Message, ex.StackTrace, ex.Uri.AbsoluteUri, ex.Content, ex.Headers);
            }
            catch (Exception ex)
            {
                // Per recuperare le eccezioni interne alla API Common invocata dalla corrente API 
                if (ex is APIException) throw;

                throw new APIException(ex.Message, ex.StackTrace, ex.InnerException?.Message, ex.InnerException?.StackTrace);
            }
        }
        /// <summary>
        /// GetRadioamatoriByCodiceNominativo
        /// </summary>
        /// <param name="tipoApplicazione"></param>
        /// <param name="codiceNominativo"></param>
        /// <returns></returns>
        [HttpGet("codicenominativo/{codiceNominativo}", Name = "GetRadioamatoriByCodiceNominativo")]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        public async Task<IActionResult> GetRadioamatoriByCodiceNominativo(
            [FromHeader(Name = HttpHeaderParamsName.HEADER_PARAM_TIPO_APPLICAZIONE)] string tipoApplicazione,
            [FromRoute] string codiceNominativo)
        {
            try
            {
                #region Validazione Input
                if (string.IsNullOrWhiteSpace(codiceNominativo))
                {
                    throw new ValidationException($"{nameof(codiceNominativo)} non valorizzato");
                }

                #endregion
                if (!string.IsNullOrWhiteSpace(codiceNominativo))
                {
                    List<RadioamatoreDto> objnominativo = await _radioamatoreBl.GetRadioamatoriByCodiceNominantivoAsync(codiceNominativo);
                    if (objnominativo != null && objnominativo.Any())
                    {
                        return Ok(objnominativo);
                    }
                }
                return Ok(new List<RadioamatoreDto>());
            }
            catch (ValidationException ex)
            {
                return BadRequest(ex);
            }
            catch (Refit.ApiException ex)
            {
                throw new APIException(ex.Message, ex.StackTrace, ex.Uri.AbsoluteUri, ex.Content, ex.Headers);
            }
            catch (Exception ex)
            {
                // Per recuperare le eccezioni interne alla API Common invocata dalla corrente API 
                if (ex is APIException) throw;

                throw new APIException(ex.Message, ex.StackTrace, ex.InnerException?.Message, ex.InnerException?.StackTrace, new { codiceNominativo });
            }
        }


        [HttpGet("contest/{codiceContest}", Name = "GetRadioamatoriByCodiceContest")]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        public async Task<IActionResult> GetRadioamatoriByCodiceContest([FromRoute] string codiceContest)
        {
            try
            {
                #region Validazione Input

                if (string.IsNullOrWhiteSpace(codiceContest))
                {
                    throw new ValidationException($"{nameof(codiceContest)} non valorizzato");
                }

                #endregion

                var result = await _radioamatoreBl.GetRadioamatoriByCodiceContestAsync(codiceContest);

                return Ok(result);
            }
            catch (ValidationException ex)
            {
                return BadRequest(ex);
            }
            catch (Refit.ApiException ex)
            {
                throw new APIException(ex.Message, ex.StackTrace, ex.Uri.AbsoluteUri, ex.Content, ex.Headers);
            }
            catch (Exception ex)
            {
                // Per recuperare le eccezioni interne alla API Common invocata dalla corrente API 
                if (ex is APIException) throw;

                throw new APIException(ex.Message, ex.StackTrace, ex.InnerException?.Message, ex.InnerException?.StackTrace, new { codiceContest });
            }
        }


        /// <summary>
        /// GetRadioamatoribyAnagrafica
        /// </summary>
        /// <param name="nome"></param>
        /// <param name="cognome"></param>
        /// <param name="datanascita"></param>
        /// <returns></returns>
        /// <exception cref="APIException"></exception>
        [HttpGet("nome/{nome}/cognome/{cognome}/datanascita/{datanascita}", Name = "GetRadioamatoriByAnagrafica")]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        public async Task<IActionResult> GetRadioamatoriByAnagrafica(
            [FromHeader(Name = HttpHeaderParamsName.HEADER_PARAM_TIPO_APPLICAZIONE)] string tipoApplicazione,
            [FromRoute] string nome,
            [FromRoute] string cognome,
            [FromRoute] string datanascita)
        {
            try
            {
                #region Validazione Input
                if (string.IsNullOrWhiteSpace(nome))
                {
                    throw new ValidationException($"{nameof(nome)} non valorizzato");
                }
                if (string.IsNullOrWhiteSpace(cognome))
                {
                    throw new ValidationException($"{nameof(cognome)} non valorizzato");
                }
                if (string.IsNullOrWhiteSpace(datanascita))
                {
                    throw new ValidationException($"{nameof(datanascita)} non valorizzato");
                }
                #endregion
                List<RadioamatoreDto> objnominativo = await _radioamatoreBl.GetRadioamatoriByAnagraficaAsync(nome, cognome, datanascita);

                if (objnominativo != null && objnominativo.Any())
                {
                    return Ok(objnominativo);
                }

                return Ok(new List<RadioamatoreDto>());
            }
            catch (ValidationException ex)
            {
                return BadRequest(ex);
            }
            catch (Refit.ApiException ex)
            {
                throw new APIException(ex.Message, ex.StackTrace, ex.Uri.AbsoluteUri, ex.Content, ex.Headers);
            }
            catch (Exception ex)
            {
                // Per recuperare le eccezioni interne alla API Common invocata dalla corrente API 
                if (ex is APIException) throw;

                throw new APIException(ex.Message, ex.StackTrace, ex.InnerException?.Message, ex.InnerException?.StackTrace, new { nome, cognome, datanascita });
            }
        }

        /// <summary>
        /// VerificaNominativoAttivo
        /// </summary>
        /// <param name="codiceFiscale"></param>
        /// <param name="codicenominativo"></param>
        /// <param name="nome"></param>
        /// <param name="cognome"></param>
        /// <param name="datanascita"></param>
        /// <returns></returns>
        /// <exception cref="APIException"></exception>
        [HttpGet("VerificaNominativoAttivo")]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        public async Task<IActionResult> VerificaNominativoAttivo(
            [FromHeader(Name = HttpHeaderParamsName.HEADER_PARAM_TIPO_APPLICAZIONE)] string tipoApplicazione,
            [FromQuery] string codiceFiscale,
            [FromQuery] string codicenominativo,
            [FromQuery] string nome,
            [FromQuery] string cognome,
            [FromQuery] string datanascita)
        {
            try
            {
                BoolResultRadioamatoreDto objCf = null;
                if (!string.IsNullOrEmpty(codiceFiscale))
                {
                    objCf = await _radioamatoreBl.GetRadioamatoriByCodiceFiscaleResultBoolAsync(codiceFiscale);
                    if (objCf.Result != true)
                    {
                        if (!string.IsNullOrEmpty(codicenominativo) && !string.IsNullOrEmpty(nome) && !string.IsNullOrEmpty(cognome) && !string.IsNullOrEmpty(datanascita))
                        {
                            objCf = await _radioamatoreBl.GetRadioamatoriByCodiceNominativoResultBoolAsync(codicenominativo, nome, cognome, datanascita);

                        }
                    }

                }
                else if (!string.IsNullOrEmpty(codicenominativo) && !string.IsNullOrEmpty(nome) && !string.IsNullOrEmpty(cognome) && !string.IsNullOrEmpty(datanascita))
                {
                    objCf = await _radioamatoreBl.GetRadioamatoriByCodiceNominativoResultBoolAsync(codicenominativo, nome, cognome, datanascita);
                }
                else
                {
                    throw new ValidationException("I campi non sono valorizzati");
                }
                return Ok(objCf);
            }
            catch (ValidationException ex)
            {
                return BadRequest(ex);
            }
            catch (Refit.ApiException ex)
            {
                throw new APIException(ex.Message, ex.StackTrace, ex.Uri.AbsoluteUri, ex.Content, ex.Headers);
            }
            catch (Exception ex)
            {
                // Per recuperare le eccezioni interne alla API Common invocata dalla corrente API 
                if (ex is APIException) throw;

                throw new APIException(ex.Message, ex.StackTrace, ex.InnerException?.Message, ex.InnerException?.StackTrace, new { codiceFiscale });
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="nome"></param>
        /// <param name="cognome"></param>
        /// <param name="datanascita"></param>
        /// <returns></returns>
        [HttpGet("NomeCognomeData", Name = "GetNominativiNomeCognomeData")]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        public async Task<IActionResult> GetNominativiByNomeCognomeData([FromQuery] string nome, [FromQuery] string cognome, [FromQuery] string datanascita)
        {
            try
            {
                #region Validazione Input
                if (string.IsNullOrWhiteSpace(nome))
                {
                    throw new ValidationException($"{nameof(nome)} non valorizzato");
                }


                if (string.IsNullOrWhiteSpace(cognome))
                {
                    throw new ValidationException($"{nameof(cognome)} non valorizzato");
                }


                if (string.IsNullOrWhiteSpace(datanascita))
                {
                    throw new ValidationException($"{nameof(datanascita)} non valorizzato");
                }

                #endregion
                List<RadioamatoreDto> objByName = await _radioamatoreBl.GetRadioamatoriByAnagraficaAsync(nome, cognome, datanascita);
                if (objByName != null)
                {
                    return Ok(objByName);
                }

                return Ok(new List<RadioamatoreDto>());
            }
            catch (ValidationException ex)
            {
                return BadRequest(ex);
            }
            catch (Refit.ApiException ex)
            {
                throw new APIException(ex.Message, ex.StackTrace, ex.Uri.AbsoluteUri, ex.Content, ex.Headers);
            }
            catch (Exception ex)
            {
                // Per recuperare le eccezioni interne alla API Common invocata dalla corrente API 
                if (ex is APIException) throw;

                throw new APIException(ex.Message, ex.StackTrace, ex.InnerException?.Message, ex.InnerException?.StackTrace, new { nome, cognome, datanascita });
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="codiceFiscale"></param>
        /// <param name="nome"></param>
        /// <param name="cognome"></param>
        /// <param name="datanascita"></param>
        /// <returns></returns>
        [HttpGet("CodiceFiscaleOrNomeCognomeData", Name = "GetNominativiByCfOrNomeCognomeData")]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        public async Task<IActionResult> GetNominativiByCfOrNomeCognomeData([FromQuery] string codiceFiscale, [FromQuery] string nome, [FromQuery] string cognome, [FromQuery] string datanascita)
        {
            try
            {
                if (!string.IsNullOrWhiteSpace(codiceFiscale))
                {
                    List<RadioamatoreDto> objCf = await _radioamatoreBl.GetRadioamatoriByCodiceFiscaleAsync(codiceFiscale);
                    if (objCf != null && objCf.Any())
                    {
                        return Ok(objCf);
                    }
                }

                List<RadioamatoreDto> objByName = await _radioamatoreBl.GetRadioamatoriByAnagraficaAsync(nome, cognome, datanascita);
                if (objByName != null)
                {
                    return Ok(objByName);
                }

                return Ok(new List<RadioamatoreDto>());
            }
            catch (ValidationException ex)
            {
                return BadRequest(ex);
            }
            catch (Refit.ApiException ex)
            {
                throw new APIException(ex.Message, ex.StackTrace, ex.Uri.AbsoluteUri, ex.Content, ex.Headers);
            }
            catch (Exception ex)
            {
                // Per recuperare le eccezioni interne alla API Common invocata dalla corrente API 
                if (ex is APIException) throw;

                throw new APIException(ex.Message, ex.StackTrace, ex.InnerException?.Message, ex.InnerException?.StackTrace, new { codiceFiscale });
            }
        }

        #endregion

        #region POST

        /// <summary>
        /// AnnullaCodiceNominativo
        /// </summary>
        /// <param name="codiceNominativo"></param>
        /// <returns></returns>
        /// <exception cref="APIException"></exception>
        [HttpPost("CodiceDomandaFE/{codiceDomandaFE}/annulla", Name = "AnnullaCodiceNominativo")]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        public async Task<IActionResult> AnnullaCodiceNominativo(
            [FromHeader(Name = HttpHeaderParamsName.HEADER_PARAM_TIPO_APPLICAZIONE)] string tipoApplicazione,
            [FromRoute] string codiceDomandaFE)
        {
            try
            {

                if (string.IsNullOrWhiteSpace(codiceDomandaFE))
                {
                    throw new ValidationException($"{nameof(codiceDomandaFE)} non valorizzato");
                }

                await this._radioamatoreBl.AnnullaCodiceNominativoAsync(codiceDomandaFE);

                return Ok();
            }
            catch (ValidationException ex)
            {
                return BadRequest(ex);
            }
            catch (Refit.ApiException ex)
            {
                throw new APIException(ex.Message, ex.StackTrace, ex.Uri.AbsoluteUri, ex.Content, ex.Headers);
            }
            catch (Exception ex)
            {
                // Per recuperare le eccezioni interne alla API Common invocata dalla corrente API di Misura
                if (ex is APIException) throw;

                throw new APIException(ex.Message, ex.StackTrace, ex.InnerException?.Message, ex.InnerException?.StackTrace, new { codiceDomandaFE });
            }
        }

        [HttpPost("idRadioamatore/{idRadioamatore}/annulla", Name = "AnnullaCodiceNominativoByIdAsync")]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        public async Task<IActionResult> AnnullaCodiceNominativoByIdAsync(
            [FromHeader(Name = HttpHeaderParamsName.HEADER_PARAM_TIPO_APPLICAZIONE)] string tipoApplicazione,
            [FromRoute] int idRadioamatore)
        {
            try
            {
                if (idRadioamatore == default)
                {
                    throw new ValidationException($"{nameof(idRadioamatore)} non valorizzato");
                }

                if ((await _radioamatoreBl.GetRadioamatoreByIdAsync(idRadioamatore)) == null)
                {
                    throw new ValidationException($"{nameof(idRadioamatore)} non esistente");
                }

                await this._radioamatoreBl.AnnullaCodiceNominativoByIdAsync(idRadioamatore);

                return Ok();
            }
            catch (ValidationException ex)
            {
                return BadRequest(ex);
            }
            catch (Refit.ApiException ex)
            {
                throw new APIException(ex.Message, ex.StackTrace, ex.Uri.AbsoluteUri, ex.Content, ex.Headers);
            }
            catch (Exception ex)
            {
                // Per recuperare le eccezioni interne alla API Common invocata dalla corrente API di Misura
                if (ex is APIException) throw;

                throw new APIException(ex.Message, ex.StackTrace, ex.InnerException?.Message, ex.InnerException?.StackTrace, new { idRadioamatore });
            }
        }

        /// <summary>
        /// Set protocollo MISE
        /// </summary>
        /// <param name="ProtocolloMise"></param>
        /// <returns></returns>
        /// <exception cref="APIException"></exception>
        [HttpPost("ProtocolloMise", Name = "ProtocolloMise")]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        public async Task<IActionResult> ProtocolloMise(
            [FromHeader(Name = HttpHeaderParamsName.HEADER_PARAM_TIPO_APPLICAZIONE)] string TipoApplicazione,
            [FromBody] SetProtocolloMise datiProtocollo)
        {
            try
            {
                DateTime dataProtocollo;

                if (string.IsNullOrWhiteSpace(datiProtocollo.CodiceDomandaFE))
                {
                    throw new ValidationException($"{nameof(datiProtocollo.CodiceDomandaFE)} non valorizzato");
                }
                if (!DateTime.TryParseExact(datiProtocollo.DataProtocollo, "dd/MM/yyyy HH:mm:ss", CultureInfo.InvariantCulture, DateTimeStyles.None, out dataProtocollo))
                {
                    throw new ValidationException($"{nameof(datiProtocollo.DataProtocollo)} non valorizzato oppure non è nel formato atteso (dd/MM/yyyy HH:mm:ss)");
                }
                if (string.IsNullOrWhiteSpace(datiProtocollo.CodiceProtocollo))
                {
                    throw new ValidationException($"{nameof(datiProtocollo.CodiceProtocollo)} non valorizzato");
                }


                await this._radioamatoreBl.SetProtocolloMisePresentazioneAsync(datiProtocollo.CodiceDomandaFE, dataProtocollo, datiProtocollo.CodiceProtocollo);

                return Ok();
            }
            catch (ValidationException ex)
            {
                return BadRequest(ex);
            }
            catch (Refit.ApiException ex)
            {
                throw new APIException(ex.Message, ex.StackTrace, ex.Uri.AbsoluteUri, ex.Content, ex.Headers);
            }
            catch (Exception ex)
            {
                // Per recuperare le eccezioni interne alla API Common invocata dalla corrente API di Misura
                if (ex is APIException) throw;

                throw new APIException(ex.Message, ex.StackTrace, ex.InnerException?.Message, ex.InnerException?.StackTrace, new { datiProtocollo });
            }
        }


        /// <summary>
        /// Cerca nominativi per parametri di filtro senza logica di subentro ispettorati
        /// </summary>
        /// <param name="tipoApplicazione"></param>
        /// <param name="parametri"></param>
        /// <returns></returns>
        /// <exception cref="APIException"></exception>
        [HttpPost("FiltroRadioamatori", Name = "FiltroRadioamatori")]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        public async Task<IActionResult> FiltraRadioamatori(
            [FromHeader(Name = HttpHeaderParamsName.HEADER_PARAM_TIPO_APPLICAZIONE)] string tipoApplicazione,
            [FromBody] ParametriRicercaRadioamatore parametri)
        {
            try
            {

                #region Validazione Input
                if (parametri == null)
                {
                    throw new ValidationException($"{nameof(parametri)} non valorizzato");
                }

                #endregion
                else
                {
                    //List<RadioamatoreDto> objCf = await _radioamatoreBl.GetRadioamatoriByParametriAsync(parametri);
                    List<RadioamatoreDto> objCf = await _radioamatoreBl.GetRadioamatoriByIspettoratoConGestioneSubentriAsync(parametri, parametri.Ispettorato);
                    if (objCf != null && objCf.Any())
                    {
                        return Ok(objCf);
                    }
                }

                return Ok(new List<RadioamatoreDto>());
            }
            catch (ValidationException ex)
            {
                return BadRequest(ex);
            }
            catch (Refit.ApiException ex)
            {
                throw new APIException(ex.Message, ex.StackTrace, ex.Uri.AbsoluteUri, ex.Content, ex.Headers);
            }
            catch (Exception ex)
            {
                // Per recuperare le eccezioni interne alla API Common invocata dalla corrente API di Misura
                if (ex is APIException) throw;

                throw new APIException(ex.Message, ex.StackTrace, ex.InnerException?.Message, ex.InnerException?.StackTrace);
            }
        }

        /// <summary>
        /// Cerca nominativi per parametri di filtro con logica di subentro ispettorati motivo per cui l'id dello spettorato è obbligatorio
        /// </summary>
        /// <param name="tipoApplicazione"></param>
        /// <param name="idSpettorato"></param>
        /// <param name="parametri"></param>
        /// <returns></returns>
        /// <exception cref="APIException"></exception>
        [HttpPost("FiltroRadioamatoriByIspettoratoConGestioneSubentri", Name = "FiltroRadioamatoriByIspettoratoConGestioneSubentri")]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        public async Task<IActionResult> FiltroRadioamatoriByIspettoratoConGestioneSubentri(
            [FromHeader(Name = HttpHeaderParamsName.HEADER_PARAM_TIPO_APPLICAZIONE)] string tipoApplicazione,
            [FromHeader(Name = "x-id-ispettorato")] string idSpettorato,
            [FromBody] ParametriRicercaRadioamatoreBase parametri)
        {
            try
            {

                #region Validazione Input
                if (parametri == null)
                {
                    throw new ValidationException($"{nameof(parametri)} non valorizzato");
                }

                if (string.IsNullOrWhiteSpace(idSpettorato))
                {
                    throw new ValidationException($"{nameof(idSpettorato)} non valorizzato");
                }

                #endregion

                List<RadioamatoreDto> objCf = await _radioamatoreBl.GetRadioamatoriByIspettoratoConGestioneSubentriAsync(parametri, idSpettorato);
                if (objCf != null && objCf.Any())
                {
                    return Ok(objCf);
                }


                return Ok(new List<RadioamatoreDto>());
            }
            catch (ValidationException ex)
            {
                return BadRequest(ex);
            }
            catch (Refit.ApiException ex)
            {
                throw new APIException(ex.Message, ex.StackTrace, ex.Uri.AbsoluteUri, ex.Content, ex.Headers);
            }
            catch (Exception ex)
            {
                // Per recuperare le eccezioni interne alla API Common invocata dalla corrente API di Misura
                if (ex is APIException) throw;

                throw new APIException(ex.Message, ex.StackTrace, ex.InnerException?.Message, ex.InnerException?.StackTrace);
            }
        }

        [HttpPost("SetScadenza", Name = "SetScadenzaNominativi")]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        public async Task<IActionResult> SetScadenzaNominativi([FromHeader(Name = Constants.HEADER_IS_READONLY)] bool? isReadOnly)
        {
            try
            {
                #region Validazione Input

                if (!isReadOnly.HasValue)
                {
                    throw new ValidationException($"{Constants.HEADER_IS_READONLY} è obbligatorio");
                }

                #endregion

                var result = await _radioamatoreBl.SetScadenzaNominativiAsync(isReadOnly.Value);

                return result.Any() ? Ok(result) : NoContent();
            }
            catch (ValidationException ex)
            {
                return BadRequest(ex);
            }
            catch (Refit.ApiException ex)
            {
                throw new APIException(ex.Message, ex.StackTrace, ex.Uri.AbsoluteUri, ex.Content, ex.Headers);
            }
            catch (Exception ex)
            {
                // Per recuperare le eccezioni interne alla API Common invocata dalla corrente API di Misura
                if (ex is APIException) throw;

                throw new APIException(ex.Message, ex.StackTrace, ex.InnerException?.Message, ex.InnerException?.StackTrace, new { isReadOnly });
            }
        }

        [HttpPost("idRadioamatore/{idRadioamatore}/datarilascio", Name = "UpdateDataRilascioAsync")]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        public async Task<IActionResult> UpdateDataRilascioAsync(
            [FromRoute] int idRadioamatore,
            [FromBody] CodiceFiscaleDataRilascio requestBody)
        {
            try
            {

                #region Validazione Input

                var validator = new CodiceFiscaleDataRilascioValidator(idRadioamatore, _conf, _radioamatoreBl, Operation.UpdateDataRilascio);
                var validationResult = validator.Validate(requestBody);
                if (!validationResult.IsValid)
                {
                    throw new ValidationException(validationResult.Errors);
                }

                #endregion

                await _radioamatoreBl.UpdateDataRilascioAsync(idRadioamatore, requestBody);
                return Ok();
            }
            catch (ValidationException ex)
            {
                return BadRequest(ex);
            }
            catch (Refit.ApiException ex)
            {
                throw new APIException(ex.Message, ex.StackTrace, ex.Uri.AbsoluteUri, ex.Content, ex.Headers);
            }
            catch (Exception ex)
            {
                // Per recuperare le eccezioni interne alla API Common invocata dalla corrente API di Misura
                if (ex is APIException) throw;

                throw new APIException(ex.Message, ex.StackTrace, ex.InnerException?.Message, ex.InnerException?.StackTrace);
            }
        }

        [HttpPost("idRadioamatore/{idRadioamatore}/codicefiscale", Name = "UpdateCodiceFiscaleAsync")]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        public async Task<IActionResult> UpdateCodiceFiscaleAsync(
            [FromRoute] int idRadioamatore,
            [FromBody] CodiceFiscaleDataRilascio requestBody)
        {
            try
            {

                #region Validazione Input

                var validator = new CodiceFiscaleDataRilascioValidator(idRadioamatore, _conf, _radioamatoreBl, Operation.UpdateCodiceFiscale);
                var validationResult = validator.Validate(requestBody);
                if (!validationResult.IsValid)
                {
                    throw new ValidationException(validationResult.Errors);
                }

                #endregion

                await _radioamatoreBl.UpdateCodiceFiscaleAsync(idRadioamatore, requestBody);
                return Ok();
            }
            catch (ValidationException ex)
            {
                return BadRequest(ex);
            }
            catch (Refit.ApiException ex)
            {
                throw new APIException(ex.Message, ex.StackTrace, ex.Uri.AbsoluteUri, ex.Content, ex.Headers);
            }
            catch (Exception ex)
            {
                // Per recuperare le eccezioni interne alla API Common invocata dalla corrente API di Misura
                if (ex is APIException) throw;

                throw new APIException(ex.Message, ex.StackTrace, ex.InnerException?.Message, ex.InnerException?.StackTrace);
            }
        }

        [HttpPost("ProtocolloUscita", Name = "ProtocolloUscitaNominativo")]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        public async Task<IActionResult> ProtocolloUscitaNominativo([FromBody] ProtocolloUscitaDto dto)
        {
            try
            {
                #region Validazione Input

                if (dto == null)
                {
                    throw new ValidationException($"{nameof(dto)} non valorizzato");
                }

                var validatorsFactory = new GenericValidatorsFactory<ProtocolloUscitaDto>(this._conf, this._radioamatoreBl, "RAD");
                validatorsFactory.Validate(dto);
                List<ValidationFailure> failures = validatorsFactory.GetFailures();
                if (failures.Count > 0)
                {
                    throw new ValidationException(failures);
                }

                #endregion

                var result = await _radioamatoreBl.SetProtocolloUscitaNominativoAsync(dto);

                return result > 0 ? Ok(new { idRadioamatore = result }) : NoContent();
            }
            catch (ValidationException ex)
            {
                return BadRequest(ex);
            }
            catch (Refit.ApiException ex)
            {
                throw new APIException(ex.Message, ex.StackTrace, ex.Uri.AbsoluteUri, ex.Content, ex.Headers);
            }
            catch (Exception ex)
            {
                // Per recuperare le eccezioni interne alla API Common invocata dalla corrente API di Misura
                if (ex is APIException) throw;

                throw new APIException(ex.Message, ex.StackTrace, ex.InnerException?.Message, ex.InnerException?.StackTrace, new { dto });
            }
        }


        #endregion

        #region PATCH

        [HttpPatch("CodiceFiscaleDataRilascio/{idRadioamatore}", Name = "CodiceFiscaleDataRilascio")]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        public async Task<IActionResult> UpdateCodiceFiscaleDataRilascio(
            [FromHeader(Name = HttpHeaderParamsName.HEADER_PARAM_TIPO_APPLICAZIONE)] string tipoApplicazione,
            [FromBody] CodiceFiscaleDataRilascio codiceFiscaleProtocolloDataRilascio,
            [FromRoute] int idRadioamatore)
        {
            try
            {

                #region Validazione Input
                if (codiceFiscaleProtocolloDataRilascio == null)
                {
                    throw new ValidationException($"{nameof(codiceFiscaleProtocolloDataRilascio)} non valorizzato");
                }

                #endregion
                else
                {
                    await _radioamatoreBl.UpdateCodiceFiscaleDataRilascio(idRadioamatore, codiceFiscaleProtocolloDataRilascio);
                    return Ok();

                }

            }
            catch (ValidationException ex)
            {
                return BadRequest(ex);
            }
            catch (Refit.ApiException ex)
            {
                throw new APIException(ex.Message, ex.StackTrace, ex.Uri.AbsoluteUri, ex.Content, ex.Headers);
            }
            catch (Exception ex)
            {
                // Per recuperare le eccezioni interne alla API Common invocata dalla corrente API di Misura
                if (ex is APIException) throw;

                throw new APIException(ex.Message, ex.StackTrace, ex.InnerException?.Message, ex.InnerException?.StackTrace);
            }
        }

        #endregion

    }
}

