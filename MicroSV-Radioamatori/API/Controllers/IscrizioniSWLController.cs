﻿using FluentValidation;
using FluentValidation.Results;
using Inv.Framework.Api.DTO;
using Invitalia.Misure.FiltriAPI;
using Invitalia.Misure.FiltriAPI.Constants;
using Invitalia.Misure.Standard2.Utilities.Extensions;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using MicroSV_Radioamatori.API.Validators;
using MicroSV_Radioamatori.BL.DTOs;
using MicroSV_Radioamatori.BL.Enums;
using MicroSV_Radioamatori.BL.Interfaces;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Threading.Tasks;

namespace MicroSV_Radioamatori.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [ServiceFilter(typeof(TraceControllerActionsFilter))]
    public class IscrizioniSWLController : ControllerBase
    {
        private readonly IConfiguration _conf;
        private readonly IRadiamatoreBl _radioamatoreBl;

        public IscrizioniSWLController(IConfiguration conf, IRadiamatoreBl radioamatoreBl)
        {
            this._conf = conf;
            this._radioamatoreBl = radioamatoreBl;
        }

        #region GET

        [HttpGet("id/{idIscrizioneSWL}", Name = "GetIscrizioneSWLById")]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        public async Task<IActionResult> GetIscrizioneSWLById(
            [FromHeader(Name = HttpHeaderParamsName.HEADER_PARAM_TIPO_APPLICAZIONE)] string tipoApplicazione,
            [FromRoute] int? idIscrizioneSWL)
        {

            try
            {
                #region Validazione Input

                if (idIscrizioneSWL == null)
                {
                    throw new ValidationException($"{nameof(idIscrizioneSWL)} non valorizzato");
                }

                #endregion

                var result = await _radioamatoreBl.GetIscrizioneSWLByIdAsync((int)idIscrizioneSWL);

                return Ok(result);

            }
            catch (ValidationException ex)
            {
                return BadRequest(ex);
            }
            catch (Refit.ApiException ex)
            {
                throw new APIException(ex.Message, ex.StackTrace, ex.Uri.AbsoluteUri, ex.Content, ex.Headers);
            }
            catch (Exception ex)
            {
                // Per recuperare le eccezioni interne alla API Common invocata dalla corrente API 
                if (ex is APIException) throw;

                throw new APIException(ex.Message, ex.StackTrace, ex.InnerException?.Message, ex.InnerException?.StackTrace, new { idIscrizioneSWL });
            }
        }

        #endregion

        #region POST


        [HttpPost("ricerca", Name = "GetIscrizioniSWLByFilter")]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        public async Task<IActionResult> GetIscrizioniSWLByFilter(
            [FromHeader(Name = HttpHeaderParamsName.HEADER_PARAM_TIPO_APPLICAZIONE)] string tipoApplicazione,
            [FromBody] IscrizioneSWLFilterDto filtro)
        {

            try
            {
                #region Validazione Input

                if (filtro == null)
                {
                    throw new ValidationException($"{nameof(filtro)} non valorizzato");
                }

                #endregion

                //var result = await _radioamatoreBl.GetIscrizioniSWLByFilterAsync(filtro);
                var result = await _radioamatoreBl.GetIscrizioniSWLByIspettoratoConGestioneSubentriAsync(filtro, filtro.IdIspettorato);
                return Ok(result);

            }
            catch (ValidationException ex)
            {
                return BadRequest(ex);
            }
            catch (Refit.ApiException ex)
            {
                throw new APIException(ex.Message, ex.StackTrace, ex.Uri.AbsoluteUri, ex.Content, ex.Headers);
            }
            catch (Exception ex)
            {
                // Per recuperare le eccezioni interne alla API Common invocata dalla corrente API 
                if (ex is APIException) throw;

                throw new APIException(ex.Message, ex.StackTrace, ex.InnerException?.Message, ex.InnerException?.StackTrace, filtro);
            }
        }

        [HttpPost("ricercaConGestioneSubentri", Name = "GetIscrizioniSWLByIspettoratoFilter")]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        public async Task<IActionResult> GetIscrizioniSWLByIspettoratoFilter(
            [FromHeader(Name = HttpHeaderParamsName.HEADER_PARAM_TIPO_APPLICAZIONE)] string tipoApplicazione,
            [FromHeader(Name = "x-id-ispettorato")] string idSpettorato,
            [FromBody] IscrizioneSWLFilterDtoBase filtro)
        {

            try
            {
                #region Validazione Input

                if (filtro == null)
                {
                    throw new ValidationException($"{nameof(filtro)} non valorizzato");
                }

                if (string.IsNullOrWhiteSpace(idSpettorato))
                {
                    throw new ValidationException($"{nameof(idSpettorato)} non valorizzato");
                }

                #endregion

                var result = await _radioamatoreBl.GetIscrizioniSWLByIspettoratoConGestioneSubentriAsync(filtro, idSpettorato);
                return Ok(result);

            }
            catch (ValidationException ex)
            {
                return BadRequest(ex);
            }
            catch (Refit.ApiException ex)
            {
                throw new APIException(ex.Message, ex.StackTrace, ex.Uri.AbsoluteUri, ex.Content, ex.Headers);
            }
            catch (Exception ex)
            {
                // Per recuperare le eccezioni interne alla API Common invocata dalla corrente API 
                if (ex is APIException) throw;

                throw new APIException(ex.Message, ex.StackTrace, ex.InnerException?.Message, ex.InnerException?.StackTrace, filtro);
            }
        }

        [HttpPost("Insert", Name = "InsertIscrizioneSWLFrontEnd")]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        public async Task<IActionResult> InsertIscrizioneSWLFrontEnd([FromHeader(Name = HttpHeaderParamsName.HEADER_PARAM_ID_RISORSA)] string idRisorsa,
                                                        [FromBody] IscrizioneSWLDto dto)
        {
            return await InsertIscrizioneSWLAsync(idRisorsa, dto, RequestOrigin.FrontEnd);
        }

        private async Task<IActionResult> InsertIscrizioneSWLAsync(string idRisorsa, IscrizioneSWLDto dto, RequestOrigin requestOrigin)
        {
            try
            {
                #region Validazione Input

                if (dto == null)
                {
                    throw new ValidationException($"{nameof(dto)} non valorizzato");
                }

                var validator = new IscrizioneSWLDTOValidator(_conf, _radioamatoreBl, requestOrigin, Operation.Insert);
                var validationResult = validator.Validate(dto);
                if (!validationResult.IsValid)
                {
                    throw new ValidationException(validationResult.Errors);
                }

                #endregion

                var result = await _radioamatoreBl.InsertIscrizioneSWLAsync(idRisorsa, dto, requestOrigin);
                return Ok(new { IdIscrizioneSWL = result });

            }
            catch (ValidationException ex)
            {
                return BadRequest(ex);
            }
            catch (Refit.ApiException ex)
            {
                throw new APIException(ex.Message, ex.StackTrace, ex.Uri.AbsoluteUri, ex.Content, ex.Headers);
            }
            catch (Exception ex)
            {
                // Per recuperare le eccezioni interne alla API Common invocata dalla corrente API 
                if (ex is APIException) throw;

                throw new APIException(ex.Message, ex.StackTrace, ex.InnerException?.Message, ex.InnerException?.StackTrace, dto);
            }
        }

        [HttpPost("Cancella", Name = "EliminaIscrizioneSWL")]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        public async Task<IActionResult> EliminaIscrizioneSWL([FromBody] IscrizioneSWLDto dto)
        {
            return await GestisciCambioStato(dto, EnumStatoIscrizioneSWL.Cancellata);
        }

        [HttpPost("ProtocolloEntrata", Name = "ProtocolloEntrataIscrizioneSWL")]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        public async Task<IActionResult> ProtocolloEntrata(
            [FromHeader(Name = HttpHeaderParamsName.HEADER_PARAM_TIPO_APPLICAZIONE)] string tipoApplicazione,
            [FromBody] SetProtocolloMise datiProtocollo)
        {
            try
            {
                DateTime dataProtocollo;

                if (string.IsNullOrWhiteSpace(datiProtocollo.CodiceDomandaFE))
                {
                    throw new ValidationException($"{nameof(datiProtocollo.CodiceDomandaFE)} non valorizzato");
                }
                if (!DateTime.TryParseExact(datiProtocollo.DataProtocollo, "dd/MM/yyyy HH:mm:ss", CultureInfo.InvariantCulture, DateTimeStyles.None, out dataProtocollo))
                {
                    throw new ValidationException($"{nameof(datiProtocollo.DataProtocollo)} non valorizzato oppure non è nel formato atteso (dd/MM/yyyy HH:mm:ss)");
                }
                if (string.IsNullOrWhiteSpace(datiProtocollo.CodiceProtocollo))
                {
                    throw new ValidationException($"{nameof(datiProtocollo.CodiceProtocollo)} non valorizzato");
                }

                var affectedRows = await this._radioamatoreBl.SetProtocolloIscrizioneSWLAsync(datiProtocollo.CodiceDomandaFE, dataProtocollo, datiProtocollo.CodiceProtocollo, datiProtocollo.NumeroIscrizioneSWL);

                return Ok(affectedRows);
            }
            catch (ValidationException ex)
            {
                return BadRequest(ex);
            }
            catch (Refit.ApiException ex)
            {
                throw new APIException(ex.Message, ex.StackTrace, ex.Uri.AbsoluteUri, ex.Content, ex.Headers);
            }
            catch (Exception ex)
            {
                // Per recuperare le eccezioni interne alla API Common invocata dalla corrente API di Misura
                if (ex is APIException) throw;

                throw new APIException(ex.Message, ex.StackTrace, ex.InnerException?.Message, ex.InnerException?.StackTrace, new { datiProtocollo });
            }
        }

        [HttpPost("ProtocolloUscita", Name = "ProtocolloUscitaIscrizioneSWL")]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        public async Task<IActionResult> ProtocolloUscita([FromHeader(Name = HttpHeaderParamsName.HEADER_PARAM_ID_RISORSA)] string idRisorsa,
                                                            [FromBody] ProtocolloUscitaDto dto)
        {
            try
            {
                #region Validazione Input

                if (dto == null)
                {
                    throw new ValidationException($"{nameof(dto)} non valorizzato");
                }

                if (string.IsNullOrWhiteSpace(idRisorsa))
                {
                    throw new ValidationException($"{HttpHeaderParamsName.HEADER_PARAM_ID_RISORSA} è obbligatorio");
                }

                var validatorsFactory = new GenericValidatorsFactory<ProtocolloUscitaDto>(this._conf, this._radioamatoreBl, "RAD5");
                validatorsFactory.Validate(dto);

                List<ValidationFailure> failures = validatorsFactory.GetFailures();
                if (failures.Count > 0)
                {
                    throw new ValidationException(failures);
                }

                #endregion

                var result = await _radioamatoreBl.SetProtocolloUscitaIscrizioneSWLAsync(idRisorsa, dto);

                return Ok(new { IdIscrizioneSWL = result });
            }
            catch (ValidationException ex)
            {
                return BadRequest(ex);
            }
            catch (Refit.ApiException ex)
            {
                throw new APIException(ex.Message, ex.StackTrace, ex.Uri.AbsoluteUri, ex.Content, ex.Headers);
            }
            catch (Exception ex)
            {
                // Per recuperare le eccezioni interne alla API Common invocata dalla corrente API di Misura
                if (ex is APIException) throw;

                throw new APIException(ex.Message, ex.StackTrace, ex.InnerException?.Message, ex.InnerException?.StackTrace, new { dto });
            }
        }
        #endregion

        #region Private methods

        private async Task<IActionResult> GestisciCambioStato(IscrizioneSWLDto dto, EnumStatoIscrizioneSWL statoIscrizioneSWL)
        {
            try
            {
                #region Validazione Input

                if (dto == null)
                {
                    throw new ValidationException($"{nameof(dto)} non valorizzato");
                }

                dto.StatoIscrizioneSWL = new StatoIscrizioneSWLDto() { Id = (int)statoIscrizioneSWL, Descrizione = statoIscrizioneSWL.GetAttributeDescription() };
                dto.IdStatoIscrizioneSWL = (int)statoIscrizioneSWL;

                var validator = new IscrizioneSWLDTOValidator(_conf, _radioamatoreBl, RequestOrigin.NotSpecified, Operation.CambioStato);
                var validationResult = validator.Validate(dto);
                if (!validationResult.IsValid)
                {
                    throw new ValidationException(validationResult.Errors);
                }

                #endregion

                var result = await _radioamatoreBl.GestisciCambioStatoIscrizioneSWLAsync(dto, statoIscrizioneSWL);

                return Ok(new { IdIscrizioneSWL = result });
            }
            catch (ValidationException ex)
            {
                return BadRequest(ex);
            }
            catch (Refit.ApiException ex)
            {
                throw new APIException(ex.Message, ex.StackTrace, ex.Uri.AbsoluteUri, ex.Content, ex.Headers);
            }
            catch (Exception ex)
            {
                // Per recuperare le eccezioni interne alla API Common invocata dalla corrente API 
                if (ex is APIException) throw;

                throw new APIException(ex.Message, ex.StackTrace, ex.InnerException?.Message, ex.InnerException?.StackTrace, dto);
            }
        }

        #endregion
    }
}
