﻿using FluentValidation;
using FluentValidation.Results;
using Inv.Framework.Api.DTO;
using Invitalia.Misure.FiltriAPI;
using Invitalia.Misure.FiltriAPI.Constants;
using Invitalia.Misure.Standard2.Utilities.Extensions;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using MicroSV_Radioamatori.API.Validators;
using MicroSV_Radioamatori.BL.DTOs;
using MicroSV_Radioamatori.BL.Enums;
using MicroSV_Radioamatori.BL.Interfaces;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Reflection.Metadata;
using System.Threading.Tasks;

namespace MicroSV_Radioamatori.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [ServiceFilter(typeof(TraceControllerActionsFilter))]
    public class PatentiController : ControllerBase
    {
        private readonly IConfiguration _conf;
        private readonly IRadiamatoreBl _radioamatoreBl;

        public PatentiController(IConfiguration conf, IRadiamatoreBl radioamatoreBl)
        {
            this._conf = conf;
            this._radioamatoreBl = radioamatoreBl;
        }

        #region GET

        [HttpGet("id/{idPatente}", Name = "GetPatenteById")]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        public async Task<IActionResult> GetPatenteById(
            [FromHeader(Name = HttpHeaderParamsName.HEADER_PARAM_TIPO_APPLICAZIONE)] string tipoApplicazione,
            [FromRoute] int? idPatente)
        {

            try
            {
                #region Validazione Input
                if (idPatente == null)
                {
                    throw new ValidationException($"{nameof(idPatente)} non valorizzato");
                }

                #endregion
                var result = await _radioamatoreBl.GetPatenteByIdAsync((int)idPatente);

                return Ok(result);

            }
            catch (ValidationException ex)
            {
                return BadRequest(ex);
            }
            catch (Refit.ApiException ex)
            {
                throw new APIException(ex.Message, ex.StackTrace, ex.Uri.AbsoluteUri, ex.Content, ex.Headers);
            }
            catch (Exception ex)
            {
                // Per recuperare le eccezioni interne alla API Common invocata dalla corrente API 
                if (ex is APIException) throw;

                throw new APIException(ex.Message, ex.StackTrace, ex.InnerException?.Message, ex.InnerException?.StackTrace, new { idPatente });
            }
        }

        #endregion

        #region POST


        [HttpPost("ricerca", Name = "GetPatentiByFilter")]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        public async Task<IActionResult> GetPatentiByFilter(
            [FromHeader(Name = HttpHeaderParamsName.HEADER_PARAM_TIPO_APPLICAZIONE)] string tipoApplicazione,
            [FromBody] PatenteFilterDto filtro)
        {

            try
            {
                #region Validazione Input

                if (filtro == null)
                {
                    throw new ValidationException($"{nameof(filtro)} non valorizzato");
                }

                #endregion

                //var result = await _radioamatoreBl.GetPatentiByFilterAsync(filtro);
                var result = await _radioamatoreBl.GetPatentiByIspettoratoConGestioneSubentriAsync(filtro);
                return Ok(result);

            }
            catch (ValidationException ex)
            {
                return BadRequest(ex);
            }
            catch (Refit.ApiException ex)
            {
                throw new APIException(ex.Message, ex.StackTrace, ex.Uri.AbsoluteUri, ex.Content, ex.Headers);
            }
            catch (Exception ex)
            {
                // Per recuperare le eccezioni interne alla API Common invocata dalla corrente API 
                if (ex is APIException) throw;

                throw new APIException(ex.Message, ex.StackTrace, ex.InnerException?.Message, ex.InnerException?.StackTrace, filtro);
            }
        }


        [HttpPost("ricercaConGestioneSubentri", Name = "GetPatentiByIspettoratoByFilter")]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        public async Task<IActionResult> GetPatentiByIspettoratoByFilter(
            [FromHeader(Name = HttpHeaderParamsName.HEADER_PARAM_TIPO_APPLICAZIONE)] string tipoApplicazione,
            [FromHeader(Name = "x-id-ispettorato")] string idSpettorato,
            [FromBody] PatenteFilterDto filtro)
        {

            try
            {
                #region Validazione Input

                if (filtro == null)
                {
                    throw new ValidationException($"{nameof(filtro)} non valorizzato");
                }

                #endregion
                filtro.IdIspettorato = idSpettorato;
                var result = await _radioamatoreBl.GetPatentiByIspettoratoConGestioneSubentriAsync(filtro);
                return Ok(result);

            }
            catch (ValidationException ex)
            {
                return BadRequest(ex);
            }
            catch (Refit.ApiException ex)
            {
                throw new APIException(ex.Message, ex.StackTrace, ex.Uri.AbsoluteUri, ex.Content, ex.Headers);
            }
            catch (Exception ex)
            {
                // Per recuperare le eccezioni interne alla API Common invocata dalla corrente API 
                if (ex is APIException) throw;

                throw new APIException(ex.Message, ex.StackTrace, ex.InnerException?.Message, ex.InnerException?.StackTrace, filtro);
            }
        }


        [HttpPost("backend/insert", Name = "InsertPatenteBackEnd")]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        public async Task<IActionResult> InsertPatenteBackEnd([FromHeader(Name = HttpHeaderParamsName.HEADER_PARAM_ID_RISORSA)] string idRisorsa,
                                                        [FromBody] PatenteDto dto)
        {
            return await InsertPatenteAsync(idRisorsa, dto, RequestOrigin.GEA);
        }

        [HttpPost("Insert", Name = "InsertPatenteFrontEnd")]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        public async Task<IActionResult> InsertPatenteFrontEnd([FromHeader(Name = HttpHeaderParamsName.HEADER_PARAM_ID_RISORSA)] string idRisorsa,
                                                        [FromBody] PatenteDto dto)
        {
            return await InsertPatenteAsync(idRisorsa, dto, RequestOrigin.FrontEnd);
        }

        [HttpPost("Update", Name = "UpdatePatente")]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        public async Task<IActionResult> UpdatePatente(
            [FromHeader(Name = HttpHeaderParamsName.HEADER_PARAM_TIPO_APPLICAZIONE)] string tipoApplicazione,
            [FromBody] PatenteDto dto)
        {

            try
            {
                #region Validazione Input

                if (dto == null)
                {
                    throw new ValidationException($"{nameof(dto)} non valorizzato");
                }

                PatenteDTOValidator validator = new PatenteDTOValidator(_conf, _radioamatoreBl, RequestOrigin.FrontEnd, Operation.Update);
                var validationResult = validator.Validate(dto);
                if (!validationResult.IsValid)
                {
                    throw new ValidationException(validationResult.Errors);
                }

                #endregion

                var result = await _radioamatoreBl.UpdatePatenteAsync(dto);
                if (result > 0)
                {
                    return Ok(new { IdPatente = result });
                }
                else
                {
                    return NoContent();
                }

            }
            catch (ValidationException ex)
            {
                return BadRequest(ex);
            }
            catch (Refit.ApiException ex)
            {
                throw new APIException(ex.Message, ex.StackTrace, ex.Uri.AbsoluteUri, ex.Content, ex.Headers);
            }
            catch (Exception ex)
            {
                // Per recuperare le eccezioni interne alla API Common invocata dalla corrente API 
                if (ex is APIException) throw;

                throw new APIException(ex.Message, ex.StackTrace, ex.InnerException?.Message, ex.InnerException?.StackTrace, dto);
            }
        }

        [HttpPost("AccogliRichiesta", Name = "AccogliRichiestaPatente")]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        public async Task<IActionResult> AccogliRichiestaPatente([FromBody] PatenteDto dto)
        {
            return await GestisciCambioStato(dto, StatoPatente.RichiestaAccolta);
        }

        [HttpPost("RigettaRichiesta", Name = "RigettaRichiestaPatente")]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        public async Task<IActionResult> RigettaRichiestaPatente([FromBody] PatenteDto dto)
        {
            return await GestisciCambioStato(dto, StatoPatente.RichiestaRigettata);
        }

        [HttpPost("EsameNonPassato", Name = "EsamePatenteNonPassato")]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        public async Task<IActionResult> BocciaPatente([FromBody] PatenteDto dto)
        {
            return await GestisciCambioStato(dto, StatoPatente.EsameNonPassato);
        }

        [HttpPost("Attiva", Name = "AttivaPatente")]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        public async Task<IActionResult> AttivaPatente([FromBody] PatenteDto dto)
        {
            return await GestisciCambioStato(dto, StatoPatente.Attiva);
        }

        [HttpPost("Cancella", Name = "CancellaPatente")]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        public async Task<IActionResult> CancellaPatente([FromBody] PatenteDto dto)
        {
            return await GestisciCambioStato(dto, StatoPatente.Cancellata);
        }

        [HttpPost("ProtocolloEntrata", Name = "ProtocolloEntrataPatenti")]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        public async Task<IActionResult> ProtocolloEntrata(
            [FromHeader(Name = HttpHeaderParamsName.HEADER_PARAM_TIPO_APPLICAZIONE)] string tipoApplicazione,
            [FromBody] SetProtocolloMise datiProtocollo)
        {
            try
            {
                DateTime dataProtocollo;

                if (string.IsNullOrWhiteSpace(datiProtocollo.CodiceDomandaFE))
                {
                    throw new ValidationException($"{nameof(datiProtocollo.CodiceDomandaFE)} non valorizzato");
                }
                if (!DateTime.TryParseExact(datiProtocollo.DataProtocollo, "dd/MM/yyyy HH:mm:ss", CultureInfo.InvariantCulture, DateTimeStyles.None, out dataProtocollo))
                {
                    throw new ValidationException($"{nameof(datiProtocollo.DataProtocollo)} non valorizzato oppure non è nel formato atteso (dd/MM/yyyy HH:mm:ss)");
                }
                if (string.IsNullOrWhiteSpace(datiProtocollo.CodiceProtocollo))
                {
                    throw new ValidationException($"{nameof(datiProtocollo.CodiceProtocollo)} non valorizzato");
                }

                var affectedRows = await this._radioamatoreBl.SetProtocolloPatenteAsync(datiProtocollo.CodiceDomandaFE, dataProtocollo, datiProtocollo.CodiceProtocollo);

                return Ok(affectedRows);
            }
            catch (ValidationException ex)
            {
                return BadRequest(ex);
            }
            catch (Refit.ApiException ex)
            {
                throw new APIException(ex.Message, ex.StackTrace, ex.Uri.AbsoluteUri, ex.Content, ex.Headers);
            }
            catch (Exception ex)
            {
                // Per recuperare le eccezioni interne alla API Common invocata dalla corrente API di Misura
                if (ex is APIException) throw;

                throw new APIException(ex.Message, ex.StackTrace, ex.InnerException?.Message, ex.InnerException?.StackTrace, new { datiProtocollo });
            }
        }

        [HttpPost("ProtocolloUscita", Name = "ProtocolloUscitaPatenti")]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        public async Task<IActionResult> ProtocolloUscita([FromHeader(Name = HttpHeaderParamsName.HEADER_PARAM_ID_RISORSA)] string idRisorsa,
                                                            [FromBody] ProtocolloUscitaDto dto)
        {
            try
            {
                #region Validazione Input

                if (dto == null)
                {
                    throw new ValidationException($"{nameof(dto)} non valorizzato");
                }

                if (string.IsNullOrWhiteSpace(idRisorsa))
                {
                    throw new ValidationException($"{HttpHeaderParamsName.HEADER_PARAM_ID_RISORSA} è obbligatorio");
                }

                var validatorsFactory = new GenericValidatorsFactory<ProtocolloUscitaDto>(this._conf, this._radioamatoreBl, "RAD2");
                validatorsFactory.Validate(dto);

                List<ValidationFailure> failures = validatorsFactory.GetFailures();
                if (failures.Count > 0)
                {
                    throw new ValidationException(failures);
                }

                #endregion

                var result = await _radioamatoreBl.SetProtocolloUscitaPatenteAsync(idRisorsa, dto);

                IActionResult actionResult = string.IsNullOrWhiteSpace(result) ? NoContent() : Ok(new { CodicePatente = result });

                return actionResult;
            }
            catch (ValidationException ex)
            {
                return BadRequest(ex);
            }
            catch (Refit.ApiException ex)
            {
                throw new APIException(ex.Message, ex.StackTrace, ex.Uri.AbsoluteUri, ex.Content, ex.Headers);
            }
            catch (Exception ex)
            {
                // Per recuperare le eccezioni interne alla API Common invocata dalla corrente API di Misura
                if (ex is APIException) throw;

                throw new APIException(ex.Message, ex.StackTrace, ex.InnerException?.Message, ex.InnerException?.StackTrace, new { dto });
            }
        }


        [HttpPost("UploadDocumento", Name = "UploadDocumentoAsync")]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        public async Task<IActionResult> UploadDocumentoAsync([FromHeader(Name = HttpHeaderParamsName.HEADER_PARAM_ID_RISORSA)] string idRisorsa,
                                                                [FromBody] PatenteDto dto)
        {
            try
            {
                #region Validazione Input

                if (string.IsNullOrWhiteSpace(idRisorsa))
                {
                    throw new ValidationException($"{HttpHeaderParamsName.HEADER_PARAM_ID_RISORSA} non valorizzato");
                }

                if (dto == null)
                {
                    throw new ValidationException($"{nameof(dto)} non valorizzato");
                }

                var validator = new PatenteDTOValidator(_conf, _radioamatoreBl, RequestOrigin.FrontEnd, Operation.UploadDocumenti);
                var validationResult = validator.Validate(dto);
                if (!validationResult.IsValid)
                {
                    throw new ValidationException(validationResult.Errors);
                }

                #endregion

                var result = await _radioamatoreBl.UploadDocumentoPatenteAsync(idRisorsa, dto);

                return Ok(result);
            }
            catch (ValidationException ex)
            {
                return BadRequest(ex);
            }
            catch (Refit.ApiException ex)
            {
                throw new APIException(ex.Message, ex.StackTrace, ex.Uri.AbsoluteUri, ex.Content, ex.Headers);
            }
            catch (Exception ex)
            {
                // Per recuperare le eccezioni interne alla API Common invocata dalla corrente API 
                if (ex is APIException) throw;

                throw new APIException(ex.Message, ex.StackTrace, ex.InnerException?.Message, ex.InnerException?.StackTrace, dto);
            }
        }

        #endregion

        #region Private methods
        private async Task<IActionResult> InsertPatenteAsync(string idRisorsa, PatenteDto dto, RequestOrigin requestOrigin)
        {
            try
            {
                #region Validazione Input

                if (dto == null)
                {
                    throw new ValidationException($"{nameof(dto)} non valorizzato");
                }

                var validator = new PatenteDTOValidator(_conf, _radioamatoreBl, requestOrigin, Operation.Insert);
                var validationResult = validator.Validate(dto);
                if (!validationResult.IsValid)
                {
                    throw new ValidationException(validationResult.Errors);
                }

                #endregion

                var result = await _radioamatoreBl.InsertPatenteAsync(idRisorsa, dto, requestOrigin);
                return Ok(new { CodicePatente = result });

            }
            catch (ValidationException ex)
            {
                return BadRequest(ex);
            }
            catch (Refit.ApiException ex)
            {
                throw new APIException(ex.Message, ex.StackTrace, ex.Uri.AbsoluteUri, ex.Content, ex.Headers);
            }
            catch (Exception ex)
            {
                // Per recuperare le eccezioni interne alla API Common invocata dalla corrente API 
                if (ex is APIException) throw;

                throw new APIException(ex.Message, ex.StackTrace, ex.InnerException?.Message, ex.InnerException?.StackTrace, dto);
            }
        }

        private async Task<IActionResult> GestisciCambioStato(PatenteDto dto, StatoPatente statoPatente)
        {
            try
            {
                #region Validazione Input

                if (dto == null)
                {
                    throw new ValidationException($"{nameof(dto)} non valorizzato");
                }

                dto.StatoPatente = new StatoPatenteDto() { Id = (int)statoPatente, Descrizione = statoPatente.GetAttributeDescription() };
                dto.IdStatoPatente = (int)statoPatente;

                var validator = new PatenteDTOValidator(_conf, _radioamatoreBl, RequestOrigin.NotSpecified, Operation.CambioStato);
                var validationResult = validator.Validate(dto);
                if (!validationResult.IsValid)
                {
                    throw new ValidationException(validationResult.Errors);
                }

                #endregion

                var result = await _radioamatoreBl.GestisciCambioStatoPatenteAsync(dto, statoPatente);

                IActionResult actionResult = string.IsNullOrWhiteSpace(result) ? NoContent() : Ok(new { CodicePatente = result });
                return actionResult;
            }
            catch (ValidationException ex)
            {
                return BadRequest(ex);
            }
            catch (Refit.ApiException ex)
            {
                throw new APIException(ex.Message, ex.StackTrace, ex.Uri.AbsoluteUri, ex.Content, ex.Headers);
            }
            catch (Exception ex)
            {
                // Per recuperare le eccezioni interne alla API Common invocata dalla corrente API 
                if (ex is APIException) throw;

                throw new APIException(ex.Message, ex.StackTrace, ex.InnerException?.Message, ex.InnerException?.StackTrace, dto);
            }
        }
        #endregion
    }
}
