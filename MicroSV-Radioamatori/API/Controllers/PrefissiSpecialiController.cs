﻿using FluentValidation;
using Inv.Framework.Api.DTO;
using Invitalia.Misure.FiltriAPI;
using Invitalia.Misure.FiltriAPI.Constants;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using MicroSV_Radioamatori.BL.Interfaces;
using System;
using System.Threading.Tasks;

namespace MicroSV_Radioamatori.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [ServiceFilter(typeof(TraceControllerActionsFilter))]
    public class PrefissiSpecialiController : ControllerBase
    {
        private readonly IConfiguration _conf;
        private readonly IRadiamatoreBl _radioamatoreBl;

        public PrefissiSpecialiController(IConfiguration conf, IRadiamatoreBl radioamatoreBl)
        {
            this._conf = conf;
            this._radioamatoreBl = radioamatoreBl;
        }

        [HttpGet("", Name = "GetPrefissiSpeciali")]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        public async Task<IActionResult> GetPrefissiSpeciali(
            [FromHeader(Name = HttpHeaderParamsName.HEADER_PARAM_TIPO_APPLICAZIONE)] string tipoApplicazione)
        {
            try
            {
                var objCf = await _radioamatoreBl.GetPrefissiNominativiSpecialiAsync();
                return Ok(objCf);
            }
            catch (ValidationException ex)
            {
                return BadRequest(ex);
            }
            catch (Refit.ApiException ex)
            {
                throw new APIException(ex.Message, ex.StackTrace, ex.Uri.AbsoluteUri, ex.Content, ex.Headers);
            }
            catch (Exception ex)
            {
                // Per recuperare le eccezioni interne alla API Common invocata dalla corrente API 
                if (ex is APIException) throw;

                throw new APIException(ex.Message, ex.StackTrace, ex.InnerException?.Message, ex.InnerException?.StackTrace);
            }
        }
    }
}
