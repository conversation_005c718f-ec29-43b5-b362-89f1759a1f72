﻿using FluentValidation;
using FluentValidation.Results;
using Inv.Framework.Api.BL.DTO.Interfaces;
using Inv.Framework.Api.DTO;
using Invitalia.Misure.FiltriAPI;
using Invitalia.Misure.FiltriAPI.Constants;
using Invitalia.Misure.Standard2.Utilities.Extensions;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using MicroSV_Radioamatori.API.Validators;
using MicroSV_Radioamatori.BL.Common;
using MicroSV_Radioamatori.BL.DTOs;
using MicroSV_Radioamatori.BL.Enums;
using MicroSV_Radioamatori.BL.Interfaces;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Threading.Tasks;

namespace MicroSV_Radioamatori.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [ServiceFilter(typeof(TraceControllerActionsFilter))]
    public class AutorizzazioniSRController : ControllerBase
    {
        private readonly IConfiguration _conf;
        private readonly IRadiamatoreBl _radioamatoreBl;
        private const string HEADER_PARAM_ORIGIN = "X-Origin";

        public AutorizzazioniSRController(IConfiguration conf, IRadiamatoreBl radioamatoreBl)
        {
            this._conf = conf;
            this._radioamatoreBl = radioamatoreBl;
        }

        #region GET

        [HttpGet("NominativiInScadenza/codiceFiscale/{codiceFiscale}", Name = "GetNominativiAutorizzazioniSRInScadenza")]
        public async Task<IActionResult> GetNominativiAutorizzazioniSRInScadenza([FromRoute] string codiceFiscale)
        {
            if (string.IsNullOrEmpty(codiceFiscale))
                throw new ValidationException($"{nameof(codiceFiscale)} non valorizzato");
            var result = await _radioamatoreBl.GetNominativiAutorizzazioniSRInScadenzaByCodiceFiscale(codiceFiscale);
            return Ok(result);
        }

        #endregion


        #region POST

        [HttpPost("Insert", Name = "InsertAutorizzazoneSRCompleta")]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        public async Task<IActionResult> InsertAutorizzazoneSRCompleta([FromHeader(Name = HEADER_PARAM_ORIGIN)] string origin, [FromBody] AutorizzazioneSRCompletaDto dto)
        {
            try
            {
                #region Validazione Input

                if (dto == null)
                {
                    throw new ValidationException($"{nameof(dto)} non valorizzato");
                }

                RequestOrigin requestOrigin = RequestOrigin.NotSpecified;
                if (!string.IsNullOrWhiteSpace(origin) && !Enum.TryParse<RequestOrigin>(origin, out requestOrigin))
                {
                    throw new ValidationException($"{HEADER_PARAM_ORIGIN} {origin} non valido");
                }

                var validator = new AutorizzazioneSRCompletaDTOValidator(_conf, _radioamatoreBl, requestOrigin, Operation.Insert);
                var validationResult = validator.Validate(dto);
                if (!validationResult.IsValid)
                {
                    throw new ValidationException(validationResult.Errors);
                }

                #endregion

                var result = await _radioamatoreBl.InsertAutorizzazioneSRCompletaAsync(dto, Operation.Insert, requestOrigin);
                return Ok(result);

            }
            catch (ValidationException ex)
            {
                return BadRequest(ex);
            }
            catch (Refit.ApiException ex)
            {
                throw new APIException(ex.Message, ex.StackTrace, ex.Uri.AbsoluteUri, ex.Content, ex.Headers);
            }
            catch (Exception ex)
            {
                // Per recuperare le eccezioni interne alla API Common invocata dalla corrente API 
                if (ex is APIException) throw;

                throw new APIException(ex.Message, ex.StackTrace, ex.InnerException?.Message, ex.InnerException?.StackTrace, dto);
            }
        }

        [HttpPost("backend/radioamatore/insert", Name = "InsertNominativoAutorizzazoneSR")]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        public async Task<IActionResult> InsertNominativoAutorizzazoneSR([FromBody] RadioamatoreSRDto dto)
        {
            try
            {
                var requestOrigin = RequestOrigin.GEA;
                #region Validazione Input

                if (dto == null)
                {
                    throw new ValidationException($"{nameof(dto)} non valorizzato");
                }

                var validator = new RadioamatoreSRDTOValidator(_conf, _radioamatoreBl, requestOrigin, Operation.Insert);
                var validationResult = validator.Validate(dto);
                if (!validationResult.IsValid)
                {
                    throw new ValidationException(validationResult.Errors);
                }

                #endregion

                var codiceRegione = await _radioamatoreBl.GetCodiceRegioneByRegione(dto.Regione, dto.Provincia);
                dto.StatoNominativo = EnumStatoNominativo.ATTIVO.ToString();
                var result = await _radioamatoreBl.GeneraNominativoSR(dto, codiceRegione, generaNominativo: false);
                return Ok(new { CodiceNominativo = result.CODICE_NOMINATIVO });

            }
            catch (ValidationException ex)
            {
                return BadRequest(ex);
            }
            catch (Refit.ApiException ex)
            {
                throw new APIException(ex.Message, ex.StackTrace, ex.Uri.AbsoluteUri, ex.Content, ex.Headers);
            }
            catch (Exception ex)
            {
                // Per recuperare le eccezioni interne alla API Common invocata dalla corrente API 
                if (ex is APIException) throw;

                throw new APIException(ex.Message, ex.StackTrace, ex.InnerException?.Message, ex.InnerException?.StackTrace, dto);
            }
        }

        [HttpPost("backend/autorizzazionesr/insert", Name = "InsertAutorizzazioneSR")]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        public async Task<IActionResult> InsertAutorizzazioneSR([FromBody] AutorizzazioneSRDto dto)
        {
            try
            {
                #region Validazione Input

                if (dto == null)
                {
                    throw new ValidationException($"{nameof(dto)} non valorizzato");
                }

                RequestOrigin requestOrigin = RequestOrigin.GEA;

                var validator = new AutorizzazioneSRDTOValidator(_conf, _radioamatoreBl, requestOrigin, Operation.Insert);
                var validationResult = validator.Validate(dto);
                if (!validationResult.IsValid)
                {
                    throw new ValidationException(validationResult.Errors);
                }

                #endregion

                var result = await _radioamatoreBl.InsertAutorizzazioneSRAndStazioneAsync(dto);
                return Ok(result);
            }
            catch (ValidationException ex)
            {
                return BadRequest(ex);
            }
            catch (Refit.ApiException ex)
            {
                throw new APIException(ex.Message, ex.StackTrace, ex.Uri.AbsoluteUri, ex.Content, ex.Headers);
            }
            catch (Exception ex)
            {
                // Per recuperare le eccezioni interne alla API Common invocata dalla corrente API 
                if (ex is APIException) throw;

                throw new APIException(ex.Message, ex.StackTrace, ex.InnerException?.Message, ex.InnerException?.StackTrace, dto);
            }
        }

        [HttpPost("ricerca", Name = "GetAutorizzazioniSRByFilter")]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        public async Task<IActionResult> GetAutorizzazioniSRByFilter(
            [FromHeader(Name = HttpHeaderParamsName.HEADER_PARAM_TIPO_APPLICAZIONE)] string tipoApplicazione,
            [FromBody] AutorizzazioneSRFilterDto filtro)
        {
            try
            {
                #region Validazione Input

                if (filtro == null)
                {
                    throw new ValidationException($"{nameof(filtro)} non valorizzato");
                }

                #endregion

                //var result = await _radioamatoreBl.GetAutorizzazioneSRByFilterAsync(filtro);
                var result = await _radioamatoreBl.GetAutorizzazioniSRByIspettoratoConGestioneSubentriAsync(filtro, filtro.IdIspettorato);
                return Ok(result);

            }
            catch (ValidationException ex)
            {
                return BadRequest(ex);
            }
            catch (Refit.ApiException ex)
            {
                throw new APIException(ex.Message, ex.StackTrace, ex.Uri.AbsoluteUri, ex.Content, ex.Headers);
            }
            catch (Exception ex)
            {
                // Per recuperare le eccezioni interne alla API Common invocata dalla corrente API 
                if (ex is APIException) throw;

                throw new APIException(ex.Message, ex.StackTrace, ex.InnerException?.Message, ex.InnerException?.StackTrace, filtro);
            }
        }

        [HttpPost("ricercaConGestioneSubentri", Name = "GetAutorizzazioniSRByIspettoratoConGestioneSubentriAsync")]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        public async Task<IActionResult> GetAutorizzazioniSRByIspettoratoConGestioneSubentriAsync(
            [FromHeader(Name = HttpHeaderParamsName.HEADER_PARAM_TIPO_APPLICAZIONE)] string tipoApplicazione,
            [FromHeader(Name = "x-id-ispettorato")] string idSpettorato,
            [FromBody] AutorizzazioneSRFilterDtoBase filtro)
        {
            try
            {
                #region Validazione Input

                if (filtro == null)
                {
                    throw new ValidationException($"{nameof(filtro)} non valorizzato");
                }

                if (string.IsNullOrWhiteSpace(idSpettorato))
                {
                    throw new ValidationException($"{nameof(idSpettorato)} non valorizzato");
                }

                #endregion

                var result = await _radioamatoreBl.GetAutorizzazioniSRByIspettoratoConGestioneSubentriAsync(filtro, idSpettorato);
                return Ok(result);

            }
            catch (ValidationException ex)
            {
                return BadRequest(ex);
            }
            catch (Refit.ApiException ex)
            {
                throw new APIException(ex.Message, ex.StackTrace, ex.Uri.AbsoluteUri, ex.Content, ex.Headers);
            }
            catch (Exception ex)
            {
                // Per recuperare le eccezioni interne alla API Common invocata dalla corrente API 
                if (ex is APIException) throw;

                throw new APIException(ex.Message, ex.StackTrace, ex.InnerException?.Message, ex.InnerException?.StackTrace, filtro);
            }
        }


        [HttpPost("ProtocolloEntrata", Name = "ProtocolloEntrataAutorizzazioniSR")]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        public async Task<IActionResult> ProtocolloEntrata(
            [FromHeader(Name = HttpHeaderParamsName.HEADER_PARAM_TIPO_APPLICAZIONE)] string TipoApplicazione,
            [FromBody] SetProtocolloMise datiProtocollo)
        {
            try
            {
                DateTime dataProtocollo;

                if (string.IsNullOrWhiteSpace(datiProtocollo.CodiceDomandaFE))
                {
                    throw new ValidationException($"{nameof(datiProtocollo.CodiceDomandaFE)} non valorizzato");
                }
                if (!DateTime.TryParseExact(datiProtocollo.DataProtocollo, "dd/MM/yyyy HH:mm:ss", CultureInfo.InvariantCulture, DateTimeStyles.None, out dataProtocollo))
                {
                    throw new ValidationException($"{nameof(datiProtocollo.DataProtocollo)} non valorizzato oppure non è nel formato atteso (dd/MM/yyyy HH:mm:ss)");
                }
                if (string.IsNullOrWhiteSpace(datiProtocollo.CodiceProtocollo))
                {
                    throw new ValidationException($"{nameof(datiProtocollo.CodiceProtocollo)} non valorizzato");
                }

                var affectedRows = await this._radioamatoreBl.SetProtocolloAutorizzazioneSRAsync(datiProtocollo.CodiceDomandaFE, dataProtocollo, datiProtocollo.CodiceProtocollo);

                return Ok(affectedRows);
            }
            catch (ValidationException ex)
            {
                return BadRequest(ex);
            }
            catch (Refit.ApiException ex)
            {
                throw new APIException(ex.Message, ex.StackTrace, ex.Uri.AbsoluteUri, ex.Content, ex.Headers);
            }
            catch (Exception ex)
            {
                // Per recuperare le eccezioni interne alla API Common invocata dalla corrente API di Misura
                if (ex is APIException) throw;

                throw new APIException(ex.Message, ex.StackTrace, ex.InnerException?.Message, ex.InnerException?.StackTrace, new { datiProtocollo });
            }
        }

        [HttpPost("SetScadenza", Name = "SetScadenzaAutorizzazioniSR")]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        public async Task<IActionResult> SetScadenzaAutorizzazioniSR([FromHeader(Name = Constants.HEADER_IS_READONLY)] bool? isReadOnly)
        {
            try
            {
                #region Validazione Input

                if (!isReadOnly.HasValue)
                {
                    throw new ValidationException($"{Constants.HEADER_IS_READONLY} è obbligatorio");
                }

                #endregion

                var result = await _radioamatoreBl.SetScadenzaAutorizzazioniSRAsync(isReadOnly.Value);

                return result.Any() ? Ok(result) : NoContent();
            }
            catch (ValidationException ex)
            {
                return BadRequest(ex);
            }
            catch (Refit.ApiException ex)
            {
                throw new APIException(ex.Message, ex.StackTrace, ex.Uri.AbsoluteUri, ex.Content, ex.Headers);
            }
            catch (Exception ex)
            {
                // Per recuperare le eccezioni interne alla API Common invocata dalla corrente API di Misura
                if (ex is APIException) throw;

                throw new APIException(ex.Message, ex.StackTrace, ex.InnerException?.Message, ex.InnerException?.StackTrace, new { isReadOnly });
            }
        }

        [HttpPost("Rinuncia", Name = "RinunciaAutorizzazioneSR")]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        public async Task<IActionResult> RinunciaAutorizzazioneSR([FromBody] AutorizzazioneSRCompletaDto dto)
        {
            return await GestisciCambioStatoAutorizzazioneSRAsync(dto, EnumStatoAutorizzazione.Rinunciata);
        }

        [HttpPost("Cancella", Name = "CancellaAutorizzazioneSR")]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        public async Task<IActionResult> CancellaAutorizzazioneSR([FromBody] AutorizzazioneSRCompletaDto dto)
        {
            return await GestisciCambioStatoAutorizzazioneSRAsync(dto, EnumStatoAutorizzazione.Cancellata);
        }

        [HttpPost("UpdateNominativoAssegnatario", Name = "UpdateNominativoAssegnatarioSR")]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        public async Task<IActionResult> UpdateNominativoAssegnatarioSR([FromBody] AutorizzazioneSRCompletaDto dto)
        {
            try
            {
                #region Validazione Input

                if (dto == null)
                {
                    throw new ValidationException($"{nameof(dto)} non valorizzato");
                }

                var validator = new AutorizzazioneSRCompletaDTOValidator(_conf, _radioamatoreBl, RequestOrigin.FrontEnd, Operation.UpdateNominativoAssegnatario);
                var validationResult = validator.Validate(dto);
                if (!validationResult.IsValid)
                {
                    throw new ValidationException(validationResult.Errors);
                }

                #endregion

                var result = await _radioamatoreBl.UpdateIdRadioamatoreSRAsync(dto.Radioamatore.IdRadioamatore, dto.Autorizzazione.Id.Value);

                return result > 0 ? Ok(new { idAutorizzazione = dto.Autorizzazione.Id }) : NoContent();
            }
            catch (ValidationException ex)
            {
                return BadRequest(ex);
            }
            catch (Refit.ApiException ex)
            {
                throw new APIException(ex.Message, ex.StackTrace, ex.Uri.AbsoluteUri, ex.Content, ex.Headers);
            }
            catch (Exception ex)
            {
                // Per recuperare le eccezioni interne alla API Common invocata dalla corrente API 
                if (ex is APIException) throw;

                throw new APIException(ex.Message, ex.StackTrace, ex.InnerException?.Message, ex.InnerException?.StackTrace, dto);
            }
        }

        [HttpPost("ProtocolloUscita", Name = "ProtocolloUscitaAutorizzazioniSR")]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        public async Task<IActionResult> ProtocolloUscitaAutorizzazioniSR([FromHeader(Name = HttpHeaderParamsName.HEADER_PARAM_ID_RISORSA)] string idRisorsa,
                                                    [FromBody] ProtocolloUscitaDto dto)
        {
            try
            {
                #region Validazione Input

                if (string.IsNullOrWhiteSpace(idRisorsa))
                {
                    throw new ValidationException($"{HttpHeaderParamsName.HEADER_PARAM_ID_RISORSA} è obbligatorio");
                }

                if (dto == null)
                {
                    throw new ValidationException($"{nameof(dto)} non valorizzato");
                }

                var validatorsFactory = new GenericValidatorsFactory<ProtocolloUscitaDto>(this._conf, this._radioamatoreBl, "RAD4");
                validatorsFactory.Validate(dto);
                List<ValidationFailure> failures = validatorsFactory.GetFailures();
                if (failures.Count > 0)
                {
                    throw new ValidationException(failures);
                }

                #endregion

                var result = await _radioamatoreBl.SetProtocolloUscitaAutorizzazioneSRAsync(dto);

                return result > 0 ? Ok(new { idAutorizzazione = dto.IdAutorizzazione }) : NoContent();
            }
            catch (ValidationException ex)
            {
                return BadRequest(ex);
            }
            catch (Refit.ApiException ex)
            {
                throw new APIException(ex.Message, ex.StackTrace, ex.Uri.AbsoluteUri, ex.Content, ex.Headers);
            }
            catch (Exception ex)
            {
                // Per recuperare le eccezioni interne alla API Common invocata dalla corrente API di Misura
                if (ex is APIException) throw;

                throw new APIException(ex.Message, ex.StackTrace, ex.InnerException?.Message, ex.InnerException?.StackTrace, new { dto });
            }
        }

        [HttpPost("radioamatore/ricerca", Name = "GetRadiomatoreSRByFilter")]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        public async Task<IActionResult> GetRadiomatoreSRByFilter(
        [FromHeader(Name = HttpHeaderParamsName.HEADER_PARAM_TIPO_APPLICAZIONE)] string tipoApplicazione,
        [FromBody] RadioamatoreSRFilterDto filtro)
            {
                try
                {
                    #region Validazione Input

                    if (filtro == null)
                    {
                        throw new ValidationException($"{nameof(filtro)} non valorizzato");
                    }

                    #endregion

                    var result = await _radioamatoreBl.GetRadioamatoriAndAutorizzazioniSRByFilterAsync(filtro);
                    return Ok(result);

                }
                catch (ValidationException ex)
                {
                    return BadRequest(ex);
                }
                catch (Refit.ApiException ex)
                {
                    throw new APIException(ex.Message, ex.StackTrace, ex.Uri.AbsoluteUri, ex.Content, ex.Headers);
                }
                catch (Exception ex)
                {
                    // Per recuperare le eccezioni interne alla API Common invocata dalla corrente API 
                    if (ex is APIException) throw;

                    throw new APIException(ex.Message, ex.StackTrace, ex.InnerException?.Message, ex.InnerException?.StackTrace, filtro);
                }
            }

        [HttpPost("backend/radioamatore/ricerca", Name = "GetRadiomatoreSRForBackendByFilter")]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        public async Task<IActionResult> GetRadiomatoreSRForBackendByFilter(
            [FromHeader(Name = HttpHeaderParamsName.HEADER_PARAM_TIPO_APPLICAZIONE)] string tipoApplicazione,
            [FromBody] RadioamatoreSRFilterDto filtro)
        {
            try
            {
                #region Validazione Input

                if (filtro == null)
                {
                    throw new ValidationException($"{nameof(filtro)} non valorizzato");
                }

                #endregion

                //var result = await _radioamatoreBl.GetRadiomatoriSRByFilter(filtro);
                var result = await _radioamatoreBl.GetRadiomatoriSRByIspettoratoFilter(filtro, filtro.IdIspettorato);
                return Ok(result);

            }
            catch (ValidationException ex)
            {
                return BadRequest(ex);
            }
            catch (Refit.ApiException ex)
            {
                throw new APIException(ex.Message, ex.StackTrace, ex.Uri.AbsoluteUri, ex.Content, ex.Headers);
            }
            catch (Exception ex)
            {
                // Per recuperare le eccezioni interne alla API Common invocata dalla corrente API 
                if (ex is APIException) throw;

                throw new APIException(ex.Message, ex.StackTrace, ex.InnerException?.Message, ex.InnerException?.StackTrace, filtro);
            }
        }

        [HttpPost("backend/radioamatore/ricercaConGestioneSubentri", Name = "")]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        public async Task<IActionResult> GetRadiomatoreSRForBackendByIspettoratoFilter(
            [FromHeader(Name = HttpHeaderParamsName.HEADER_PARAM_TIPO_APPLICAZIONE)] string tipoApplicazione,
            [FromHeader(Name = "x-id-ispettorato")] string idSpettorato,
            [FromBody] RadioamatoreSRFilterDtoBase filtro)
        {
            try
            {
                #region Validazione Input

                if (filtro == null)
                {
                    throw new ValidationException($"{nameof(filtro)} non valorizzato");
                }

                if (string.IsNullOrWhiteSpace(idSpettorato))
                {
                    throw new ValidationException($"{nameof(idSpettorato)} non valorizzato");
                }

                #endregion

                var result = await _radioamatoreBl.GetRadiomatoriSRByIspettoratoFilter(filtro, idSpettorato);
                return Ok(result);

            }
            catch (ValidationException ex)
            {
                return BadRequest(ex);
            }
            catch (Refit.ApiException ex)
            {
                throw new APIException(ex.Message, ex.StackTrace, ex.Uri.AbsoluteUri, ex.Content, ex.Headers);
            }
            catch (Exception ex)
            {
                // Per recuperare le eccezioni interne alla API Common invocata dalla corrente API 
                if (ex is APIException) throw;

                throw new APIException(ex.Message, ex.StackTrace, ex.InnerException?.Message, ex.InnerException?.StackTrace, filtro);
            }
        }


        [HttpPost("radioamatore/SetScadenza", Name = "SetScadenzaNominativiSR")]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        public async Task<IActionResult> SetScadenzaNominativiSR([FromHeader(Name = Constants.HEADER_IS_READONLY)] bool? isReadOnly)
        {
            try
            {
                #region Validazione Input

                if (!isReadOnly.HasValue)
                {
                    throw new ValidationException($"{Constants.HEADER_IS_READONLY} è obbligatorio");
                }

                #endregion

                var result = await _radioamatoreBl.SetScadenzaNominativiSRAsync(isReadOnly.Value);

                return result.Any() ? Ok(result) : NoContent();
            }
            catch (ValidationException ex)
            {
                return BadRequest(ex);
            }
            catch (Refit.ApiException ex)
            {
                throw new APIException(ex.Message, ex.StackTrace, ex.Uri.AbsoluteUri, ex.Content, ex.Headers);
            }
            catch (Exception ex)
            {
                // Per recuperare le eccezioni interne alla API Common invocata dalla corrente API di Misura
                if (ex is APIException) throw;

                throw new APIException(ex.Message, ex.StackTrace, ex.InnerException?.Message, ex.InnerException?.StackTrace, new { isReadOnly });
            }
        }

        [HttpPost("radioamatore/annulla", Name = "AnnullaNominativoSR")]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        public async Task<IActionResult> AnnullaNominativoSR([FromBody] RadioamatoreSRDto dto)
        {
            return await GestisciCambioStatoNominativoSRAsync(dto, EnumStatoNominativo.ANNULLATO);
        }


        #endregion

        #region private methods

        private async Task<IActionResult> GestisciCambioStatoAutorizzazioneSRAsync(AutorizzazioneSRCompletaDto dto, EnumStatoAutorizzazione statoAutorizzazione)
        {
            try
            {
                #region Validazione Input

                if (dto == null || dto.Autorizzazione == null)
                {
                    throw new ValidationException($"{nameof(dto)} non valorizzato");
                }

                dto.Autorizzazione.StatoAutorizzazione = new StatoAutorizzazioneDto() { Id = (int)statoAutorizzazione, Descrizione = statoAutorizzazione.GetAttributeDescription() };
                dto.Autorizzazione.IdStatoAutorizzazione = (short)statoAutorizzazione;

                var validator = new AutorizzazioneSRCompletaDTOValidator(_conf, _radioamatoreBl, RequestOrigin.NotSpecified, Operation.CambioStato);
                var validationResult = validator.Validate(dto);
                if (!validationResult.IsValid)
                {
                    throw new ValidationException(validationResult.Errors);
                }

                #endregion

                var result = await _radioamatoreBl.GestisciCambioStatoAutorizzazioneSRAsync(dto.Autorizzazione, statoAutorizzazione);

                return result > 0 ? Ok(new { idAutorizzazione = dto.Autorizzazione.Id }) : NoContent();
            }
            catch (ValidationException ex)
            {
                return BadRequest(ex);
            }
            catch (Refit.ApiException ex)
            {
                throw new APIException(ex.Message, ex.StackTrace, ex.Uri.AbsoluteUri, ex.Content, ex.Headers);
            }
            catch (Exception ex)
            {
                // Per recuperare le eccezioni interne alla API Common invocata dalla corrente API 
                if (ex is APIException) throw;

                throw new APIException(ex.Message, ex.StackTrace, ex.InnerException?.Message, ex.InnerException?.StackTrace, dto);
            }
        }


        private async Task<IActionResult> GestisciCambioStatoNominativoSRAsync(RadioamatoreSRDto dto, EnumStatoNominativo statoAutorizzazione)
        {
            try
            {
                #region Validazione Input

                if (dto == null)
                {
                    throw new ValidationException($"{nameof(dto)} non valorizzato");
                }

                var validator = new RadioamatoreSRDTOValidator(_conf, _radioamatoreBl, RequestOrigin.NotSpecified, Operation.CambioStato);
                var validationResult = validator.Validate(dto);
                if (!validationResult.IsValid)
                {
                    throw new ValidationException(validationResult.Errors);
                }

                #endregion

                var result = await _radioamatoreBl.GestisciCambioStatoNominativoSRAsync(dto, statoAutorizzazione);

                return result > 0 ? Ok(new { idRadioamatore = dto.IdRadioamatore }) : NoContent();
            }
            catch (ValidationException ex)
            {
                return BadRequest(ex);
            }
            catch (Refit.ApiException ex)
            {
                throw new APIException(ex.Message, ex.StackTrace, ex.Uri.AbsoluteUri, ex.Content, ex.Headers);
            }
            catch (Exception ex)
            {
                // Per recuperare le eccezioni interne alla API Common invocata dalla corrente API 
                if (ex is APIException) throw;

                throw new APIException(ex.Message, ex.StackTrace, ex.InnerException?.Message, ex.InnerException?.StackTrace, dto);
            }
        }

        #endregion
    }
}
