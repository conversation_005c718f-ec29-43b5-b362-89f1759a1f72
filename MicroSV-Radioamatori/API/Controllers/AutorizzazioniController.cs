﻿using FluentValidation;
using FluentValidation.Results;
using Inv.Framework.Api.DTO;
using Invitalia.Misure.FiltriAPI;
using Invitalia.Misure.FiltriAPI.Constants;
using Invitalia.Misure.Standard2.Utilities.Extensions;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.JSInterop.Infrastructure;
using MicroSV_Radioamatori.API.Validators;
using MicroSV_Radioamatori.BL.Common;
using MicroSV_Radioamatori.BL.DTOs;
using MicroSV_Radioamatori.BL.Enums;
using MicroSV_Radioamatori.BL.Interfaces;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Threading.Tasks;

namespace MicroSV_Radioamatori.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [ServiceFilter(typeof(TraceControllerActionsFilter))]
    public class AutorizzazioniController : ControllerBase
    {
        private readonly IConfiguration _conf;
        private readonly IRadiamatoreBl _radioamatoreBl;

        public AutorizzazioniController(IConfiguration conf, IRadiamatoreBl radioamatoreBl)
        {
            this._conf = conf;
            this._radioamatoreBl = radioamatoreBl;
        }

        #region GET

        [HttpGet("id/{idAutorizzazione}", Name = "GetAutorizzazioneById")]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        public async Task<IActionResult> GetAutorizzazioneById(
            [FromHeader(Name = HttpHeaderParamsName.HEADER_PARAM_TIPO_APPLICAZIONE)] string tipoApplicazione,
            [FromRoute] int? idAutorizzazione)
        {
            try
            {
                #region Validazione Input
                if (idAutorizzazione == null)
                {
                    throw new ValidationException($"{nameof(idAutorizzazione)} non valorizzato");
                }

                #endregion
                var result = await _radioamatoreBl.GetAutorizzazioneByIdAsync((int)idAutorizzazione);

                return Ok(result);

            }
            catch (ValidationException ex)
            {
                return BadRequest(ex);
            }
            catch (Refit.ApiException ex)
            {
                throw new APIException(ex.Message, ex.StackTrace, ex.Uri.AbsoluteUri, ex.Content, ex.Headers);
            }
            catch (Exception ex)
            {
                // Per recuperare le eccezioni interne alla API Common invocata dalla corrente API 
                if (ex is APIException) throw;

                throw new APIException(ex.Message, ex.StackTrace, ex.InnerException?.Message, ex.InnerException?.StackTrace, new { idAutorizzazione });
            }
        }

        [HttpGet("codicefiscale/{codiceFiscale}", Name = "GetAutorizzazioniByCF")]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        public async Task<IActionResult> GetAutorizzazioniByCF(
            [FromHeader(Name = HttpHeaderParamsName.HEADER_PARAM_TIPO_APPLICAZIONE)] string tipoApplicazione,
            [FromRoute] string codiceFiscale)
        {

            try
            {
                #region Validazione Input
                if (string.IsNullOrWhiteSpace(codiceFiscale))
                {
                    throw new ValidationException($"{nameof(codiceFiscale)} non valorizzato");
                }

                #endregion
                if (!string.IsNullOrWhiteSpace(codiceFiscale))
                {
                    List<AutorizzazioneDto> objCf = await _radioamatoreBl.GetAutorizzazioniByCodiceFiscaleAsync(codiceFiscale);
                    if (objCf != null && objCf.Any())
                    {
                        return Ok(objCf);
                    }
                }

                return Ok(new List<AutorizzazioneDto> ());

            }
            catch (ValidationException ex)
            {
                return BadRequest(ex);
            }
            catch (Refit.ApiException ex)
            {
                throw new APIException(ex.Message, ex.StackTrace, ex.Uri.AbsoluteUri, ex.Content, ex.Headers);
            }
            catch (Exception ex)
            {
                // Per recuperare le eccezioni interne alla API Common invocata dalla corrente API 
                if (ex is APIException) throw;

                throw new APIException(ex.Message, ex.StackTrace, ex.InnerException?.Message, ex.InnerException?.StackTrace, new { codiceFiscale });
            }
        }

        #endregion

        #region POST

        [HttpPost("ricerca", Name = "GetAutorizzazioniByFilter")]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        public async Task<IActionResult> GetAutorizzazioniByFilter(
            [FromHeader(Name = HttpHeaderParamsName.HEADER_PARAM_TIPO_APPLICAZIONE)] string tipoApplicazione,
            [FromBody] AutorizzazioneFilterDto filtro)
        {

            try
            {
                #region Validazione Input

                if (filtro == null)
                {
                    throw new ValidationException($"{nameof(filtro)} non valorizzato");
                }

                #endregion

                //var result = await _radioamatoreBl.GetAutorizzazioneByFilterAsync(filtro);
                var result = await _radioamatoreBl.GetAutorizzazioniByIspettoratoConGestioneSubentriAsync(filtro, filtro.IdIspettorato);
                return Ok(result);

            }
            catch (ValidationException ex)
            {
                return BadRequest(ex);
            }
            catch (Refit.ApiException ex)
            {
                throw new APIException(ex.Message, ex.StackTrace, ex.Uri.AbsoluteUri, ex.Content, ex.Headers);
            }
            catch (Exception ex)
            {
                // Per recuperare le eccezioni interne alla API Common invocata dalla corrente API 
                if (ex is APIException) throw;

                throw new APIException(ex.Message, ex.StackTrace, ex.InnerException?.Message, ex.InnerException?.StackTrace, filtro);
            }
        }

        [HttpPost("ricercaConGestioneSubentri", Name = "GetAutorizzazioniByIspettoratoConGestioneSubentriAsync")]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        public async Task<IActionResult> GetAutorizzazioniByIspettoratoConGestioneSubentriAsync(
            [FromHeader(Name = HttpHeaderParamsName.HEADER_PARAM_TIPO_APPLICAZIONE)] string tipoApplicazione,
            [FromHeader(Name = "x-id-ispettorato")] string idSpettorato,
            [FromBody] AutorizzazioneFilterDtoBase filtro)
        {

            try
            {
                #region Validazione Input

                if (filtro == null)
                {
                    throw new ValidationException($"{nameof(filtro)} non valorizzato");
                }

                if (string.IsNullOrWhiteSpace(idSpettorato))
                {
                    throw new ValidationException($"{nameof(idSpettorato)} non valorizzato");
                }

                #endregion

                var result = await _radioamatoreBl.GetAutorizzazioniByIspettoratoConGestioneSubentriAsync(filtro, idSpettorato);
                return Ok(result);

            }
            catch (ValidationException ex)
            {
                return BadRequest(ex);
            }
            catch (Refit.ApiException ex)
            {
                throw new APIException(ex.Message, ex.StackTrace, ex.Uri.AbsoluteUri, ex.Content, ex.Headers);
            }
            catch (Exception ex)
            {
                // Per recuperare le eccezioni interne alla API Common invocata dalla corrente API 
                if (ex is APIException) throw;

                throw new APIException(ex.Message, ex.StackTrace, ex.InnerException?.Message, ex.InnerException?.StackTrace, filtro);
            }
        }

        [HttpPost("backend/insert", Name = "InsertAutorizzazioneBackEnd")]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        public async Task<IActionResult> InsertAutorizzazioneBackEnd([FromHeader(Name = HttpHeaderParamsName.HEADER_PARAM_ID_RISORSA)] string idRisorsa,
                                                [FromBody] AutorizzazioneDto dto)
        {
            return await InsertAutorizzazioneAsync(idRisorsa, dto, RequestOrigin.GEA);
        }

        [HttpPost("Insert", Name = "InsertAutorizzazoneFrontEnd")]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        public async Task<IActionResult> InsertAutorizzazoneFrontEnd([FromHeader(Name = HttpHeaderParamsName.HEADER_PARAM_ID_RISORSA)] string idRisorsa,
                                                [FromBody] AutorizzazioneDto dto)
        {
            return await InsertAutorizzazioneAsync(idRisorsa, dto, RequestOrigin.FrontEnd);
        }

        [HttpPost("Update", Name = "UpdateAutorizzazione")]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        public async Task<IActionResult> UpdateAutorizzazione(
            [FromHeader(Name = HttpHeaderParamsName.HEADER_PARAM_TIPO_APPLICAZIONE)] string tipoApplicazione,
            [FromBody] AutorizzazioneDto dto)
        {

            try
            {
                #region Validazione Input

                if (dto == null)
                {
                    throw new ValidationException($"{nameof(dto)} non valorizzato");
                }

                var validator = new AutorizzazioneDTOValidator(_conf, _radioamatoreBl, RequestOrigin.FrontEnd, Operation.Update);
                var validationResult = validator.Validate(dto);
                if (!validationResult.IsValid)
                {
                    throw new ValidationException(validationResult.Errors);
                }

                #endregion

                var result = await _radioamatoreBl.UpdateAutorizzazioneAsync(dto);
                if (result > 0)
                {
                    return Ok(new { IdAutorizzazione = result });
                }
                else
                {
                    return NoContent();
                }

            }
            catch (ValidationException ex)
            {
                return BadRequest(ex);
            }
            catch (Refit.ApiException ex)
            {
                throw new APIException(ex.Message, ex.StackTrace, ex.Uri.AbsoluteUri, ex.Content, ex.Headers);
            }
            catch (Exception ex)
            {
                // Per recuperare le eccezioni interne alla API Common invocata dalla corrente API 
                if (ex is APIException) throw;

                throw new APIException(ex.Message, ex.StackTrace, ex.InnerException?.Message, ex.InnerException?.StackTrace, dto);
            }
        }

        [HttpPost("UpdateVisibilitaAttestato", Name = "UpdateVisibilitaAttestatoAutorizzazione")]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        public async Task<IActionResult> UpdateVisibilitaAttestatoAutorizzazione([FromBody] AutorizzazioneDto dto)
        {
            try
            {
                #region Validazione Input

                if (dto == null)
                {
                    throw new ValidationException($"{nameof(dto)} non valorizzato");
                }

                var validator = new AutorizzazioneDTOValidator(_conf, _radioamatoreBl, RequestOrigin.FrontEnd, Operation.UpdateVisibilitaAttestato);
                var validationResult = validator.Validate(dto);
                if (!validationResult.IsValid)
                {
                    throw new ValidationException(validationResult.Errors);
                }

                #endregion

                var result = await _radioamatoreBl.UpdateVisibilitaAttestatoAsync(dto.Id, dto.IsDocumentoAutorizzazioneVisibileFe.Value);

                return result > 0 ? Ok(new { idAutorizzazione = dto.Id }) : NoContent();
            }
            catch (ValidationException ex)
            {
                return BadRequest(ex);
            }
            catch (Refit.ApiException ex)
            {
                throw new APIException(ex.Message, ex.StackTrace, ex.Uri.AbsoluteUri, ex.Content, ex.Headers);
            }
            catch (Exception ex)
            {
                // Per recuperare le eccezioni interne alla API Common invocata dalla corrente API 
                if (ex is APIException) throw;

                throw new APIException(ex.Message, ex.StackTrace, ex.InnerException?.Message, ex.InnerException?.StackTrace, dto);
            }
        }

        [HttpPost("UpdateNominativoAssegnatario", Name = "UpdateNominativoAssegnatario")]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        public async Task<IActionResult> UpdateNominativoAssegnatario([FromBody] AutorizzazioneDto dto)
        {
            try
            {
                #region Validazione Input

                if (dto == null)
                {
                    throw new ValidationException($"{nameof(dto)} non valorizzato");
                }

                var validator = new AutorizzazioneDTOValidator(_conf, _radioamatoreBl, RequestOrigin.FrontEnd, Operation.UpdateNominativoAssegnatario);
                var validationResult = validator.Validate(dto);
                if (!validationResult.IsValid)
                {
                    throw new ValidationException(validationResult.Errors);
                }

                #endregion

                var result = await _radioamatoreBl.UpdateIdRadioamatoreAsync(dto.IdRadioamatore.Value, dto.Id);

                return result > 0 ? Ok(new { idAutorizzazione = dto.Id }) : NoContent();
            }
            catch (ValidationException ex)
            {
                return BadRequest(ex);
            }
            catch (Refit.ApiException ex)
            {
                throw new APIException(ex.Message, ex.StackTrace, ex.Uri.AbsoluteUri, ex.Content, ex.Headers);
            }
            catch (Exception ex)
            {
                // Per recuperare le eccezioni interne alla API Common invocata dalla corrente API 
                if (ex is APIException) throw;

                throw new APIException(ex.Message, ex.StackTrace, ex.InnerException?.Message, ex.InnerException?.StackTrace, dto);
            }
        }
        
        [HttpPost("UploadDocumenti", Name = "UploadDocumenti")]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        public async Task<IActionResult> UploadDocumenti([FromHeader(Name = HttpHeaderParamsName.HEADER_PARAM_ID_RISORSA)] string idRisorsa,
                                                            [FromBody] AutorizzazioneDto dto)
        {
            try
            {
                #region Validazione Input

                if (string.IsNullOrWhiteSpace(idRisorsa))
                {
                    throw new ValidationException($"{HttpHeaderParamsName.HEADER_PARAM_ID_RISORSA} non valorizzato");
                }

                if (dto == null)
                {
                    throw new ValidationException($"{nameof(dto)} non valorizzato");
                }

                var validator = new AutorizzazioneDTOValidator(_conf, _radioamatoreBl, RequestOrigin.FrontEnd, Operation.UploadDocumenti);
                var validationResult = validator.Validate(dto);
                if (!validationResult.IsValid)
                {
                    throw new ValidationException(validationResult.Errors);
                }

                #endregion

                var result = await _radioamatoreBl.UploadDocumentoAutorizzazioneAsync(idRisorsa, dto);

                return result > 0 ? Ok(new { idAutorizzazione = dto.Id }) : NoContent();
            }
            catch (ValidationException ex)
            {
                return BadRequest(ex);
            }
            catch (Refit.ApiException ex)
            {
                throw new APIException(ex.Message, ex.StackTrace, ex.Uri.AbsoluteUri, ex.Content, ex.Headers);
            }
            catch (Exception ex)
            {
                // Per recuperare le eccezioni interne alla API Common invocata dalla corrente API 
                if (ex is APIException) throw;

                throw new APIException(ex.Message, ex.StackTrace, ex.InnerException?.Message, ex.InnerException?.StackTrace, dto);
            }
        }

        [HttpPost("ProtocolloEntrata", Name = "ProtocolloEntrataAutorizzazioni")]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        public async Task<IActionResult> ProtocolloEntrata(
            [FromHeader(Name = HttpHeaderParamsName.HEADER_PARAM_TIPO_APPLICAZIONE)] string TipoApplicazione,
            [FromBody] SetProtocolloMise datiProtocollo)
        {
            try
            {
                DateTime dataProtocollo;

                if (string.IsNullOrWhiteSpace(datiProtocollo.CodiceDomandaFE))
                {
                    throw new ValidationException($"{nameof(datiProtocollo.CodiceDomandaFE)} non valorizzato");
                }
                if (!DateTime.TryParseExact(datiProtocollo.DataProtocollo, "dd/MM/yyyy HH:mm:ss", CultureInfo.InvariantCulture, DateTimeStyles.None, out dataProtocollo))
                {
                    throw new ValidationException($"{nameof(datiProtocollo.DataProtocollo)} non valorizzato oppure non è nel formato atteso (dd/MM/yyyy HH:mm:ss)");
                }
                if (string.IsNullOrWhiteSpace(datiProtocollo.CodiceProtocollo))
                {
                    throw new ValidationException($"{nameof(datiProtocollo.CodiceProtocollo)} non valorizzato");
                }

                var affectedRows = await this._radioamatoreBl.SetProtocolloAutorizzazioneAsync(datiProtocollo.CodiceDomandaFE, dataProtocollo, datiProtocollo.CodiceProtocollo);

                return Ok(affectedRows);
            }
            catch (ValidationException ex)
            {
                return BadRequest(ex);
            }
            catch (Refit.ApiException ex)
            {
                throw new APIException(ex.Message, ex.StackTrace, ex.Uri.AbsoluteUri, ex.Content, ex.Headers);
            }
            catch (Exception ex)
            {
                // Per recuperare le eccezioni interne alla API Common invocata dalla corrente API di Misura
                if (ex is APIException) throw;

                throw new APIException(ex.Message, ex.StackTrace, ex.InnerException?.Message, ex.InnerException?.StackTrace, new { datiProtocollo });
            }
        }

        [HttpPost("ProtocolloUscita", Name = "ProtocolloUscitaAutorizzazioni")]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        public async Task<IActionResult> ProtocolloUscita([FromHeader(Name = HttpHeaderParamsName.HEADER_PARAM_ID_RISORSA)] string idRisorsa,
                                                            [FromBody] ProtocolloUscitaDto dto)
        {
            try
            {
                #region Validazione Input

                if (string.IsNullOrWhiteSpace(idRisorsa))
                {
                    throw new ValidationException($"{HttpHeaderParamsName.HEADER_PARAM_ID_RISORSA} è obbligatorio");
                }

                if (dto == null)
                {
                    throw new ValidationException($"{nameof(dto)} non valorizzato");
                }

                var validatorsFactory = new GenericValidatorsFactory<ProtocolloUscitaDto>(this._conf, this._radioamatoreBl, "RAD3");
                validatorsFactory.Validate(dto);
                List<ValidationFailure> failures = validatorsFactory.GetFailures();
                if (failures.Count > 0)
                {
                    throw new ValidationException(failures);
                }

                #endregion

                var result = await _radioamatoreBl.SetProtocolloUscitaAutorizzazioneAsync(idRisorsa, dto);

                return result > 0 ? Ok(new { idAutorizzazione = dto.IdAutorizzazione }) : NoContent();
            }
            catch (ValidationException ex)
            {
                return BadRequest(ex);
            }
            catch (Refit.ApiException ex)
            {
                throw new APIException(ex.Message, ex.StackTrace, ex.Uri.AbsoluteUri, ex.Content, ex.Headers);
            }
            catch (Exception ex)
            {
                // Per recuperare le eccezioni interne alla API Common invocata dalla corrente API di Misura
                if (ex is APIException) throw;

                throw new APIException(ex.Message, ex.StackTrace, ex.InnerException?.Message, ex.InnerException?.StackTrace, new { dto });
            }
        }

        [HttpPost("Attiva", Name = "AttivaAutorizzazione")]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        public async Task<IActionResult> AttivaAutorizzazione([FromBody] AutorizzazioneDto dto)
        {
            return await GestisciCambioStatoAsync(dto, EnumStatoAutorizzazione.Attiva);
        }

        [HttpPost("Cancella", Name = "CancellaAutorizzazione")]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        public async Task<IActionResult> CancellaAutorizzazione([FromBody] AutorizzazioneDto dto)
        {
            return await GestisciCambioStatoAsync(dto, EnumStatoAutorizzazione.Cancellata);
        }

        [HttpPost("Rinuncia", Name = "RinunciaAutorizzazione")]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        public async Task<IActionResult> RinunciaAutorizzazione([FromBody] AutorizzazioneDto dto)
        {
            return await GestisciCambioStatoAsync(dto, EnumStatoAutorizzazione.Rinunciata);
        }

        [HttpPost("SetScadenza", Name = "SetScadenzaAutorizzazioni")]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        public async Task<IActionResult> SetScadenzaAutorizzazioni([FromHeader(Name = Constants.HEADER_IS_READONLY)] bool? isReadOnly)
        {
            try
            {
                #region Validazione Input

                if (!isReadOnly.HasValue)
                {
                    throw new ValidationException($"{Constants.HEADER_IS_READONLY} è obbligatorio");
                }

                #endregion

                var result = await _radioamatoreBl.SetScadenzaAutorizzazioniAsync(isReadOnly.Value);

                return result.Any() ? Ok(result) : NoContent();
            }
            catch (ValidationException ex)
            {
                return BadRequest(ex);
            }
            catch (Refit.ApiException ex)
            {
                throw new APIException(ex.Message, ex.StackTrace, ex.Uri.AbsoluteUri, ex.Content, ex.Headers);
            }
            catch (Exception ex)
            {
                // Per recuperare le eccezioni interne alla API Common invocata dalla corrente API di Misura
                if (ex is APIException) throw;

                throw new APIException(ex.Message, ex.StackTrace, ex.InnerException?.Message, ex.InnerException?.StackTrace, new { isReadOnly });
            }
        }

        #endregion

        #region Private methods

        private async Task<IActionResult> InsertAutorizzazioneAsync(string idRisorsa, AutorizzazioneDto dto, RequestOrigin requestOrigin)
        {
            try
            {
                #region Validazione Input

                if (dto == null)
                {
                    throw new ValidationException($"{nameof(dto)} non valorizzato");
                }

                var validator = new AutorizzazioneDTOValidator(_conf, _radioamatoreBl, requestOrigin, Operation.Insert);
                var validationResult = validator.Validate(dto);
                if (!validationResult.IsValid)
                {
                    throw new ValidationException(validationResult.Errors);
                }

                #endregion

                var result = await _radioamatoreBl.InsertAutorizzazioneAsync(idRisorsa, dto, requestOrigin);
                return Ok(result);

            }
            catch (ValidationException ex)
            {
                return BadRequest(ex);
            }
            catch (Refit.ApiException ex)
            {
                throw new APIException(ex.Message, ex.StackTrace, ex.Uri.AbsoluteUri, ex.Content, ex.Headers);
            }
            catch (Exception ex)
            {
                // Per recuperare le eccezioni interne alla API Common invocata dalla corrente API 
                if (ex is APIException) throw;

                throw new APIException(ex.Message, ex.StackTrace, ex.InnerException?.Message, ex.InnerException?.StackTrace, dto);
            }
        }

        private async Task<IActionResult> GestisciCambioStatoAsync(AutorizzazioneDto dto, EnumStatoAutorizzazione statoAutorizzazione)
        {
            try
            {
                #region Validazione Input

                if (dto == null)
                {
                    throw new ValidationException($"{nameof(dto)} non valorizzato");
                }

                dto.StatoAutorizzazione = new StatoAutorizzazioneDto() { Id = (int)statoAutorizzazione, Descrizione = statoAutorizzazione.GetAttributeDescription() };
                dto.IdStatoAutorizzazione = (short)statoAutorizzazione;

                var validator = new AutorizzazioneDTOValidator(_conf, _radioamatoreBl, RequestOrigin.NotSpecified, Operation.CambioStato);
                var validationResult = validator.Validate(dto);
                if (!validationResult.IsValid)
                {
                    throw new ValidationException(validationResult.Errors);
                }

                #endregion

                var result = await _radioamatoreBl.GestisciCambioStatoAutorizzazioneAsync(dto, statoAutorizzazione);

                return result > 0 ? Ok(new { idAutorizzazione = dto.Id }) : NoContent();
            }
            catch (ValidationException ex)
            {
                return BadRequest(ex);
            }
            catch (Refit.ApiException ex)
            {
                throw new APIException(ex.Message, ex.StackTrace, ex.Uri.AbsoluteUri, ex.Content, ex.Headers);
            }
            catch (Exception ex)
            {
                // Per recuperare le eccezioni interne alla API Common invocata dalla corrente API 
                if (ex is APIException) throw;

                throw new APIException(ex.Message, ex.StackTrace, ex.InnerException?.Message, ex.InnerException?.StackTrace, dto);
            }
        }

        #endregion
    }
}
