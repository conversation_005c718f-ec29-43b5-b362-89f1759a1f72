﻿using FluentValidation;
using Inv.Framework.Api.DTO;
using Invitalia.Misure.FiltriAPI;
using Invitalia.Misure.FiltriAPI.Constants;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using MicroSV_Radioamatori.BL.Interfaces;
using System;
using System.Threading.Tasks;

namespace MicroSV_Radioamatori.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [ServiceFilter(typeof(TraceControllerActionsFilter))]
    public class ZonaController : ControllerBase
    {
        private readonly IConfiguration _conf;
        private readonly IRadiamatoreBl _radioamatoreBl;

        public ZonaController(IConfiguration conf, IRadiamatoreBl radioamatoreBl)
        {
            this._conf = conf;
            this._radioamatoreBl = radioamatoreBl;
        }

        /// <summary>
        /// GetZona
        /// </summary>
        /// <param name="siglaRegione"></param>
        /// <param name="siglaProvincia"></param>
        /// <param name="codiceIstatComune"></param>
        /// <returns></returns>
        /// <exception cref="APIException"></exception>
        [HttpGet("siglaregione/{siglaRegione}/siglaprovincia/{siglaProvincia}/codiceistatcomune/{codiceIstatComune}")]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        public async Task<IActionResult> GetZona(
            [FromHeader(Name = HttpHeaderParamsName.HEADER_PARAM_TIPO_APPLICAZIONE)] string tipoApplicazione,
            [FromRoute] string siglaRegione, 
            [FromRoute] string siglaProvincia, 
            [FromRoute] string codiceIstatComune)
        {
            try
            {
                #region Validazione Input
                if (string.IsNullOrWhiteSpace(siglaRegione))
                {
                    throw new ValidationException($"{nameof(siglaRegione)} non valorizzato");
                }
                if (string.IsNullOrWhiteSpace(siglaProvincia))
                {
                    throw new ValidationException($"{nameof(siglaProvincia)} non valorizzato");
                }
                if (string.IsNullOrWhiteSpace(codiceIstatComune))
                {
                    throw new ValidationException($"{nameof(codiceIstatComune)} non valorizzato");
                }
                #endregion

                var dto = await _radioamatoreBl.GetZonaAsync(siglaRegione, siglaProvincia, codiceIstatComune);
                return Ok(dto);
            }
            catch (ValidationException ex)
            {
                return BadRequest(ex);
            }           
            catch (Exception ex)
            {
                // Per recuperare le eccezioni interne alla API Common invocata dalla corrente API 
                if (ex is APIException) throw;

                throw new APIException(ex.Message, ex.StackTrace, ex.InnerException?.Message, ex.InnerException?.StackTrace, new { siglaRegione, siglaProvincia, codiceIstatComune });
            }
        }
    }
}
