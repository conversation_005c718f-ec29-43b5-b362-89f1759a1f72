﻿using FluentValidation;
using Inv.Framework.Api.DTO;
using Invitalia.Misure.FiltriAPI;
using Invitalia.Misure.FiltriAPI.Constants;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using MicroSV_Radioamatori.BL.Interfaces;
using Newtonsoft.Json.Linq;
using System;
using System.Threading.Tasks;
using MicroSV_Radioamatori.BL.IncomingDTOs;
using System.Collections.Generic;
using System.Linq;
using MicroSV_Radioamatori.BL.DTOs;
using Microsoft.AspNetCore.Http;

namespace MicroSV_Radioamatori.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [ServiceFilter(typeof(TraceControllerActionsFilter))]
    public class ControlloNominativoController : ControllerBase
    {

        private readonly IConfiguration _conf;
        private readonly IRadiamatoreBl _radioamatoreBl;

        public ControlloNominativoController(IConfiguration conf, IRadiamatoreBl radioamatoreBl)
        {
            this._conf = conf;
            this._radioamatoreBl = radioamatoreBl;
        }

        [HttpPost("",
            Name = "ControlloNominativo")]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(EsitoValidazioneDto), StatusCodes.Status202Accepted)]
        [ProducesResponseType(typeof(EsitoValidazioneDto), StatusCodes.Status409Conflict)]
        [ProducesResponseType(typeof(ValidationException), StatusCodes.Status400BadRequest)]
        public async Task<IActionResult> ControllaNominativoStandardAsync(
            [FromHeader(Name = HttpHeaderParamsName.HEADER_PARAM_TIPO_APPLICAZIONE)] string tipoApplicazione,
            [FromBody] RadioamatoreDaGenerareDto radioamatoreDaGenerare,
            [FromQuery] bool isStandard, [FromQuery] bool isContest, [FromQuery] bool isManifestazione, [FromQuery] bool isManifestazioneArt44)
        {
            try
            {
                #region Validazione Input
                if (radioamatoreDaGenerare == null)
                {
                    throw new ValidationException($"{nameof(radioamatoreDaGenerare)} non valorizzato");
                }
                var listaBool = new List<bool>()
                {
                    isContest, isManifestazione, isManifestazioneArt44, isStandard
                };
                if (listaBool.Count(b => b == true) != 1)
                {
                    throw new ValidationException($"E' necessario valorizzare un solo booleano con true per indicare il tipo di generazione");
                }


                #endregion


                var esito = (await _radioamatoreBl.ControllaNominativoPerIstruttoriaAsync(radioamatoreDaGenerare, isStandard, isContest, isManifestazione, isManifestazioneArt44));

                if (esito.Successo)
                {
                    return Ok(esito);
                }
                else if (esito.ProseguiSuFEAbilitato)
                {
                    return Accepted(esito);
                }
                else
                {
                    return Conflict(esito);
                }

                throw new Exception($"Richiesta di nominativo per il protocollo {radioamatoreDaGenerare.CodiceProtocollo} non riconducibile a un tipologia.");
            }
            catch (ValidationException ex)
            {
                return BadRequest(ex);
            }
            catch (Refit.ApiException ex)
            {
                throw new APIException(ex.Message, ex.StackTrace, ex.Uri.AbsoluteUri, ex.Content, ex.Headers);
            }
            catch (Exception ex)
            {
                // Per recuperare le eccezioni interne alla API Common invocata dalla corrente API 
                if (ex is APIException) throw;

                throw new APIException(ex.Message, ex.StackTrace, ex.InnerException?.Message, ex.InnerException?.StackTrace);
            }
        }
    }
}
