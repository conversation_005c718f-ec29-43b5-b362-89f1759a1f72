﻿using FluentValidation.Results;
using Inv.Framework.Api.BL.DTO.Interfaces;
using Invitalia.Misure.FiltriAPI;
using Invitalia.Misure.FiltriAPI.Constants;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using MicroSV_Radioamatori.API.Validators;
using MicroSV_Radioamatori.BL.DTOs;
using MicroSV_Radioamatori.BL.Enums;
using MicroSV_Radioamatori.BL.Interfaces;
using System.Collections.Generic;
using System;
using System.Threading.Tasks;
using FluentValidation;
using Inv.Framework.Api.DTO;
using MicroSV_Radioamatori.API.Filters;

namespace MicroSV_Radioamatori.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [ServiceFilter(typeof(TraceControllerActionsFilter))]
    public class AllegatiController : ControllerBase
    {
        private readonly IConfiguration _conf;
        private readonly IRadiamatoreBl _radioamatoreBl;

        public AllegatiController(IConfiguration conf, IRadiamatoreBl radioamatoreBl)
        {
            this._conf = conf;
            this._radioamatoreBl = radioamatoreBl;
        }

        [HttpGet("backend/blobname/{blobName}", Name = "GetDocumentByBlobNameForBackEnd")]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        public async Task<IActionResult> GetDocumentByBlobNameForBackEnd([FromHeader(Name = HttpHeaderParamsName.HEADER_PARAM_CODICE_MISURA)] string codiceMisura,
                                                        [FromHeader(Name = HttpHeaderParamsName.HEADER_PARAM_TIPO_APPLICAZIONE)] string tipoApplicazione,
                                                        [FromHeader(Name = HttpHeaderParamsName.HEADER_PARAM_TIPO_SOGGETTO)] string tipoSoggetto,
                                                        [FromRoute] string blobName)
        {
            return await DownloadDocumentByBlobNameAsync(codiceMisura, tipoApplicazione, tipoSoggetto, blobName);
        }

        [HttpGet("blobname/{blobName}", Name = "GetDocumentByBlobNameForFrontEnd")]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        [ServiceFilter(typeof(CheckAccessAttachmentFilter))]
        public async Task<IActionResult> GetDocumentByBlobNameForFrontEnd([FromHeader(Name = HttpHeaderParamsName.HEADER_PARAM_ID_RISORSA)] string idRisorsa,
                                                                [FromHeader(Name = HttpHeaderParamsName.HEADER_PARAM_CODICE_MISURA)] string codiceMisura,
                                                                [FromHeader(Name = HttpHeaderParamsName.HEADER_PARAM_TIPO_APPLICAZIONE)] string tipoApplicazione,
                                                                [FromHeader(Name = HttpHeaderParamsName.HEADER_PARAM_TIPO_SOGGETTO)] string tipoSoggetto,
                                                                [FromHeader(Name = HttpHeaderParamsName.HEADER_PARAM_UTENTE_CORRENTE_BASE_64)] string utenteBase64,
                                                                [FromRoute] string blobName)
        {
            if (string.IsNullOrWhiteSpace(idRisorsa))
            {
                throw new ValidationException($"{HttpHeaderParamsName.HEADER_PARAM_ID_RISORSA} è obbligatorio");
            }

            if (string.IsNullOrWhiteSpace(utenteBase64))
            {
                throw new ValidationException($"{HttpHeaderParamsName.HEADER_PARAM_UTENTE_CORRENTE_BASE_64} è obbligatorio");
            }

            return await DownloadDocumentByBlobNameAsync(codiceMisura, tipoApplicazione, tipoSoggetto, blobName);
        }

        private async Task<IActionResult> DownloadDocumentByBlobNameAsync(string codiceMisura, string tipoApplicazione, string tipoSoggetto, string blobName)
        {
            try
            {
                #region Validazione Input

                if (string.IsNullOrWhiteSpace(codiceMisura))
                {
                    throw new ValidationException($"{HttpHeaderParamsName.HEADER_PARAM_CODICE_MISURA} è obbligatorio");
                }

                if (string.IsNullOrWhiteSpace(tipoApplicazione))
                {
                    throw new ValidationException($"{HttpHeaderParamsName.HEADER_PARAM_TIPO_APPLICAZIONE} è obbligatorio");
                }

                if (string.IsNullOrWhiteSpace(tipoSoggetto))
                {
                    throw new ValidationException($"{HttpHeaderParamsName.HEADER_PARAM_TIPO_SOGGETTO} è obbligatorio");
                }

                if (string.IsNullOrWhiteSpace(blobName))
                {
                    throw new ValidationException($"{nameof(blobName)} non valorizzato");
                }

                #endregion

                var result = await _radioamatoreBl.DownloadAllegatoByBlobName(codiceMisura, blobName);

                return Ok(result);
            }
            catch (ValidationException ex)
            {
                return BadRequest(ex);
            }
            catch (Refit.ApiException ex)
            {
                throw new APIException(ex.Message, ex.StackTrace, ex.Uri.AbsoluteUri, ex.Content, ex.Headers);
            }
            catch (Exception ex)
            {
                // Per recuperare le eccezioni interne alla API Common invocata dalla corrente API di Misura
                if (ex is APIException) throw;

                throw new APIException(ex.Message, ex.StackTrace, ex.InnerException?.Message, ex.InnerException?.StackTrace, new { codiceMisura, blobName });
            }
        }
    }
}
