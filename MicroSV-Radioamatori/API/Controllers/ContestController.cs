﻿using FluentValidation;
using Inv.Framework.Api.DTO;
using Invitalia.Misure.FiltriAPI;
using Invitalia.Misure.FiltriAPI.Constants;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using MicroSV_Radioamatori.BL.DTOs;
using MicroSV_Radioamatori.BL.Interfaces;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace MicroSV_Radioamatori.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [ServiceFilter(typeof(TraceControllerActionsFilter))]
    public class ContestController : ControllerBase
    {
        private readonly IConfiguration _conf;
        private readonly IRadiamatoreBl _radioamatoreBl;

        public ContestController(IConfiguration conf, IRadiamatoreBl radioamatoreBl)
        {
            this._conf = conf;
            this._radioamatoreBl = radioamatoreBl;
        }
        
        /// <summary>
        /// GetAnagraficaContest
        /// </summary>
        /// <returns></returns>
        /// <exception cref="APIException"></exception>
        [HttpGet("", Name = "GetAnagraficaContest")]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        public async Task<IActionResult> GetAnagraficaContest(
            [FromHeader(Name = HttpHeaderParamsName.HEADER_PARAM_TIPO_APPLICAZIONE)] string tipoApplicazione)
        {

            try
            {
                var objCf = await _radioamatoreBl.GetAnagraficaContestAsync();
                return Ok(objCf);
            }
            catch (ValidationException ex)
            {
                return BadRequest(ex);
            }
            catch (Refit.ApiException ex)
            {
                throw new APIException(ex.Message, ex.StackTrace, ex.Uri.AbsoluteUri, ex.Content, ex.Headers);
            }
            catch (Exception ex)
            {
                // Per recuperare le eccezioni interne alla API Common invocata dalla corrente API 
                if (ex is APIException) throw;

                throw new APIException(ex.Message, ex.StackTrace, ex.InnerException?.Message, ex.InnerException?.StackTrace);
            }
        }
        /// <summary>
        /// GetContestbyRadioamatori
        /// </summary>
        /// <param name="tipoApplicazione"></param>
        /// <param name="idRadioamatore"></param>
        /// <returns></returns>
        [HttpGet("idRadioamatore/{idRadioamatore}", Name = "GetContestbyRadioamatori")]
        [ResponseCache(Location = ResponseCacheLocation.None, NoStore = true)]
        public async Task<IActionResult> GetContestbyRadioamatori(
           [FromHeader(Name = HttpHeaderParamsName.HEADER_PARAM_TIPO_APPLICAZIONE)] string tipoApplicazione,
           [FromRoute] string idRadioamatore)
        {

            try
            {
                #region Validazione Input
                if (string.IsNullOrWhiteSpace(idRadioamatore))
                {
                    throw new ValidationException($"{nameof(idRadioamatore)} non valorizzato");
                }
                #endregion

                List<AnagraficaContestContestDto> objCf = await _radioamatoreBl.GetContestbyRadioamatoreAsync(idRadioamatore);
                if (objCf != null && objCf.Any())
                {
                    return Ok(objCf);
                }

                return Ok(new List<ContestDto>());
            }
            catch (ValidationException ex)
            {
                return BadRequest(ex);
            }
            catch (Refit.ApiException ex)
            {
                throw new APIException(ex.Message, ex.StackTrace, ex.Uri.AbsoluteUri, ex.Content, ex.Headers);
            }
            catch (Exception ex)
            {
                // Per recuperare le eccezioni interne alla API Common invocata dalla corrente API 
                if (ex is APIException) throw;

                throw new APIException(ex.Message, ex.StackTrace, ex.InnerException?.Message, ex.InnerException?.StackTrace);
            }
        }

    }
}

