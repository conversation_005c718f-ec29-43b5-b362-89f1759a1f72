﻿using FluentValidation;
using FluentValidation.Results;
using Microsoft.Extensions.Configuration;
using MicroSV_Radioamatori.BL.DTOs;
using MicroSV_Radioamatori.BL.Interfaces;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;

namespace MicroSV_Radioamatori.API.Validators
{
    public class ValidatorsFactory 
    {
        internal IValidator validatorRadioamatore;

        private readonly IConfiguration _conf;
        private readonly IRadiamatoreBl _radioamatoreBl;
        private List<ValidationResult> results;
        public ValidatorsFactory(IConfiguration conf, IRadiamatoreBl radioamatoreBl, RadioamatoreDto dto, string codiceMisura)
        {
            this._conf = conf;            
            this._radioamatoreBl = radioamatoreBl;

            SetValidatorsByRadioamatore(dto);

        }
        private void SetValidatorsByRadioamatore(RadioamatoreDto radioamatoreDTO)
        {
            // DATI ANAGRAFICI
            try
            {
                string RadioamatoreValidator = nameof(RadioamatoreDTOValidator);
               
                this.validatorRadioamatore = Instantiate<IValidator>(RadioamatoreValidator);
            }
            catch (Exception e)
            {
                this.validatorRadioamatore = Instantiate<IValidator>(nameof(RadioamatoreDTOValidator));
            }
        }

        private T Instantiate<T>(string className)
        {
            Type typeImplemented = typeof(T);

            Type selectedType = Assembly.GetExecutingAssembly() // i.e. our project/library
                .GetTypes()
                .First(t => typeImplemented.IsAssignableFrom(t) && t.Name.ToLower() == className.ToLower());

            return (T)Activator.CreateInstance(selectedType, this._conf, this._radioamatoreBl);
        }

        internal void Validate(RadioamatoreDto radioamatoreDTO)
        {
            results = new List<ValidationResult>();

            ValidationContext<RadioamatoreDto> datiAnagraficiValidationContext = new ValidationContext<RadioamatoreDto>(radioamatoreDTO);
            ValidationResult datiAnagraficiValidationResult = this.validatorRadioamatore.Validate(datiAnagraficiValidationContext);
            results.Add(datiAnagraficiValidationResult);
        }

        public List<ValidationFailure> GetFailures()
        {
            List<ValidationFailure> failures = new List<ValidationFailure>();
            foreach (ValidationResult result in results)
            {
                if (!result.IsValid)
                    failures.AddRange(result.Errors);
            }

            return failures;
        }
    }
}
