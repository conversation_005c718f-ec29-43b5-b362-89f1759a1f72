﻿using FluentValidation;
using Inv.Fwk.Helper.DateTimeZone;
using Microsoft.Extensions.Configuration;
using MicroSV_Radioamatori.BL.Interfaces;
using Newtonsoft.Json;
using System.Collections.Generic;
using System.Linq;

namespace MicroSV_Radioamatori.API.Validators
{
    public class BaseValidator<T> : AbstractValidator<T>
    {
        internal readonly IConfiguration _conf;
        internal readonly IRadiamatoreBl _radioamatoreBl;
        internal readonly string _codiceMisura;

        public BaseValidator(IConfiguration conf, IRadiamatoreBl radioamatoreBl, string codiceMisura)
        {
            _conf = conf;
            _radioamatoreBl = radioamatoreBl;
            _codiceMisura = codiceMisura;
        }

        public override FluentValidation.Results.ValidationResult Validate(ValidationContext<T> context)
        {
            if (context.InstanceToValidate != null)
            {
                return base.Validate(context);
            }

            return new FluentValidation.Results.ValidationResult();
        }

        public virtual bool BeAValidDate(string date)
        {
            return DateTimeHelper.BeAValidDate(date);
        }
    }
}
