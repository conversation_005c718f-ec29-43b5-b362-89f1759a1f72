﻿using FluentValidation;
using Invitalia.Misure.Standard2.Utilities.DTO;
using Microsoft.Extensions.Configuration;
using MicroSV_Radioamatori.BL.DTOs;
using MicroSV_Radioamatori.BL.Enums;
using MicroSV_Radioamatori.BL.Interfaces;
using System;
using System.Collections;

namespace MicroSV_Radioamatori.API.Validators.Documento
{
    public class DocumentoDtoValidator : BaseValidator<DocumentoDto>
    {
        public DocumentoDtoValidator(IConfiguration conf, IRadiamatoreBl radioamatoreBl, string codiceMisura) : base(conf, radioamatoreBl, codiceMisura)
        {
            Init();
        }

        protected virtual void Init()
        {

        }

        protected virtual void TipoDocumentoValidator<TEnum>() where TEnum : struct
        {
            RuleFor(dto => dto.TipoDocumento)
                .NotEmpty().WithMessage($"{nameof(DocumentoDto.TipoDocumento)} deve essere valorizzato");

            When(dto => !string.IsNullOrWhiteSpace(dto.TipoDocumento),
                    () =>
                    {
                        RuleFor(dto => dto.TipoDocumento)
                            .Must(tipoDocumento => BeValidTipoDocumento<TEnum>(tipoDocumento))
                            .WithMessage(dto => $"{dto.TipoDocumento} non è un tipo documento valido");
                    });
        }

        protected virtual void FileBase64Validator()
        {
            RuleFor(dto => dto.FileBase64)
                .NotEmpty().WithMessage($"{nameof(DocumentoDto.FileBase64)} deve essere valorizzato");
        }

        protected virtual void FileNameValidator()
        {
            RuleFor(dto => dto.FileName)
                .NotEmpty().WithMessage($"{nameof(DocumentoDto.FileName)} deve essere valorizzato");
        }

        protected virtual void EstensioneValidator()
        {
            RuleFor(dto => dto.Estensione)
                .NotEmpty().WithMessage($"{nameof(DocumentoDto.Estensione)} deve essere valorizzato");
        }

        protected virtual bool BeValidTipoDocumento<TEnum>(string tipoDocumento) where TEnum : struct
        {
            return Enum.TryParse(tipoDocumento, out TEnum _);
        }
    }
}
