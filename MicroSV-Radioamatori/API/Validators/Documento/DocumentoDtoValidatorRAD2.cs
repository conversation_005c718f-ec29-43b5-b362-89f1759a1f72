﻿using Microsoft.Extensions.Configuration;
using MicroSV_Radioamatori.BL.Enums;
using MicroSV_Radioamatori.BL.Interfaces;

namespace MicroSV_Radioamatori.API.Validators.Documento
{
    public class DocumentoDtoValidatorRAD2 : DocumentoDtoValidator
    {
        public DocumentoDtoValidatorRAD2(IConfiguration conf, IRadiamatoreBl radioamatoreBl, string codiceMisura) : base(conf, radioamatoreBl, codiceMisura)
        {
        }

        protected override void Init()
        {
            TipoDocumentoValidator<TipoDocumentoPatente>();
            FileBase64Validator();
            FileNameValidator();
            EstensioneValidator();
        }
    }
}
