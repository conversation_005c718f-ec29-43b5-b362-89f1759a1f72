﻿using Microsoft.Extensions.Configuration;
using MicroSV_Radioamatori.BL.Enums;
using MicroSV_Radioamatori.BL.Interfaces;

namespace MicroSV_Radioamatori.API.Validators.Documento
{
    public class DocumentoDtoValidatorRAD3 : DocumentoDtoValidator
    {
        public DocumentoDtoValidatorRAD3(IConfiguration conf, IRadiamatoreBl radioamatoreBl, string codiceMisura) : base(conf, radioamatoreBl, codiceMisura)
        {
        }

        protected override void Init()
        {
            TipoDocumentoValidator<TipoDocumentoAutorizzazione>();
            FileBase64Validator();
            FileNameValidator();
            EstensioneValidator();
        }
    }
}
