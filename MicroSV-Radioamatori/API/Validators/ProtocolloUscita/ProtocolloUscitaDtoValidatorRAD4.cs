﻿using FluentValidation;
using Inv.Framework.Api.BL.DTO.Interfaces;
using Invitalia.Misure.Standard2.Utilities.Extensions;
using Microsoft.Extensions.Configuration;
using MicroSV_Radioamatori.BL.DTOs;
using MicroSV_Radioamatori.BL.Enums;
using MicroSV_Radioamatori.BL.Interfaces;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace MicroSV_Radioamatori.API.Validators.ProtocolloUscita
{
    public class ProtocolloUscitaDtoValidatorRAD4 : ProtocolloUscitaDtoValidator
    {
        private AutorizzazioneSRDto _autorizzazioneSR;
        private List<AutorizzazioneSRDto> _autorizzazioniSRAssegnatario;
        private List<AutorizzazioneDto> _autorizzazioniGeneraliAssegnatario;

        private RadioamatoreSRDto _radioamatoreSR;

        private string _errorMessage = "";

        public ProtocolloUscitaDtoValidatorRAD4(IConfiguration conf, IRadiamatoreBl radioamatoreBl, string codiceMisura) : base(conf, radioamatoreBl, codiceMisura)
        {
        }

        protected override void Init()
        {
            IdAutorizzazioneValidator();
            DataRilascioAutorizzazioneValidator();
            CodiceFiscaleTitolarePatenteValidator();
            CodiceProtocolloInUscitaValidator();
            DataProtocolloInUscitaValidator();
            UtenteValidator();
            PatenteValidator();
            NominativoValidator();
            AutorizzazioniGeneraliValidator();

            //Validazioni rinnovo
            When(x => x.IdAutorizzazione.HasValue && IsRinnovoAutorizzazioneSRAsync(x.IdAutorizzazione.Value).GetAwaiter().GetResult(), () =>
            {
                NominativoSRValidator();
                RinnovoAutorizzazioneValidator();
                AutorizzazioneDaRinnovareValidator();
            });
        }

        protected virtual void IdAutorizzazioneValidator()
        {
            RuleFor(dto => dto.IdAutorizzazione)
                .NotEmpty().WithMessage($"{nameof(ProtocolloUscitaDto.IdAutorizzazione)} deve essere valorizzato");

            When(dto => dto.IdAutorizzazione.HasValue, () =>
            {
                RuleFor(dto => dto.IdAutorizzazione)
                    .Must((id) => ExistsAutorizzazioneAsync(id.Value).GetAwaiter().GetResult())
                    .WithMessage(x => $"Non esiste nessuna autorizzazione con id {x.IdAutorizzazione}");
            });
        }

        protected virtual void DataRilascioAutorizzazioneValidator()
        {
            RuleFor(dto => dto.DataRilascioAutorizzazione)
                .NotEmpty().WithMessage($"{nameof(ProtocolloUscitaDto.DataRilascioAutorizzazione)} deve essere valorizzato");

            When(dto => !string.IsNullOrWhiteSpace(dto.DataRilascioAutorizzazione),
                    () => RuleFor(dto => dto.DataRilascioAutorizzazione)
                            .Must(data => BeAValidDate(data)).WithMessage(dto => $"{dto.DataRilascioAutorizzazione} non è una data valida"));
        }

        protected virtual void CodiceFiscaleTitolarePatenteValidator()
        {
            When(x => !IsArt143AutorizzazioneSRAsync(x.IdAutorizzazione.Value).GetAwaiter().GetResult(), () => {
                RuleFor(dto => dto.CodiceFiscaleTitolarePatente)
                    .NotEmpty().WithMessage($"{nameof(ProtocolloUscitaDto.CodiceFiscaleTitolarePatente)} deve essere valorizzato");
            });
        }

        protected virtual void PatenteValidator()
        {
            When(x => !string.IsNullOrWhiteSpace(x.CodiceFiscaleTitolarePatente) 
                        && x.IdAutorizzazione.HasValue
                        && !IsArt143AutorizzazioneSRAsync(x.IdAutorizzazione.Value).GetAwaiter().GetResult(), () =>
            {
                RuleFor(x => x.CodiceFiscaleTitolarePatente)
                    .Must(cf => HasPatenteAttivaAsync(cf).GetAwaiter().GetResult())
                    .WithMessage("Attenzione! Non è presente nessuna patente in stato Attiva per il titolare patente");
            });
        }

        protected virtual void NominativoValidator()
        {
            When(x => x.IdAutorizzazione.HasValue && ExistsAutorizzazioneAsync(x.IdAutorizzazione.Value).GetAwaiter().GetResult(), () =>
            {
                RuleFor(x => x)
                    .Must(x => HasNominativoAttivoAsync(x.IdAutorizzazione.Value).GetAwaiter().GetResult())
                    .WithMessage("Attenzione! Non è presente un nominativo in stato attivo associato all’assegnatario");
            });
        }

        protected virtual void NominativoSRValidator()
        {
            RuleFor(x => x)
                .Must(x => HasNominativoSRAsync(x.IdAutorizzazione.Value).GetAwaiter().GetResult())
                    .WithMessage("Attenzione! L'autorizzazione che si sta rinnovando non risulta associata a un nominativo per stazione ripetitrice");

            When(x => GetAutorizzazioneSRAsync(x.IdAutorizzazione.Value).GetAwaiter().GetResult().IdRadioamatoreSR.HasValue, () =>
            {
                RuleFor(x => x)
                    .Must(x => HasNominativoSRAttivoAsync(x.IdAutorizzazione.Value).GetAwaiter().GetResult())
                        .WithMessage("Attenzione! Il nominativo per stazione ripetitrice indicato non risulta attivo")
                    .Must(x => HasCFNominativoSRValidoAsync(x.IdAutorizzazione.Value).GetAwaiter().GetResult())
                        .WithMessage("Attenzione! Il nominativo per stazione ripetitrice indicato non risulta essere associato al codice fiscale dell'assegnatario");
            });
        }

        protected virtual void AutorizzazioniGeneraliValidator()
        {
            When((x) => x.IdAutorizzazione.HasValue && ExistsAutorizzazioneAsync(x.IdAutorizzazione.Value).GetAwaiter().GetResult(), () =>
            {
                RuleFor(x => x.IdAutorizzazione)
                    .Must((id) => HasAssegnatarioAutorizzazioneGeneraleValidaAsync(id.Value).GetAwaiter().GetResult())
                        .WithMessage((x) => _errorMessage);
            });
        }

        protected virtual void RinnovoAutorizzazioneValidator()
        {
            When((x) => x.IdAutorizzazione.HasValue &&
                        ExistsAutorizzazioneAsync(x.IdAutorizzazione.Value).GetAwaiter().GetResult() &&
                        !IsAutorizzazioneSRInStatoAsync(x.IdAutorizzazione.Value, EnumStatoAutorizzazione.Rinnovo).GetAwaiter().GetResult(), () =>
            {
                RuleFor(x => x)
                    .Must(x => !HasAutorizzazioniSRInStatoAsync(x.IdAutorizzazione.Value, EnumStatoAutorizzazione.InIstruttoria).GetAwaiter().GetResult())
                    .WithMessage("Attenzione! E’ presente una richiesta di autorizzazione generale per stazione ripetitrice in istruttoria");

                RuleFor(x => x)
                    .Must(x => !HasAutorizzazioniSRInStatoAsync(x.IdAutorizzazione.Value, EnumStatoAutorizzazione.Attiva).GetAwaiter().GetResult())
                    .WithMessage("Attenzione! E’ presente una richiesta di autorizzazione generale per stazione ripetitrice in stato attivo");

                RuleFor(x => x)
                    .Must(x => !HasAutorizzazioniSRInStatoAsync(x.IdAutorizzazione.Value, EnumStatoAutorizzazione.Rinunciata).GetAwaiter().GetResult())
                    .WithMessage("Attenzione! L’assegnatario risulta aver rinunciato all’autorizzazione generale per stazione ripetitrice. Procedere alla richiesta di una nuova autorizzazione generale");

                RuleFor(x => x)
                    .Must(x => !HasAutorizzazioniSRInStatoAsync(x.IdAutorizzazione.Value, EnumStatoAutorizzazione.Cancellata).GetAwaiter().GetResult() &&
                                !HasAutorizzazioniSRInStatoAsync(x.IdAutorizzazione.Value, EnumStatoAutorizzazione.Disattivata).GetAwaiter().GetResult())
                    .WithMessage("Attenzione! L’assegnatario non risulta essere in possesso di autorizzazione generale per stazione ripetitrice da rinnovare");

                RuleFor(x => x)
                    .Must(x => !IsAutorizzazioneSRInStatoAsync(x.IdAutorizzazione.Value, EnumStatoAutorizzazione.Scaduta).GetAwaiter().GetResult())
                    .WithMessage("Attenzione! L’autorizzazione generale per stazione ripetitrice dell’assegnatario risulta essere scaduta. Procedere alla richiesta di una nuova autorizzazione generale per stazione ripetitrice");
            });
        }

        protected virtual void AutorizzazioneDaRinnovareValidator()
        {
            When((x) => x.IdAutorizzazione.HasValue &&
                        ExistsAutorizzazioneAsync(x.IdAutorizzazione.Value).GetAwaiter().GetResult(), () =>
                        {
                            RuleFor(x => x)
                                .Must(x => IsAutorizzazioneSRDaRinnovareValidAsync(x.IdAutorizzazione.Value).GetAwaiter().GetResult())
                                .WithMessage("Attenzione! L'autorizzazione generale per stazione ripetitrice da rinnovare non è in scadenza");
                        });
        }

        #region private methods

        private async Task<AutorizzazioneSRDto> GetAutorizzazioneSRAsync(int idAutorizzazione)
        {
            return _autorizzazioneSR ??= (await _radioamatoreBl.GetAutorizzazioneSRByIdAsync(idAutorizzazione));
        }

        private async Task<RadioamatoreSRDto> GetRadioamatoreSRAsync(int idRadioamatoreSR)
        {
            return _radioamatoreSR ??= (await _radioamatoreBl.GetRadioamatoreSRByIdAsync(idRadioamatoreSR));
        }

        private async Task<List<AutorizzazioneDto>> GetAutorizzazioniGeneraliAssegnatarioAsync(string cfAssegnatario)
        {
            return _autorizzazioniGeneraliAssegnatario ??= (await _radioamatoreBl.GetAutorizzazioneByFilterAsync(new AutorizzazioneFilterDto() { CodiceFiscale = cfAssegnatario }));
        }

        private async Task<bool> ExistsAutorizzazioneAsync(int idAutorizzazione)
        {
            var autorizzazione = await GetAutorizzazioneSRAsync(idAutorizzazione);

            return autorizzazione != null;
        }

        private async Task<bool> IsArt143AutorizzazioneSRAsync(int idAutorizzazione)
        {
            var autorizzazioneSR = (await _radioamatoreBl.GetAutorizzazioneSRByIdAsync(idAutorizzazione));
            return autorizzazioneSR?.IdTipologiaAssegnatario != 1 && autorizzazioneSR?.IdTipologiaAssegnatario != 2;
        }

        private async Task<bool> IsRinnovoAutorizzazioneSRAsync(int idAutorizzazione)
        {
            var autorizzazioneSR = (await _radioamatoreBl.GetAutorizzazioneSRByIdAsync(idAutorizzazione));
            return autorizzazioneSR?.Rinnovo ?? false;
        }

        private async Task<bool> HasPatenteAttivaAsync(string cfTitolarePatente)
        {
            var patenti = await _radioamatoreBl.GetPatentiByFilterAsync(new PatenteFilterDto()
            {
                CodiceFiscale = cfTitolarePatente
            });
            return patenti != null && patenti.Any(p => p.IdStatoPatente == (int)StatoPatente.Attiva);
        }

        private async Task<bool> HasNominativoAttivoAsync(int idAutorizzazione)
        {
            var autorizzazione = await GetAutorizzazioneSRAsync(idAutorizzazione);
            var nominativi = await _radioamatoreBl.GetRadioamatoriByCodiceFiscaleAsync(autorizzazione.CodiceFiscale);
            return nominativi != null && nominativi.Any(p => p.TipoNominativo.EqualsIgnoreCaseAndTrim("STANDARD") && p.StatoNominativo.EqualsIgnoreCaseAndTrim("ATTIVO"));
        }

        private async Task<bool> HasNominativoSRAsync(int idAutorizzazione)
        {
            var autorizzazioneSR = await GetAutorizzazioneSRAsync(idAutorizzazione);
            return autorizzazioneSR.IdRadioamatoreSR.HasValue;
        }

        private async Task<bool> HasNominativoSRAttivoAsync(int idAutorizzazione)
        {
            var autorizzazioneSR = await GetAutorizzazioneSRAsync(idAutorizzazione);
            var radioamatoreSR = await GetRadioamatoreSRAsync(autorizzazioneSR.IdRadioamatoreSR.Value);
            return radioamatoreSR?.StatoNominativo.EqualsIgnoreCaseAndTrim("ATTIVO") ?? false;
        }

        private async Task<bool> HasCFNominativoSRValidoAsync(int idAutorizzazione)
        {
            var autorizzazioneSR = await GetAutorizzazioneSRAsync(idAutorizzazione);
            var radioamatoreSR = await _radioamatoreBl.GetRadioamatoreSRByIdAsync(autorizzazioneSR.IdRadioamatoreSR.Value);
            return radioamatoreSR?.CodiceFiscale.EqualsIgnoreCaseAndTrim(autorizzazioneSR.CodiceFiscale) ?? false;
        }

        private async Task<bool> HasAssegnatarioAutorizzazioneGeneraleValidaAsync(int idAutorizzazione)
        {
            var isValid = false;

            var autorizzazione = await GetAutorizzazioneSRAsync(idAutorizzazione);
            if (autorizzazione != null)
            {
                var autorizzazioniAssegnatario = await GetAutorizzazioniGeneraliAssegnatarioAsync(autorizzazione.CodiceFiscale);
                isValid = autorizzazioniAssegnatario != null &&
                            autorizzazioniAssegnatario.Any(a => a.IdStatoAutorizzazione == (short)EnumStatoAutorizzazione.Attiva || a.IdStatoAutorizzazione == (short)EnumStatoAutorizzazione.InScadenza);

                if (!isValid)
                {
                    if (autorizzazioniAssegnatario == null || !autorizzazioniAssegnatario.Any())
                    {
                        _errorMessage = "Attenzione: non è presente una richiesta di autorizzazione generale";
                    }
                    else if (autorizzazioniAssegnatario.Any(a => a.IdStatoAutorizzazione == (short)EnumStatoAutorizzazione.InIstruttoria))
                    {
                        _errorMessage = "Attenzione: è presente una richiesta di autorizzazione generale in istruttoria, prima di procedere è necessario completare l’iter di autorizzazione generale";
                    }
                    else if (autorizzazioniAssegnatario.Any(a => a.IdStatoAutorizzazione == (short)EnumStatoAutorizzazione.Rinnovo))
                    {
                        _errorMessage = "Attenzione: è presente una richiesta di rinnovo di autorizzazione generale in istruttoria, prima di procedere è necessario completare l’iter di autorizzazione generale";
                    }
                    else if (autorizzazioniAssegnatario.Any(a => a.IdStatoAutorizzazione == (short)EnumStatoAutorizzazione.Rinunciata ||
                                                                    a.IdStatoAutorizzazione == (short)EnumStatoAutorizzazione.Scaduta ||
                                                                    a.IdStatoAutorizzazione == (short)EnumStatoAutorizzazione.Cancellata ||
                                                                    a.IdStatoAutorizzazione == (short)EnumStatoAutorizzazione.Disattivata))
                    {
                        _errorMessage = "Attenzione: non risulta una autorizzazione generale attiva presente a sistema.";
                    }
                }
            }

            return isValid;
        }

        private async Task<bool> IsAutorizzazioneSRInStatoAsync(int idAutorizzazione, EnumStatoAutorizzazione statoAutorizzazione)
        {
            var autorizzazioneSR = await GetAutorizzazioneSRAsync(idAutorizzazione);
            return autorizzazioneSR.IdStatoAutorizzazione == (int)statoAutorizzazione;
        }

        private async Task<bool> IsAutorizzazioneDaRinnovareInStatoAsync(int idAutorizzazione, EnumStatoAutorizzazione statoAutorizzazione)
        {
            var autorizzazioneSR = await _radioamatoreBl.GetAutorizzazioneSRByIdAsync(idAutorizzazione);
            return autorizzazioneSR.IdStatoAutorizzazione == (int)statoAutorizzazione;
        }


        private async Task<List<AutorizzazioneSRDto>> GetAutorizzazioniSRByCfAsync(string codiceFiscaleAssegnatario)
        {
            return _autorizzazioniSRAssegnatario ??= await _radioamatoreBl.GetAutorizzazioneSRByFilterAsync(new AutorizzazioneSRFilterDto()
            {
                CodiceFiscale = codiceFiscaleAssegnatario
            });
        }

        private async Task<bool> HasAutorizzazioniSRInStatoAsync(int idAutorizzazione, EnumStatoAutorizzazione statoAutorizzazione)
        {
            var autorizzazioneSR = await GetAutorizzazioneSRAsync(idAutorizzazione);

            var autorizzazioniSRAssegnatario = await GetAutorizzazioniSRByCfAsync(autorizzazioneSR.CodiceFiscale);

            return autorizzazioniSRAssegnatario.Any(x => x.IdStatoAutorizzazione == (int)statoAutorizzazione);
        }

        private async Task<bool> IsAutorizzazioneSRDaRinnovareValidAsync(int idRinnovo)
        {
            var autorizzazioneSR = await GetAutorizzazioneSRAsync(idRinnovo);
            return !autorizzazioneSR.IdAutorizzazioneRinnovata.HasValue ||
                    await IsAutorizzazioneDaRinnovareInStatoAsync(autorizzazioneSR.IdAutorizzazioneRinnovata.Value, EnumStatoAutorizzazione.InScadenza);
        }

        #endregion
    }
}
