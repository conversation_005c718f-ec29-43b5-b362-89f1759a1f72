﻿using FluentValidation;
using Microsoft.Extensions.Configuration;
using MicroSV_Radioamatori.API.Validators.Documento;
using MicroSV_Radioamatori.BL.DTOs;
using MicroSV_Radioamatori.BL.Interfaces;
using System.Linq;

namespace MicroSV_Radioamatori.API.Validators.ProtocolloUscita
{
    public class ProtocolloUscitaDtoValidator : BaseValidator<ProtocolloUscitaDto>
    {
        public ProtocolloUscitaDtoValidator(IConfiguration conf, IRadiamatoreBl radioamatoreBl, string codiceMisura) : base(conf, radioamatoreBl, codiceMisura)
        {
            Init();
        }

        protected virtual void Init()
        {

        }

        protected virtual void CodiceProtocolloInUscitaValidator()
        {
            RuleFor(dto => dto.CodiceProtocolloInUscita)
                .NotEmpty().WithMessage($"{nameof(ProtocolloUscitaDto.CodiceProtocolloInUscita)} deve essere valorizzato");
        }

        protected virtual void DataProtocolloInUscitaValidator()
        {
            RuleFor(dto => dto.DataProtocolloInUscita)
                .NotEmpty().WithMessage($"{nameof(ProtocolloUscitaDto.DataProtocolloInUscita)} deve essere valorizzato");

            When(dto => !string.IsNullOrWhiteSpace(dto.DataProtocolloInUscita), 
                    () => RuleFor(dto => dto.DataProtocolloInUscita)
                            .Must(data => BeAValidDate(data)).WithMessage(dto => $"{dto.DataProtocolloInUscita} non è una data valida"));
        }

        protected virtual void UtenteValidator()
        {
            RuleFor(dto => dto.Utente)
                .NotEmpty().WithMessage($"{nameof(ProtocolloUscitaDto.Utente)} deve essere valorizzato");
        }

        protected virtual void DocumentiValidator()
        {
            RuleFor(dto => dto.Documenti)
                .NotEmpty().WithMessage($"{nameof(ProtocolloUscitaDto.Documenti)} deve essere valorizzato");

            When(dto => dto.Documenti != null && dto.Documenti.Any(), () =>
            {
                AddDocumentoDtoValidator();
            });
        }

        protected virtual void AddDocumentoDtoValidator()
        {
            var validatorsFactory = new GenericValidatorsFactory<DocumentoDto>(this._conf, this._radioamatoreBl, _codiceMisura);
            RuleForEach(x => x.Documenti).SetValidator((IValidator<DocumentoDto>)validatorsFactory.validator);
        }
    }
}
