﻿using FluentValidation;
using Inv.Framework.Api.BL.DTO.Interfaces;
using Invitalia.Misure.Standard2.Utilities.Extensions;
using Microsoft.Extensions.Configuration;
using MicroSV_Radioamatori.BL.DTOs;
using MicroSV_Radioamatori.BL.Enums;
using MicroSV_Radioamatori.BL.Interfaces;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace MicroSV_Radioamatori.API.Validators.ProtocolloUscita
{
    public class ProtocolloUscitaDtoValidatorRAD5 : ProtocolloUscitaDtoValidator
    {
        private IscrizioneSWLDto _iscrizione;
        private List<IscrizioneSWLDto> _iscrizioni;

        public ProtocolloUscitaDtoValidatorRAD5(IConfiguration conf, IRadiamatoreBl radioamatoreBl, string codiceMisura) : base(conf, radioamatoreBl, codiceMisura)
        {
        }

        protected override void Init()
        {
            IdIscrizioneSWLValidator();
            DataRilascioIscrizioneSWLValidator();
            CodiceProtocolloInUscitaValidator();
            DataProtocolloInUscitaValidator();
            AltreIscrizioniSWLValidator();
            DocumentiValidator();
            UtenteValidator();
        }

        protected virtual void IdIscrizioneSWLValidator()
        {
            RuleFor(dto => dto.IdIscrizioneSWL)
                .NotEmpty().WithMessage($"{nameof(ProtocolloUscitaDto.IdIscrizioneSWL)} deve essere valorizzato");

            When(dto => dto.IdIscrizioneSWL.HasValue, () =>
            {
                RuleFor(dto => dto.IdIscrizioneSWL)
                    .Must((id) => ExistsIscrizioneSWLAsync(id.Value).GetAwaiter().GetResult())
                    .WithMessage(x => $"Non esiste nessuna iscrizione all'elenco SWL con id {x.IdIscrizioneSWL}");
            });
        }

        protected virtual void DataRilascioIscrizioneSWLValidator()
        {
            RuleFor(dto => dto.DataRilascioIscrizioneSWL)
                .NotEmpty().WithMessage($"{nameof(ProtocolloUscitaDto.DataRilascioIscrizioneSWL)} deve essere valorizzato");

            When(dto => !string.IsNullOrWhiteSpace(dto.DataRilascioIscrizioneSWL),
                    () => RuleFor(dto => dto.DataRilascioIscrizioneSWL)
                            .Must(data => BeAValidDate(data)).WithMessage(dto => $"{dto.DataRilascioIscrizioneSWL} non è una data valida"));
        }

        protected virtual void AltreIscrizioniSWLValidator()
        {
            When(dto => dto.IdIscrizioneSWL.HasValue && ExistsIscrizioneSWLAsync(dto.IdIscrizioneSWL.Value).GetAwaiter().GetResult(), () =>
            {
                RuleFor(dto => dto)
                    .Must(dto => !HasAssegnatarioAltreIscrizioneSWLAsync(dto.IdIscrizioneSWL.Value, EnumStatoIscrizioneSWL.Attiva).GetAwaiter().GetResult())
                    .WithMessage("Attenzione: il richiedente risulta già iscritto all’elenco SWL")
                    .Must(dto => !HasAssegnatarioAltreIscrizioneSWLAsync(dto.IdIscrizioneSWL.Value, EnumStatoIscrizioneSWL.InIstruttoria).GetAwaiter().GetResult())
                    .WithMessage("Attenzione: risulta già presente a sistema una richiesta di iscrizione all’elenco SWL");
            });
        }

        protected override void DocumentiValidator()
        {
            When(dto => dto.Documenti != null && dto.Documenti.Any(), () =>
            {
                AddDocumentoDtoValidator();

                //verifico presenza di uno e un solo documento di tipo ATTESTATO_ASCOLTO
                RuleFor(dto => dto.Documenti)
                    .Must(documenti => documenti.Any(doc => doc.TipoDocumento.EqualsIgnoreCaseAndTrim(TipoDocumentoIscrizioneSWL.ATTESTATO_ASCOLTO.ToString())))
                    .WithMessage($"Deve essere comunicato un documento di tipo {TipoDocumentoIscrizioneSWL.ATTESTATO_ASCOLTO}");

                RuleFor(dto => dto.Documenti)
                    .Must(documenti => documenti.Where(doc => doc.TipoDocumento.EqualsIgnoreCaseAndTrim(TipoDocumentoIscrizioneSWL.ATTESTATO_ASCOLTO.ToString())).Count() <= 1)
                    .WithMessage($"Non può essere comunicato più di un documento di tipo {TipoDocumentoIscrizioneSWL.ATTESTATO_ASCOLTO}");
            });
        }

        #region private methods

        private async Task<IscrizioneSWLDto> GetIscrizioneSWLAsync(int idIscrizioneSWL)
        {
            return _iscrizione ??= (await _radioamatoreBl.GetIscrizioneSWLByIdAsync(idIscrizioneSWL));
        }

        private async Task<List<IscrizioneSWLDto>> GetIscrizioniSWLAssegnatarioAsync(string cfAssegnatario)
        {
            return _iscrizioni ??= (await _radioamatoreBl.GetIscrizioniSWLByFilterAsync(new IscrizioneSWLFilterDto() { CodiceFiscale = cfAssegnatario }));
        }

        private async Task<bool> ExistsIscrizioneSWLAsync(int idAutorizzazione)
        {
            return await GetIscrizioneSWLAsync(idAutorizzazione) != null;
        }

        private async Task<bool> HasAssegnatarioAltreIscrizioneSWLAsync(int idIscrizioneSWL, EnumStatoIscrizioneSWL statoIscrizioneSWL)
        {
            var current = await GetIscrizioneSWLAsync(idIscrizioneSWL);
            var iscrizioni = await GetIscrizioniSWLAssegnatarioAsync(current.CodiceFiscale);

            return iscrizioni != null && iscrizioni.Any(i => i.Id != idIscrizioneSWL && i.IdStatoIscrizioneSWL == (int)statoIscrizioneSWL);
        }

        #endregion
    }
}
