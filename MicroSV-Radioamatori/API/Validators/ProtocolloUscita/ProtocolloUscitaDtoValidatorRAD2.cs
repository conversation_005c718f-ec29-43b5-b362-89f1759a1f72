﻿using FluentValidation;
using Invitalia.Misure.Standard2.Utilities.Extensions;
using Microsoft.Extensions.Configuration;
using MicroSV_Radioamatori.BL.DTOs;
using MicroSV_Radioamatori.BL.Enums;
using MicroSV_Radioamatori.BL.Interfaces;
using System.Linq;

namespace MicroSV_Radioamatori.API.Validators.ProtocolloUscita
{
    public class ProtocolloUscitaDtoValidatorRAD2 : ProtocolloUscitaDtoValidator
    {
        public ProtocolloUscitaDtoValidatorRAD2(IConfiguration conf, IRadiamatoreBl radioamatoreBl, string codiceMisura) : base(conf, radioamatoreBl, codiceMisura)
        {
        }

        protected override void Init()
        {
            CodicePatenteValidator();
            DataRilascioPatenteValidator();
            CodiceProtocolloInUscitaValidator();
            DataProtocolloInUscitaValidator();
            UtenteValidator();
            DocumentiValidator();
        }

        protected virtual void CodicePatenteValidator()
        {
            RuleFor(dto => dto.CodicePatente)
                .NotEmpty().WithMessage($"{nameof(ProtocolloUscitaDto.CodicePatente)} deve essere valorizzato");

            When(dto => !string.IsNullOrWhiteSpace(dto.CodicePatente), () =>
            {
                RuleFor(dto => dto.CodicePatente)
                    .Must((codice) => _radioamatoreBl.ExistsPatenteByCodiceAsync(codice).GetAwaiter().GetResult())
                    .WithMessage(x => $"Non esiste nessuna patente con codice {x.CodicePatente}");

                RuleFor(dto => dto.CodicePatente)
                    .Must((codice) => IsPatenteAttiva(codice))
                    .WithMessage(x => $"La patente {x.CodicePatente} non è in stato {BL.Enums.StatoPatente.Attiva.GetAttributeDescription()}");
            });
        }

        protected virtual void DataRilascioPatenteValidator()
        {
            RuleFor(dto => dto.DataRilascioPatente)
                .NotEmpty().WithMessage($"{nameof(ProtocolloUscitaDto.DataRilascioPatente)} deve essere valorizzato");

            When(dto => !string.IsNullOrWhiteSpace(dto.DataRilascioPatente),
                    () => RuleFor(dto => dto.DataRilascioPatente)
                            .Must(data => BeAValidDate(data)).WithMessage(dto => $"{dto.DataRilascioPatente} non è una data valida"));
        }

        protected override void DocumentiValidator()
        {
            base.DocumentiValidator();

            When(dto => dto.Documenti != null && dto.Documenti.Any(), () =>
            {
                //verifico presenza di uno e un solo documento di tipo PATENTE
                RuleFor(dto => dto.Documenti)
                    .Must(documenti => documenti.Any(doc => doc.TipoDocumento.Equals(TipoDocumentoPatente.PATENTE.ToString(), System.StringComparison.InvariantCultureIgnoreCase)))
                    .WithMessage($"Deve essere comunicato un documento di tipo {TipoDocumentoPatente.PATENTE}");

                RuleFor(dto => dto.Documenti)
                    .Must(documenti => documenti.Where(doc => doc.TipoDocumento.Equals(TipoDocumentoPatente.PATENTE.ToString(), System.StringComparison.InvariantCultureIgnoreCase)).Count() <= 1)
                    .WithMessage($"Non può essere comunicato più di un documento di tipo {TipoDocumentoPatente.PATENTE}");

                //verifico presenza di uno e un solo documento di tipo HAREC
                RuleFor(dto => dto.Documenti)
                    .Must(documenti => documenti.Any(doc => doc.TipoDocumento.Equals(TipoDocumentoPatente.HAREC.ToString(), System.StringComparison.InvariantCultureIgnoreCase)))
                    .WithMessage($"Deve essere comunicato un documento di tipo {TipoDocumentoPatente.HAREC}");

                RuleFor(dto => dto.Documenti)
                    .Must(documenti => documenti.Where(doc => doc.TipoDocumento.Equals(TipoDocumentoPatente.HAREC.ToString(), System.StringComparison.InvariantCultureIgnoreCase)).Count() <= 1)
                    .WithMessage($"Non può essere comunicato più di un documento di tipo {TipoDocumentoPatente.HAREC}");
            });
        }

        #region private methods
        private bool IsPatenteAttiva(string codicePatente)
        {
            var patente = _radioamatoreBl.GetPatenteByCodiceAsync(codicePatente).GetAwaiter().GetResult();
            return patente != null && patente.IdStatoPatente == (int)BL.Enums.StatoPatente.Attiva;
        }
        #endregion

    }
}
