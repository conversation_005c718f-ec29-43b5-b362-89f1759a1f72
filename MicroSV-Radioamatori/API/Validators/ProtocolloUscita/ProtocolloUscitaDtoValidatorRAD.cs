﻿using FluentValidation;
using Invitalia.Misure.Standard2.Utilities.Extensions;
using Microsoft.Extensions.Configuration;
using MicroSV_Radioamatori.BL.DTOs;
using MicroSV_Radioamatori.BL.Enums;
using MicroSV_Radioamatori.BL.Interfaces;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace MicroSV_Radioamatori.API.Validators.ProtocolloUscita
{
    public class ProtocolloUscitaDtoValidatorRAD : ProtocolloUscitaDtoValidator
    {
        private RadioamatoreDto _radioamatore;

        public ProtocolloUscitaDtoValidatorRAD(IConfiguration conf, IRadiamatoreBl radioamatoreBl, string codiceMisura) : base(conf, radioamatoreBl, codiceMisura)
        {
        }

        protected override void Init()
        {
            CodiceDomandaFEValidator();
            CodiceNominativoValidator();
            DataRilascioNominativoValidator();
            CodiceProtocolloInUscitaValidator();
            DataProtocolloInUscitaValidator();
            StatoNominativoValidator();
            CorrispondenzaDomandaNominativoValidator();
        }

        protected virtual void CodiceDomandaFEValidator()
        {
            RuleFor(dto => dto.CodiceDomandaFE)
                .NotEmpty().WithMessage($"{nameof(ProtocolloUscitaDto.CodiceDomandaFE)} deve essere valorizzato");

            When(dto => !string.IsNullOrWhiteSpace(dto.CodiceDomandaFE), () =>
            {
                RuleFor(dto => dto.CodiceDomandaFE)
                    .Must((codiceDomanda) => ExistsDomandaAsync(codiceDomanda).GetAwaiter().GetResult())
                    .WithMessage(x => $"Non esiste nessuna autorizzazione con id {x.IdAutorizzazione}");
            });
        }
        
        protected virtual void CodiceNominativoValidator()
        {
            RuleFor(dto => dto.CodiceNominativo)
                .NotEmpty().WithMessage($"{nameof(ProtocolloUscitaDto.CodiceDomandaFE)} deve essere valorizzato");
        }

        protected virtual void DataRilascioNominativoValidator()
        {
            RuleFor(dto => dto.DataRilascioNominativo)
                .NotEmpty().WithMessage($"{nameof(ProtocolloUscitaDto.DataRilascioNominativo)} deve essere valorizzato");

            When(dto => !string.IsNullOrWhiteSpace(dto.DataRilascioNominativo), () =>
            {
                RuleFor(dto => dto.DataRilascioNominativo)
                                .Must(data => BeAValidDate(data))
                                .WithMessage(dto => $"{dto.DataRilascioNominativo} non è una data valida");
            });
        }

        protected virtual void StatoNominativoValidator()
        {
            When(x => ExistsDomandaAsync(x.CodiceDomandaFE).GetAwaiter().GetResult(), () =>
            {
                RuleFor(x => x)
                    .Must(x => IsNominativoInStatoAsync(x.CodiceDomandaFE, EnumStatoNominativo.IN_ISTRUTTORIA).GetAwaiter().GetResult())
                    .WithMessage("Attenzione! Il nominativo non è in stato IN ISTRUTTORIA");
            });
        }
        
        protected virtual void CorrispondenzaDomandaNominativoValidator()
        {
            When(x => !string.IsNullOrWhiteSpace(x.CodiceNominativo) && ExistsDomandaAsync(x.CodiceDomandaFE).GetAwaiter().GetResult(), () =>
            {
                RuleFor(x => x)
                    .Must(x => IsDomandaAbbinataNominativoAsync(x.CodiceDomandaFE, x.CodiceNominativo).GetAwaiter().GetResult())
                    .WithMessage("Attenzione! La domanda comunicata non è abbinata al nominativo indicato");
            });
        }

        #region private methods

        private async Task<RadioamatoreDto> GetRadioamatoreByCodiceDomandaAsync(string codiceDomanda)
        {
            return _radioamatore ??= (await _radioamatoreBl.GetRadioamatoreByCodiceDomandaFEAsync(codiceDomanda));
        }

        private async Task<bool> ExistsDomandaAsync(string codiceDomanda)
        {
            var radioamatore = await GetRadioamatoreByCodiceDomandaAsync(codiceDomanda);

            return radioamatore != null;
        }

        private async Task<bool> IsNominativoInStatoAsync(string codiceDomanda, EnumStatoNominativo statoNominativo)
        {
            var radioamatore = await GetRadioamatoreByCodiceDomandaAsync(codiceDomanda);

            return radioamatore?.StatoNominativo?.EqualsIgnoreCaseAndTrim(statoNominativo.ToString()) ?? false;
        }
        
        private async Task<bool> IsDomandaAbbinataNominativoAsync(string codiceDomanda, string nominativo)
        {
            var radioamatore = await GetRadioamatoreByCodiceDomandaAsync(codiceDomanda);

            return radioamatore?.CodiceNominativo?.EqualsIgnoreCaseAndTrim(nominativo) ?? false;
        }

        #endregion
    }
}
