﻿using FluentValidation;
using Invitalia.Misure.Standard2.Utilities.Extensions;
using Microsoft.Extensions.Configuration;
using MicroSV_Radioamatori.BL.DTOs;
using MicroSV_Radioamatori.BL.Enums;
using MicroSV_Radioamatori.BL.Interfaces;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace MicroSV_Radioamatori.API.Validators.ProtocolloUscita
{
    public class ProtocolloUscitaDtoValidatorRAD3 : ProtocolloUscitaDtoValidator
    {
        private AutorizzazioneDto _autorizzazione;
        private List<AutorizzazioneDto> _autorizzazioniAssegnatario;

        public ProtocolloUscitaDtoValidatorRAD3(IConfiguration conf, IRadiamatoreBl radioamatoreBl, string codiceMisura) : base(conf, radioamatoreBl, codiceMisura)
        {
        }

        protected override void Init()
        {
            IdAutorizzazioneValidator();
            DataRilascioAutorizzazioneValidator();
            CodiceProtocolloInUscitaValidator();
            DataProtocolloInUscitaValidator();
            UtenteValidator();
            DocumentiValidator();
            AltreAutorizzazioniValidator();
        }

        protected virtual void IdAutorizzazioneValidator()
        {
            RuleFor(dto => dto.IdAutorizzazione)
                .NotEmpty().WithMessage($"{nameof(ProtocolloUscitaDto.IdAutorizzazione)} deve essere valorizzato");

            When(dto => dto.IdAutorizzazione.HasValue, () =>
            {
                RuleFor(dto => dto.IdAutorizzazione)
                    .Must((id) => ExistsAutorizzazioneAsync(id.Value).GetAwaiter().GetResult())
                    .WithMessage(x => $"Non esiste nessuna autorizzazione con id {x.IdAutorizzazione}");
            });
        }

        protected virtual void DataRilascioAutorizzazioneValidator()
        {
            RuleFor(dto => dto.DataRilascioAutorizzazione)
                .NotEmpty().WithMessage($"{nameof(ProtocolloUscitaDto.DataRilascioAutorizzazione)} deve essere valorizzato");

            When(dto => !string.IsNullOrWhiteSpace(dto.DataRilascioAutorizzazione),
                    () => RuleFor(dto => dto.DataRilascioAutorizzazione)
                            .Must(data => BeAValidDate(data)).WithMessage(dto => $"{dto.DataRilascioAutorizzazione} non è una data valida"));
        }

        protected override void DocumentiValidator()
        {
            When(dto => dto.Documenti != null && dto.Documenti.Any(), () =>
            {
                AddDocumentoDtoValidator();

                //verifico presenza di uno e un solo documento di tipo AUTORIZZAZIONE_GENERALE
                RuleFor(dto => dto.Documenti)
                    .Must(documenti => documenti.Any(doc => doc.TipoDocumento.EqualsIgnoreCaseAndTrim(TipoDocumentoAutorizzazione.AUTORIZZAZIONE_GENERALE.ToString())))
                    .WithMessage($"Deve essere comunicato un documento di tipo {TipoDocumentoAutorizzazione.AUTORIZZAZIONE_GENERALE}");

                RuleFor(dto => dto.Documenti)
                    .Must(documenti => documenti.Where(doc => doc.TipoDocumento.EqualsIgnoreCaseAndTrim(TipoDocumentoAutorizzazione.AUTORIZZAZIONE_GENERALE.ToString())).Count() <= 1)
                    .WithMessage($"Non può essere comunicato più di un documento di tipo {TipoDocumentoAutorizzazione.AUTORIZZAZIONE_GENERALE}");
            });
        }

        protected virtual void AltreAutorizzazioniValidator()
        {
            When((dto) => dto.IdAutorizzazione.HasValue, () =>
            {
                When((dto) => !IsRinnovoAsync(dto.IdAutorizzazione.Value).GetAwaiter().GetResult(), () =>
                {
                    RuleFor(x => x.IdAutorizzazione)
                        .Must((id) => !HasAssegnatarioAltraAutorizzazioneByStatoAsync(id.Value, (short)EnumStatoAutorizzazione.Attiva).GetAwaiter().GetResult())
                            .WithMessage($"Attenzione: l’assegnatario risulta già essere in possesso di autorizzazione generale")
                        .Must((id) => !HasAssegnatarioAltraAutorizzazioneByStatoAsync(id.Value, (short)EnumStatoAutorizzazione.InIstruttoria).GetAwaiter().GetResult())
                            .WithMessage($"Attenzione: è già presente una richiesta di autorizzazione generale in istruttoria")
                        .Must((id) => !HasAssegnatarioAltraAutorizzazioneByStatoAsync(id.Value, (short)EnumStatoAutorizzazione.InScadenza).GetAwaiter().GetResult())
                            .WithMessage($"Attenzione: l’autorizzazione risulta in scadenza, è necessario procedere con il rinnovo")
                        .Must((id) => !HasAssegnatarioAltraAutorizzazioneByStatoAsync(id.Value, (short)EnumStatoAutorizzazione.Rinnovo).GetAwaiter().GetResult())
                            .WithMessage($"Attenzione: è già presente una richiesta di rinnovo dell'autorizzazione generale in istruttoria");
                })
                .Otherwise(() =>
                {
                    RuleFor(x => x.IdAutorizzazione)
                        .Must((id) => IsAutorizzazioneCorrenteInStatoAsync(id.Value, (short)EnumStatoAutorizzazione.Rinnovo).GetAwaiter().GetResult())
                            .WithMessage($"Attenzione: l’autorizzazione corrente non è in stato {EnumStatoAutorizzazione.Rinnovo.GetAttributeDescription()}")
                        .Must((id) => IsRenewableAsync(id.Value).GetAwaiter().GetResult())
                            .WithMessage($"Attenzione: l’autorizzazione che si intende rinnovare non è in stato {EnumStatoAutorizzazione.InScadenza.GetAttributeDescription()}");

                });
            });
        }

        #region private methods

        private async Task<AutorizzazioneDto> GetAutorizzazioneAsync(int idAutorizzazione)
        {
            return _autorizzazione ??= (await _radioamatoreBl.GetAutorizzazioneByIdAsync(idAutorizzazione));
        }

        private async Task<List<AutorizzazioneDto>> GetAutorizzazioniAssegnatarioAsync(string cfAssegnatario)
        {
            return _autorizzazioniAssegnatario ??= (await _radioamatoreBl.GetAutorizzazioneByFilterAsync(new AutorizzazioneFilterDto() { CodiceFiscale = cfAssegnatario }));
        }

        private async Task<bool> ExistsAutorizzazioneAsync(int idAutorizzazione)
        {
            var autorizzazione = await GetAutorizzazioneAsync(idAutorizzazione);

            return autorizzazione != null;
        }

        private async Task<bool> IsRinnovoAsync(int idAutorizzazione)
        {
            var autorizzazione = await GetAutorizzazioneAsync(idAutorizzazione);

            return autorizzazione?.IsRinnovo ?? false;
        }

        private async Task<bool> IsAutorizzazioneCorrenteInStatoAsync(int idAutorizzazione, short statoAutorizzazione)
        {
            var autorizzazione = await GetAutorizzazioneAsync(idAutorizzazione);

            return autorizzazione != null && autorizzazione.IdStatoAutorizzazione == statoAutorizzazione;
        }

        private async Task<bool> HasAssegnatarioAltraAutorizzazioneByStatoAsync(int idAutorizzazione, short statoAutorizzazione)
        {
            var result = false;

            var autorizzazione = await GetAutorizzazioneAsync(idAutorizzazione);
            if (autorizzazione != null)
            {
                var autorizzazioniAssegnatario = await GetAutorizzazioniAssegnatarioAsync(autorizzazione.CodiceFiscale);
                result = autorizzazioniAssegnatario != null && autorizzazioniAssegnatario.Any(a => a.Id != autorizzazione.Id && a.IdStatoAutorizzazione == statoAutorizzazione);
            }

            return result;
        }

        private async Task<bool> IsRenewableAsync(int idAutorizzazione)
        {
            bool isValid = false;
            var autorizzazione = await GetAutorizzazioneAsync(idAutorizzazione);

            if (autorizzazione != null)
            {
                if (!autorizzazione.IdAutorizzazioneRinnovata.HasValue)
                {
                    isValid = true;
                }
                else
                {
                    var autorizzazioneDaRinnovare = await _radioamatoreBl.GetAutorizzazioneByIdAsync(autorizzazione.IdAutorizzazioneRinnovata.Value);
                    isValid = autorizzazioneDaRinnovare != null && autorizzazioneDaRinnovare.IdStatoAutorizzazione == (short)EnumStatoAutorizzazione.InScadenza;
                }
            }

            return isValid;
        }

        #endregion
    }
}
