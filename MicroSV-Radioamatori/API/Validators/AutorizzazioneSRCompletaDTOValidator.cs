﻿using FluentValidation;
using Invitalia.Misure.Standard2.Utilities.Extensions;
using Microsoft.Extensions.Configuration;
using MicroSV_Radioamatori.BL.DTOs;
using MicroSV_Radioamatori.BL.Enums;
using MicroSV_Radioamatori.BL.Interfaces;
using System.Linq;

namespace MicroSV_Radioamatori.API.Validators
{
    public class AutorizzazioneSRCompletaDTOValidator : AbstractValidator<AutorizzazioneSRCompletaDto>
    {
        private readonly IConfiguration _conf;
        protected readonly string timeZoneId;
        private readonly IRadiamatoreBl _radioamatoreBl;
        private readonly Operation _operation;
        private readonly RequestOrigin _origin;

        private AutorizzazioneSRDto _autorizzazione;
        private RadioamatoreSRDto _radioamatore;

        public AutorizzazioneSRCompletaDTOValidator(IConfiguration conf, IRadiamatoreBl radioamatoreBl, RequestOrigin requestOrigin, Operation operation)
        {
            this._conf = conf;
            this._radioamatoreBl = radioamatoreBl;
            this._operation = operation;
            this._origin = requestOrigin;
            Init();
        }

        private void Init()
        {
            RuleFor(x => x)
                .NotNull().WithMessage("Autorizzazione completa deve essere valorizzato");

            switch (_operation)
            {
                case Operation.Insert:
                    RadioamatoreValidator();
                    StazioneValidator();
                    AutorizzazioneValidator();
                    break;
                case Operation.CambioStato:
                    IdAutorizzazioneValidator();
                    PresenzaAutorizzazioneByIdValidator();
                    CambioStatoAutorizzazioneValidator();
                    break;
                case Operation.UpdateNominativoAssegnatario:
                    IdAutorizzazioneValidator();
                    PresenzaAutorizzazioneByIdValidator();
                    //AutorizzazioneSenzaNominativoValidator();
                    IdRadioamatoreValidator();
                    PresenzaRadioamatoreByIdValidator();
                    NominativoAttivoValidator();
                    CorrispondenzaCFValidator();
                    AssenzaAltreAutorizzazioniAttiveScadenzaValidator();
                    break;
                default:
                    break;
            }

        }

        protected virtual void RadioamatoreValidator()
        {

            RuleFor(x => x.Radioamatore).NotNull().WithMessage("Radioamatore deve essere valorizzato");

            When(x => x.Autorizzazione.IdTipologiaAssegnatario == 1 || x.Autorizzazione.IdTipologiaAssegnatario == 2, () =>
            {
                RuleFor(x => x.Radioamatore.Nome).NotEmpty().WithMessage("{PropertyName} deve essere valorizzato");
                RuleFor(x => x.Radioamatore.Cognome).NotEmpty().WithMessage("{PropertyName} deve essere valorizzato");
                RuleFor(x => x.Radioamatore.DataDiNascita).NotEmpty().WithMessage("{PropertyName} deve essere valorizzato");
            })
            .Otherwise(() =>
            {
                RuleFor(x => x.Radioamatore.Denominazione).NotEmpty().WithMessage("{PropertyName} deve essere valorizzato");
            });
            RuleFor(x => x.Radioamatore.CodiceFiscale).NotEmpty().WithMessage("{PropertyName} deve essere valorizzato");
            RuleFor(x => x.Radioamatore.Regione).NotEmpty().WithMessage("{PropertyName} deve essere valorizzato");
            RuleFor(x => x.Radioamatore.Indirizzo).NotEmpty().WithMessage("{PropertyName} deve essere valorizzato");
            RuleFor(x => x.Radioamatore.Comune).NotEmpty().WithMessage("{PropertyName} deve essere valorizzato");
            RuleFor(x => x.Radioamatore.Provincia).NotEmpty().WithMessage("{PropertyName} deve essere valorizzato");
            RuleFor(x => x.Radioamatore.Cap).NotEmpty().WithMessage("{PropertyName} deve essere valorizzato");
            RuleFor(x => x.Radioamatore.IdAnagraficaIspettorato).NotEmpty().WithMessage("{PropertyName} deve essere valorizzato");
        }

        protected virtual void StazioneValidator()
        {
            RuleFor(x => x.StazioneRipetitrice).NotNull().WithMessage("Radioamatore deve essere valorizzato");

            RuleFor(x => x.StazioneRipetitrice.Regione).NotEmpty().WithMessage("{PropertyName} deve essere valorizzato");
            RuleFor(x => x.StazioneRipetitrice.CodiceRegione).NotEmpty().WithMessage("{PropertyName} deve essere valorizzato");
            RuleFor(x => x.StazioneRipetitrice.Provincia).NotEmpty().WithMessage("{PropertyName} deve essere valorizzato");
            RuleFor(x => x.StazioneRipetitrice.CodiceProvincia).NotEmpty().WithMessage("{PropertyName} deve essere valorizzato");
            RuleFor(x => x.StazioneRipetitrice.Comune).NotEmpty().WithMessage("{PropertyName} deve essere valorizzato");
            RuleFor(x => x.StazioneRipetitrice.CodiceComune).NotEmpty().WithMessage("{PropertyName} deve essere valorizzato");
            RuleFor(x => x.StazioneRipetitrice.Indirizzo).NotEmpty().WithMessage("{PropertyName} deve essere valorizzato");
            RuleFor(x => x.StazioneRipetitrice.Civico).NotEmpty().WithMessage("{PropertyName} deve essere valorizzato");
            RuleFor(x => x.StazioneRipetitrice.Cap).NotEmpty().WithMessage("{PropertyName} deve essere valorizzato");
            RuleFor(x => x.StazioneRipetitrice.Latitudine).NotEmpty().WithMessage("{PropertyName} deve essere valorizzato");
            RuleFor(x => x.StazioneRipetitrice.Longitudine).NotEmpty().WithMessage("{PropertyName} deve essere valorizzato");
            RuleFor(x => x.StazioneRipetitrice.NomeOperatore).NotEmpty().WithMessage("{PropertyName} deve essere valorizzato");
            RuleFor(x => x.StazioneRipetitrice.CognomeOperatore).NotEmpty().WithMessage("{PropertyName} deve essere valorizzato");
            RuleFor(x => x.StazioneRipetitrice.CodiceFiscaleOperatore).NotEmpty().WithMessage("{PropertyName} deve essere valorizzato");
            RuleFor(x => x.StazioneRipetitrice.RegioneResidenzaOperatore).NotEmpty().WithMessage("{PropertyName} deve essere valorizzato");
            RuleFor(x => x.StazioneRipetitrice.CodiceRegioneResidenzaOperatore).NotEmpty().WithMessage("{PropertyName} deve essere valorizzato");
            RuleFor(x => x.StazioneRipetitrice.ProvinciaResidenzaOperatore).NotEmpty().WithMessage("{PropertyName} deve essere valorizzato");
            RuleFor(x => x.StazioneRipetitrice.CodiceProvinciaResidenzaOperatore).NotEmpty().WithMessage("{PropertyName} deve essere valorizzato");
            RuleFor(x => x.StazioneRipetitrice.ComuneResidenzaOperatore).NotNull().WithMessage("{PropertyName} deve essere valorizzato");
            RuleFor(x => x.StazioneRipetitrice.CodiceComuneResidenzaOperatore).NotEmpty().WithMessage("{PropertyName} deve essere valorizzato");
            RuleFor(x => x.StazioneRipetitrice.IndirizzoResidenzaOperatore).NotEmpty().WithMessage("{PropertyName} deve essere valorizzato");
            RuleFor(x => x.StazioneRipetitrice.NumeroCivicoResidenzaOperatore).NotEmpty().WithMessage("{PropertyName} deve essere valorizzato");
            RuleFor(x => x.StazioneRipetitrice.CapResidenzaOperatore).NotEmpty().WithMessage("{PropertyName} deve essere valorizzato");
            RuleFor(x => x.StazioneRipetitrice.EmailOperatore).NotEmpty().WithMessage("{PropertyName} deve essere valorizzato");
            RuleFor(x => x.StazioneRipetitrice.TelefonoOperatore).NotEmpty().WithMessage("{PropertyName} deve essere valorizzato");
        }

        protected virtual void AutorizzazioneValidator()
        {
            RuleFor(x => x.Autorizzazione).NotNull().WithMessage("Radioamatore deve essere valorizzato");

            RuleFor(x => x.Autorizzazione.IdTipologiaAssegnatario).NotEmpty().WithMessage("{PropertyName} deve essere valorizzato");

            When(x => x.Autorizzazione.IdTipologiaAssegnatario == 1 || x.Autorizzazione.IdTipologiaAssegnatario == 2, () =>
            {
                RuleFor(x => x.Autorizzazione.Nome).NotEmpty().WithMessage("{PropertyName} deve essere valorizzato");
                RuleFor(x => x.Autorizzazione.Cognome).NotEmpty().WithMessage("{PropertyName} deve essere valorizzato");
                RuleFor(x => x.Autorizzazione.Sesso).NotEmpty().WithMessage("{PropertyName} deve essere valorizzato");
                RuleFor(x => x.Autorizzazione.LuogoDiNascita).NotEmpty().WithMessage("{PropertyName} deve essere valorizzato");
                RuleFor(x => x.Autorizzazione.CodiceLuogoDiNascita).NotEmpty().WithMessage("{PropertyName} deve essere valorizzato");
                RuleFor(x => x.Autorizzazione.DataDiNascita).NotEmpty().WithMessage("{PropertyName} deve essere valorizzato");
            })
            .Otherwise(() =>
            {
                RuleFor(x => x.Autorizzazione.Denominazione).NotEmpty().WithMessage("{PropertyName} deve essere valorizzato");
            });

            RuleFor(x => x.Autorizzazione.CodiceDomanda).NotEmpty().WithMessage("{PropertyName} deve essere valorizzato");
            RuleFor(x => x.Autorizzazione.CodiceFiscale).NotEmpty().WithMessage("{PropertyName} deve essere valorizzato");
            RuleFor(x => x.Autorizzazione.RegioneResidenza).NotEmpty().WithMessage("{PropertyName} deve essere valorizzato");
            RuleFor(x => x.Autorizzazione.CodiceRegioneResidenza).NotEmpty().WithMessage("{PropertyName} deve essere valorizzato");
            RuleFor(x => x.Autorizzazione.ProvinciaResidenza).NotEmpty().WithMessage("{PropertyName} deve essere valorizzato");
            RuleFor(x => x.Autorizzazione.CodiceProvinciaResidenza).NotEmpty().WithMessage("{PropertyName} deve essere valorizzato");
            RuleFor(x => x.Autorizzazione.ComuneResidenza).NotEmpty().WithMessage("{PropertyName} deve essere valorizzato");
            RuleFor(x => x.Autorizzazione.CodiceComuneResidenza).NotEmpty().WithMessage("{PropertyName} deve essere valorizzato");
            RuleFor(x => x.Autorizzazione.IndirizzoResidenza).NotEmpty().WithMessage("{PropertyName} deve essere valorizzato");
            RuleFor(x => x.Autorizzazione.CapResidenza).NotEmpty().WithMessage("{PropertyName} deve essere valorizzato");
            RuleFor(x => x.Autorizzazione.IdIspettorato).NotEmpty().WithMessage("{PropertyName} deve essere valorizzato");
            RuleFor(x => x.Autorizzazione.Pec).NotEmpty().WithMessage("{PropertyName} deve essere valorizzato");
            RuleFor(x => x.Autorizzazione.Email).NotEmpty().WithMessage("{PropertyName} deve essere valorizzato");
            RuleFor(x => x.Autorizzazione.Telefono).NotEmpty().WithMessage("{PropertyName} deve essere valorizzato");
            RuleFor(x => x.Autorizzazione.IdTipologiaAssegnatario).NotEmpty().WithMessage("{PropertyName} deve essere valorizzato");
            RuleFor(x => x.Autorizzazione.Rinnovo).NotEmpty().WithMessage("{PropertyName} deve essere valorizzato");
            RuleFor(x => x.Autorizzazione.IdTipologiaAutorizzazione).NotEmpty().WithMessage("{PropertyName} deve essere valorizzato");
        }

        protected virtual void IdAutorizzazioneValidator()
        {
            RuleFor(x => x.Autorizzazione.Id)
                .NotEmpty().WithMessage($"{nameof(AutorizzazioneSRDto.Id)} deve essere valorizzato");
        }

        protected virtual void PresenzaAutorizzazioneByIdValidator()
        {
            When(x => x.Autorizzazione.Id != default, () =>
            {
                RuleFor(x => x.Autorizzazione.Id)
                    .Must((id) => ExistsAutorizzazioneSRById(id.Value))
                    .WithMessage(x => $"Non esiste nessuna autorizzazione con id {x.Autorizzazione.Id}");
            });
        }

        protected virtual void PresenzaRadioamatoreByIdValidator()
        {
            When(x => x.Radioamatore.IdRadioamatore != default, () =>
            {
                RuleFor(x => x.Radioamatore.IdRadioamatore)
                    .Must((id) => ExistsRadioamatoreSRById(id))
                    .WithMessage(x => $"Non esiste nessun radioamatore con id {x.Radioamatore.IdRadioamatore}");
            });
        }

        protected virtual void RadioamatoreAttivoValidator()
        {
            When(x => x.Radioamatore.IdRadioamatore != default, () =>
            {
                RuleFor(x => x.Radioamatore.IdRadioamatore)
                    .Must((id) => ExistsRadioamatoreSRById(id))
                    .WithMessage(x => $"Non esiste nessun radioamatore con id {x.Radioamatore.IdRadioamatore}");
            });
        }

        protected virtual void CambioStatoAutorizzazioneValidator()
        {
            When(x => x.Autorizzazione.Id != default, () =>
            {
                RuleFor(x => x)
                    .Must((request) =>
                    {
                        var cambioConsentito = false;
                        var autorizzazione = GetAutorizzazioneSRById(request.Autorizzazione.Id.Value);

                        if (autorizzazione != null)
                        {
                            //lo stato autorizzazione nella request è lo stato TO BE,
                            //bisogna verificare se l'autorizzazione recuperata da db è in uno stato tale da consentire il cambio con quello comunicato
                            switch (request.Autorizzazione.IdStatoAutorizzazione)
                            {
                                case (int)EnumStatoAutorizzazione.Rinunciata:
                                    cambioConsentito = true;
                                    break;
                                case (int)EnumStatoAutorizzazione.Cancellata:
                                    cambioConsentito = autorizzazione.IdStatoAutorizzazione == (int)EnumStatoAutorizzazione.InIstruttoria || autorizzazione.IdStatoAutorizzazione == (int)EnumStatoAutorizzazione.Rinnovo;
                                    break;
                                default:
                                    break;
                            }
                        }

                        return cambioConsentito;
                    })
                    .WithMessage(x => $"L'autorizzazione con id {x.Autorizzazione.Id} non può essere portata nello stato {((EnumStatoAutorizzazione)x.Autorizzazione.IdStatoAutorizzazione).GetAttributeDescription()} in base al suo stato attuale.");
            });
        }

        protected virtual void IdRadioamatoreValidator()
        {
            RuleFor(x => x.Radioamatore.IdRadioamatore)
                .NotEmpty()
                .WithMessage($"{nameof(RadioamatoreSRDto.IdRadioamatore)} deve essere valorizzato");
        }

        protected virtual void AutorizzazioneSenzaNominativoValidator()
        {
            When(x => x.Autorizzazione.Id.HasValue, () =>
            {
                RuleFor(x => x.Autorizzazione)
                    .Must(x => !IsAutorizzazioneAssociataNominativo(x.Id.Value))
                    .WithMessage("L'autorizzazione ha già un nominativo associato");
            });
        }

        protected virtual void NominativoAttivoValidator()
        {
            When(x => x.Radioamatore.IdRadioamatore != default, () =>
            {
                RuleFor(x => x.Radioamatore.IdRadioamatore)
                    .Must((id) => IsRadioamatoreSRAttivoById(id))
                    .WithMessage(x => $"L'id radioamatore {x.Radioamatore.IdRadioamatore} non corrisponde a un nominativo attivo");
            });
        }

        protected virtual void CorrispondenzaCFValidator()
        {
            When(x => x.Autorizzazione.Id.HasValue && x.Radioamatore.IdRadioamatore != default, () =>
            {
                RuleFor(x => x)
                    .Must((x) => AreCFCorrispondenti(x.Autorizzazione.Id.Value, x.Radioamatore.IdRadioamatore))
                    .WithMessage(x => $"Il CF dell'assegnatario autorizzazione non corrisponde al CF associato al nominativo comunicato");
            });
        }

        protected virtual void AssenzaAltreAutorizzazioniAttiveScadenzaValidator()
        {
            When(x => x.Radioamatore.IdRadioamatore != default, () =>
            {
                RuleFor(x => x)
                    .Must((x) => IsRadioamatoreSRValidoById(x.Radioamatore.IdRadioamatore))
                    .WithMessage(x => $"L'id radiomatore cominicato è già associato ad autorizzazioni attive o in scadenza");
            });
        }

        private bool ExistsAutorizzazioneSRById(int idAutorizzazione)
        {
            return GetAutorizzazioneSRById(idAutorizzazione) != null;
        }

        private bool ExistsRadioamatoreSRById(int idRadioamatore)
        {
            return GetRadioamatoreSRById(idRadioamatore) != null;
        }

        private bool IsRadioamatoreSRAttivoById(int idRadioamatore)
        {
            var radioamatore = GetRadioamatoreSRById(idRadioamatore);
            return radioamatore != null && radioamatore.StatoNominativo.EqualsIgnoreCaseAndTrim("ATTIVO");
        }

        private bool IsRadioamatoreSRValidoById(int idRadioamatore)
        {
            var autorizzazioni = _radioamatoreBl.GetAutorizzazioneSRByFilterAsync(new AutorizzazioneSRFilterDto()
            {
                IdRadioamatore = idRadioamatore,
            }).GetAwaiter().GetResult();

            return autorizzazioni == null || !autorizzazioni.Any(r => r.IdStatoAutorizzazione == (int)EnumStatoAutorizzazione.Attiva || r.IdStatoAutorizzazione == (int)EnumStatoAutorizzazione.InScadenza);
        }

        private bool AreCFCorrispondenti(int idAutorizzazione, int idRadioamatore)
        {
            var areValid = false;
            var autorizzazione = GetAutorizzazioneSRById(idAutorizzazione);
            var radioamatore = GetRadioamatoreSRById(idRadioamatore);
            if (autorizzazione != null && radioamatore != null)
            {
                areValid = autorizzazione.CodiceFiscale.EqualsIgnoreCaseAndTrim(radioamatore?.CodiceFiscale);
            }
            return areValid;
        }

        private AutorizzazioneSRDto GetAutorizzazioneSRById(int idAutorizzazione)
        {
            return _autorizzazione ??= _radioamatoreBl.GetAutorizzazioneSRByIdAsync(idAutorizzazione).GetAwaiter().GetResult();
        }

        private RadioamatoreSRDto GetRadioamatoreSRById(int idRadioamatore)
        {
            return _radioamatore ??= _radioamatoreBl.GetRadioamatoreSRByIdAsync(idRadioamatore).GetAwaiter().GetResult();
        }

        private bool IsAutorizzazioneAssociataNominativo(int idAutorizzazione)
        {
            var autorizzazione = GetAutorizzazioneSRById(idAutorizzazione);
            return autorizzazione?.IdRadioamatoreSR.HasValue ?? false;
        }
    }
}
