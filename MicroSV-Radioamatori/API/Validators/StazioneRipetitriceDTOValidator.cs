﻿using FluentValidation;
using Microsoft.Extensions.Configuration;
using MicroSV_Radioamatori.BL.DTOs;
using MicroSV_Radioamatori.BL.Enums;
using MicroSV_Radioamatori.BL.Interfaces;

namespace MicroSV_Radioamatori.API.Validators
{
    public class StazioneRipetitriceDTOValidator : AbstractValidator<StazioneRipetitriceDto>
    {
        private readonly IConfiguration _conf;
        protected readonly string timeZoneId;
        private readonly IRadiamatoreBl _radioamatoreBl;
        private readonly Operation _operation;
        private readonly RequestOrigin _origin;

        public StazioneRipetitriceDTOValidator(IConfiguration conf, IRadiamatoreBl radioamatoreBl, RequestOrigin requestOrigin, Operation operation)
        {
            this._conf = conf;
            this._radioamatoreBl = radioamatoreBl;
            this._operation = operation;
            this._origin = requestOrigin;
            Init();
        }

        private void Init()
        {
            //Stazione ripetitrice
            RuleFor(x => x.Indirizzo).NotEmpty().WithMessage("{PropertyName} deve essere valorizzato");
            RuleFor(x => x.Civico)
                .NotEmpty().WithMessage("{PropertyName} deve essere valorizzato")
                .MaximumLength(10).WithMessage("{PropertyName} deve essere lungo al massimo {MaxLength} caratteri");
            RuleFor(x => x.Cap).NotEmpty().WithMessage("{PropertyName} deve essere valorizzato");
            RuleFor(x => x.Comune).NotEmpty().WithMessage("{PropertyName} deve essere valorizzato");
            RuleFor(x => x.CodiceComune).NotEmpty().WithMessage("{PropertyName} deve essere valorizzato");
            RuleFor(x => x.Provincia).NotEmpty().WithMessage("{PropertyName} deve essere valorizzato");
            RuleFor(x => x.CodiceProvincia).NotEmpty().WithMessage("{PropertyName} deve essere valorizzato");
            RuleFor(x => x.Regione).NotEmpty().WithMessage("{PropertyName} deve essere valorizzato");
            RuleFor(x => x.CodiceRegione).NotEmpty().WithMessage("{PropertyName} deve essere valorizzato");
            RuleFor(x => x.IdIspettorato).NotEmpty().WithMessage("{PropertyName} deve essere valorizzato");
            RuleFor(x => x.Latitudine).NotEmpty().WithMessage("{PropertyName} deve essere valorizzato");
            RuleFor(x => x.Longitudine).NotEmpty().WithMessage("{PropertyName} deve essere valorizzato");

            //Operatore
            RuleFor(x => x.NomeOperatore)
                .NotEmpty().WithMessage("{PropertyName} deve essere valorizzato")
                .MaximumLength(50).WithMessage("{PropertyName} deve essere lungo al massimo {MaxLength} caratteri");
            RuleFor(x => x.CognomeOperatore)
                .NotEmpty().WithMessage("{PropertyName} deve essere valorizzato")
                .MaximumLength(50).WithMessage("{PropertyName} deve essere lungo al massimo {MaxLength} caratteri");
            RuleFor(x => x.NominativoOperatore).NotEmpty().WithMessage("{PropertyName} deve essere valorizzato");
            RuleFor(x => x.CodiceFiscaleOperatore)
                .NotEmpty().WithMessage("{PropertyName} deve essere valorizzato")
                .MaximumLength(16).WithMessage("{PropertyName} deve essere lungo al massimo {MaxLength} caratteri");
            RuleFor(x => x.RegioneResidenzaOperatore).NotEmpty().WithMessage("{PropertyName} deve essere valorizzato");
            RuleFor(x => x.ProvinciaResidenzaOperatore).NotEmpty().WithMessage("{PropertyName} deve essere valorizzato");
            RuleFor(x => x.ComuneResidenzaOperatore).NotNull().WithMessage("{PropertyName} deve essere valorizzato");
            RuleFor(x => x.IndirizzoResidenzaOperatore).NotEmpty().WithMessage("{PropertyName} deve essere valorizzato");
            RuleFor(x => x.NumeroCivicoResidenzaOperatore)
                .NotEmpty().WithMessage("{PropertyName} deve essere valorizzato")
                .MaximumLength(10).WithMessage("{PropertyName} deve essere lungo al massimo {MaxLength} caratteri");
            RuleFor(x => x.CapResidenzaOperatore).NotEmpty().WithMessage("{PropertyName} deve essere valorizzato");
            RuleFor(x => x.EmailOperatore).NotEmpty().WithMessage("{PropertyName} deve essere valorizzato");
            RuleFor(x => x.TelefonoOperatore)
                .NotEmpty().WithMessage("{PropertyName} deve essere valorizzato")
                .MaximumLength(50).WithMessage("{PropertyName} deve essere lungo al massimo {MaxLength} caratteri");

            if (_origin == RequestOrigin.FrontEnd)
            {
                RuleFor(x => x.CodiceRegioneResidenzaOperatore).NotEmpty().WithMessage("{PropertyName} deve essere valorizzato");
                RuleFor(x => x.CodiceProvinciaResidenzaOperatore).NotEmpty().WithMessage("{PropertyName} deve essere valorizzato");
                RuleFor(x => x.CodiceComuneResidenzaOperatore).NotEmpty().WithMessage("{PropertyName} deve essere valorizzato");
            }
        }
    }
}
