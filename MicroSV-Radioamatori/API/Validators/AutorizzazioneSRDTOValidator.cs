﻿using FluentValidation;
using Inv.Fwk.Helper.DateTimeZone;
using Invitalia.Misure.Standard2.Utilities.Extensions;
using Microsoft.Extensions.Configuration;
using MicroSV_Radioamatori.BL.DTOs;
using MicroSV_Radioamatori.BL.Enums;
using MicroSV_Radioamatori.BL.Interfaces;
using System;
using System.Linq;

namespace MicroSV_Radioamatori.API.Validators
{
    public class AutorizzazioneSRDTOValidator : AbstractValidator<AutorizzazioneSRDto>
    {
        private readonly IConfiguration _conf;
        protected readonly string timeZoneId;
        private readonly IRadiamatoreBl _radioamatoreBl;
        private readonly Operation _operation;
        private readonly RequestOrigin _origin;
        private RadioamatoreSRDto _radioamatoreSR;

        public AutorizzazioneSRDTOValidator(IConfiguration conf, IRadiamatoreBl radioamatoreBl, RequestOrigin requestOrigin, Operation operation)
        {
            this._conf = conf;
            this._radioamatoreBl = radioamatoreBl;
            this._operation = operation;
            this._origin = requestOrigin;
            timeZoneId = _conf["TimeZoneId"];

            Init();
        }

        private void Init()
        {
            RuleFor(x => x.IdTipologiaAssegnatario).NotEmpty().WithMessage("{PropertyName} deve essere valorizzato");

            When(x => x.IdTipologiaAssegnatario.HasValue, () =>
            {
                When(x => x.IdTipologiaAssegnatario == 7, () => AssegnatarioArticolo143Validator())
                .Otherwise(() => AssegnatarioPersonaFisicaValidator());
            });

            RuleFor(x => x.Utente).NotEmpty().WithMessage("{PropertyName} deve essere valorizzato");
            RuleFor(x => x.RegioneResidenza).NotEmpty().WithMessage("{PropertyName} deve essere valorizzato");
            RuleFor(x => x.ProvinciaResidenza).NotEmpty().WithMessage("{PropertyName} deve essere valorizzato");
            RuleFor(x => x.ComuneResidenza).NotEmpty().WithMessage("{PropertyName} deve essere valorizzato");

            if (_origin == RequestOrigin.FrontEnd)
            {
                RuleFor(x => x.RegioneResidenza).NotEmpty().WithMessage("{PropertyName} deve essere valorizzato");
                RuleFor(x => x.ProvinciaResidenza).NotEmpty().WithMessage("{PropertyName} deve essere valorizzato");
                RuleFor(x => x.ComuneResidenza).NotEmpty().WithMessage("{PropertyName} deve essere valorizzato");
            }

            RuleFor(x => x.IndirizzoResidenza).NotEmpty().WithMessage("{PropertyName} deve essere valorizzato");
            RuleFor(x => x.CapResidenza)
                .NotEmpty().WithMessage("{PropertyName} deve essere valorizzato")
                .MaximumLength(5).WithMessage("{PropertyName} deve essere lungo al massimo {MaxLength} caratteri");
            RuleFor(x => x.Email).NotEmpty().WithMessage("{PropertyName} deve essere valorizzato");
            RuleFor(x => x.Telefono).NotEmpty().WithMessage("{PropertyName} deve essere valorizzato");
            RuleFor(x => x.DataRilascio).NotEmpty().WithMessage("{PropertyName} deve essere valorizzato");

            RuleFor(x => x.DataScadenza).NotEmpty().WithMessage("{PropertyName} deve essere valorizzato");
            When(x => x.DataScadenza.HasValue, () =>
            {
                RuleFor(x => x.DataScadenza)
                    .Must(dataScadenza => dataScadenza > DateTime.UtcNow.ConvertTimeFromUtc(timeZoneId))
                        .WithMessage("La data scadenza deve essere una data futura")
                    .Must(dataScadenza => dataScadenza.Value.Year <= DateTime.UtcNow.AddYears(10).ConvertTimeFromUtc(timeZoneId).Year)
                        .WithMessage("La data scadenza deve essere una data futura")
                    .Must(dataScadenza =>
                        {
                            return dataScadenza.Value.Day == 31 && dataScadenza.Value.Month == 12;
                        })
                        .WithMessage("I valori di giorno e mese del campo {PropertyName} non è valido, la data scadenza deve essere il 31 dicembre");
            });
            IdAnagraficaIspettoratoValidator();

            RuleFor(x => x.StazioneRipetritrice)
                .NotEmpty().WithMessage("{PropertyName} deve essere valorizzato")
                .SetValidator(new StazioneRipetitriceDTOValidator(_conf, _radioamatoreBl, _origin, _operation));

            IdRadioamatoreSRValidator();

            When(x => !string.IsNullOrWhiteSpace(x.CodiceFiscale), () =>
            {
                RuleFor(x => x.CodiceFiscale)
                    .Must(cf => _radioamatoreBl.GetRadioamatoriByCodiceFiscaleAsync(cf).GetAwaiter().GetResult()?.Any(n => n.StatoNominativo.EqualsIgnoreCaseAndTrim("ATTIVO")) ?? false)
                        .WithMessage("Impossibile inserire una nuova autorizzazione generale per stazione ripetitrice: l’assegnatario risulta non essere in possesso di Nominativo di chiamata attivo")
                    .Must(cf => _radioamatoreBl.GetAutorizzazioneByFilterAsync(new AutorizzazioneFilterDto() { CodiceFiscale = cf }).GetAwaiter().GetResult()?.Any(a => a.IdStatoAutorizzazione == (int)EnumStatoAutorizzazione.Attiva || a.IdStatoAutorizzazione == (int)EnumStatoAutorizzazione.InScadenza) ?? false)
                        .WithMessage("Impossibile inserire una nuova autorizzazione generale per stazione ripetitrice: l’assegnatario risulta non essere in possesso di un’autorizzazione generale valida");
            });
        }

        private void IdRadioamatoreSRValidator()
        {
            RuleFor(x => x.IdRadioamatoreSR).NotEmpty().WithMessage("{PropertyName} deve essere valorizzato");
            When(x => x.IdRadioamatoreSR.HasValue, () =>
            {
                RuleFor(x => x.IdRadioamatoreSR)
                    .Must(id => GetRadioamatoreSRById(id.Value) != null)
                        .WithMessage("Attenzione! Il Nominativo selezionato non è presente sul db");

                When(x => GetRadioamatoreSRById(x.IdRadioamatoreSR.Value) != null, () =>
                {
                    RuleFor(x => x.IdRadioamatoreSR)
                        .Must((x, id) => HasCfCorrispondente(x.CodiceFiscale, id.Value))
                            .WithMessage("Attenzione! Il Nominativo inserito non risulta associato allo stesso assegnatario dell'autorizzazione")
                        .Must(id => HasNominativoAttivo(id.Value))
                            .WithMessage("Attenzione! il Nominativo inserito non risulta essere attivo")
                        .Must(id => HasAssociazioniValide(id.Value))
                            .WithMessage("Attenzione! il Nominativo inserito risulta associato ad un'altra autorizzazione");
                });
            });
        }

        private void IdAnagraficaIspettoratoValidator()
        {
            RuleFor(x => x.IdIspettorato).NotEmpty().WithMessage("{PropertyName} deve essere valorizzato");
            When(x => !string.IsNullOrWhiteSpace(x.IdIspettorato), () =>
            {
                RuleFor(x => x.IdIspettorato)
                        .Must(idAnagrafica =>
                        {
                            var ispettorati = _radioamatoreBl.GetAnagraficaIspettoratiAsync().GetAwaiter().GetResult();
                            return ispettorati.Any(i => i.IdIspettorato.EqualsIgnoreCaseAndTrim(idAnagrafica));
                        })
                        .WithMessage("Il valore {PropertyValue} del campo {PropertyName} non è un valore valido");

            });
        }

        private RadioamatoreSRDto GetRadioamatoreSRById(int idRadioamatoreSR)
        {
            return _radioamatoreSR ??= _radioamatoreBl.GetRadioamatoreSRByIdAsync(idRadioamatoreSR).GetAwaiter().GetResult();
        }

        private bool HasCfCorrispondente(string cfAssegnatario, int idRadioamatoreSR)
        {
            var radioamatoreSR = GetRadioamatoreSRById(idRadioamatoreSR);
            return !string.IsNullOrWhiteSpace(cfAssegnatario) && cfAssegnatario.EqualsIgnoreCaseAndTrim(radioamatoreSR?.CodiceFiscale);
        }

        private bool HasNominativoAttivo(int idRadioamatoreSR)
        {
            var radioamatoreSR = GetRadioamatoreSRById(idRadioamatoreSR);
            return "ATTIVO".EqualsIgnoreCaseAndTrim(radioamatoreSR?.StatoNominativo);
        }

        private bool HasAssociazioniValide(int idRadioamatoreSR)
        {
            var isValid = false;
            var radioamatoreSR = GetRadioamatoreSRById(idRadioamatoreSR);
            if (radioamatoreSR != null)
            {
                var autorizzazioni = _radioamatoreBl.GetAutorizzazioneSRByFilterAsync(new AutorizzazioneSRFilterDto()
                {
                    NominativoStazione = radioamatoreSR.CodiceNominativo
                }).GetAwaiter().GetResult();
                isValid = !autorizzazioni.Any(a => a.IdStatoAutorizzazione == (int)EnumStatoAutorizzazione.Attiva || a.IdStatoAutorizzazione == (int)EnumStatoAutorizzazione.InScadenza);
            }

            return isValid;
        }

        protected virtual void AssegnatarioPersonaFisicaValidator()
        {
            RuleFor(x => x.Nome).NotEmpty().WithMessage("{PropertyName} deve essere valorizzato");
            RuleFor(x => x.Cognome).NotEmpty().WithMessage("{PropertyName} deve essere valorizzato");
            RuleFor(x => x.CodiceFiscale)
                .NotEmpty().WithMessage("{PropertyName} deve essere valorizzato")
                .MaximumLength(16).WithMessage("{PropertyName} deve essere lungo al massimo {MaxLength} caratteri")
                .Must(cf => _radioamatoreBl.GetPatentiByCfAsync(cf).GetAwaiter().GetResult()?.Any(p => p.IdStatoPatente == (int)StatoPatente.Attiva) ?? false)
                        .WithMessage("Impossibile inserire una nuova autorizzazione generale per stazione ripetitrice: l’assegnatario risulta non essere in possesso una patente di radioamatore");
            RuleFor(x => x.Sesso).NotEmpty().WithMessage("{PropertyName} deve essere valorizzato");
            When(x => !string.IsNullOrWhiteSpace(x.Sesso), () =>
            {
                RuleFor(x => x.Sesso).Matches("^(M|F)$").WithMessage("{PropertyName} non valido, i valori accettati sono M o F");
            });
            RuleFor(x => x.LuogoDiNascita).NotEmpty().WithMessage("{PropertyName} deve essere valorizzato");
            RuleFor(x => x.DataDiNascita).NotEmpty().WithMessage("{PropertyName} deve essere valorizzato");
            When(x => x.DataDiNascita.HasValue, () =>
            {
                When(x => x.IdTipologiaAssegnatario.HasValue, () =>
                {
                    When(x => x.IdTipologiaAssegnatario == 2, () =>
                    {
                        RuleFor(x => x.DataDiNascita)
                            .Must(dataNascita =>
                            {
                                var sediciAnniFa = DateTime.UtcNow.AddYears(-16).ConvertTimeFromUtc(timeZoneId);
                                var diciottoAnniFa = DateTime.UtcNow.AddYears(-18).ConvertTimeFromUtc(timeZoneId);

                                return diciottoAnniFa.Date < dataNascita.Value.Date && dataNascita.Value.Date <= sediciAnniFa.Date;
                            })
                            .WithMessage("La data di nascita dell'assegnatario non è compresa tra 18 e 16 anni fa");
                    })
                    .Otherwise(() =>
                    {
                        RuleFor(x => x.DataDiNascita)
                            .Must(dataNascita =>
                            {
                                var diciottoAnniFa = DateTime.UtcNow.AddYears(-18).ConvertTimeFromUtc(timeZoneId);

                                return dataNascita.Value.Date <= diciottoAnniFa;
                            })
                            .WithMessage("L'assegnatario deve essere maggiorenne");
                    });
                });
            });
        }

        protected virtual void AssegnatarioArticolo143Validator()
        {
            RuleFor(x => x.Denominazione).NotEmpty().WithMessage("{PropertyName} deve essere valorizzato");
            RuleFor(x => x.CodiceFiscale)
                .NotEmpty().WithMessage("{PropertyName} deve essere valorizzato")
                .MaximumLength(11).WithMessage("{PropertyName} deve essere lungo al massimo {MaxLength} caratteri");
        }

    }
}
