﻿using FluentValidation;
using Invitalia.Misure.Standard2.Utilities.Extensions;
using Microsoft.Extensions.Configuration;
using MicroSV_Radioamatori.BL.DTOs;
using MicroSV_Radioamatori.BL.Enums;
using MicroSV_Radioamatori.BL.IncomingDTOs;
using MicroSV_Radioamatori.BL.Interfaces;
using System;
using System.Collections.Generic;
using System.Linq;

namespace MicroSV_Radioamatori.API.Validators
{
    public class CodiceFiscaleDataRilascioValidator : AbstractValidator<CodiceFiscaleDataRilascio>
    {
        protected readonly string timeZoneId;
        private readonly int _idRadioamatore;
        private readonly IConfiguration _conf;
        private readonly IRadiamatoreBl _radioamatoreBl;
        private readonly Operation _operation;
        private RadioamatoreDto _radioamatore;
        private RadioamatoreDto Radioamatore
        {
            get
            {
                return _radioamatore ?? _radioamatoreBl.GetRadioamatoreByIdAsync(_idRadioamatore).GetAwaiter().GetResult();
            }
        }

        private List<RadioamatoreDto> _nominativi;

        public CodiceFiscaleDataRilascioValidator(int idRadioamatore, IConfiguration conf, IRadiamatoreBl radioamatoreBl, Operation operation)
        {
            this._idRadioamatore = idRadioamatore;
            this._conf = conf;
            this._radioamatoreBl = radioamatoreBl;
            this._operation = operation;

            Init();
        }

        private void Init()
        {
            RuleFor(x => x)
                .NotNull().WithMessage("Richiesta non valorizzata");

            switch (_operation)
            {
                case Operation.UpdateDataRilascio:
                    UpdateDataRilascioValidator();
                    break;
                case Operation.UpdateCodiceFiscale:
                    UpdateCodiceFiscaleValidator();
                    break;
                default:
                    break;
            }
        }

        private void EsistenzaRadioamatoreValidator()
        {
            RuleFor(x => x)
                .Must(x => Radioamatore != null)
                .WithMessage($"Non esiste nessun record avente id radioamatore {_idRadioamatore}");
        }

        private void UpdateDataRilascioValidator()
        {
            EsistenzaRadioamatoreValidator();

            RuleFor(x => x.DataRilascio)
                .NotEmpty().WithMessage("{PropertyName} deve essere valorizzata");

            When(x => Radioamatore != null, () =>
            {
                RuleFor(x => x.DataRilascio)
                    .Must(x => string.IsNullOrWhiteSpace(Radioamatore.DataRilascio))
                    .WithMessage("Non è possibile inserire la data rilascio in quanto già valorizzata");
            });
        }

        private void UpdateCodiceFiscaleValidator()
        {
            EsistenzaRadioamatoreValidator();

            RuleFor(x => x.CodiceFiscale)
                .NotEmpty().WithMessage("{PropertyName} deve essere valorizzata");

            When(x => Radioamatore != null, () =>
            {
                RuleFor(x => x.CodiceFiscale)
                    .Must(x => string.IsNullOrWhiteSpace(Radioamatore.CodiceFiscale))
                    .WithMessage("Non è possibile inserire il codice fiscale in quanto già valorizzato");
            });

            When(x => !string.IsNullOrWhiteSpace(x.CodiceFiscale), () =>
            {
                RuleFor(x => x.CodiceFiscale)
                    .Must(cf => !ExistsNominativo(cf, EnumStatoNominativo.ATTIVO))
                        .WithMessage("Esiste già un Nominativo di chiamata Attivo per l’assegnatario indicato.")
                    .Must(cf => !ExistsNominativo(cf, EnumStatoNominativo.IN_ISTRUTTORIA))
                        .WithMessage("È già presente una richiesta di Nominativo di chiamata In Istruttoria per l’assegnatario indicato.")
                    .Must(cf => !ExistsNominativo(cf, EnumStatoNominativo.GENERATO))
                        .WithMessage("E' già presente una richiesta di Nominativo sul sistema di FE per l’assegnatario indicato.");
            });
        }

        private List<RadioamatoreDto> GetNominativiByCF(string codiceFiscale)
        {
            return _nominativi ??= _radioamatoreBl.GetRadioamatoriByCodiceFiscaleAsync(codiceFiscale, filtraStati: false).GetAwaiter().GetResult();
        }

        private bool ExistsNominativo(string codiceFiscale, EnumStatoNominativo statoNominativo)
        {
            var nominativi = GetNominativiByCF(codiceFiscale);
            return nominativi != null && nominativi.Any(n => n.StatoNominativo.EqualsIgnoreCaseAndTrim(statoNominativo.ToString()));
        }

    }
}
