﻿using FluentValidation;
using Inv.Fwk.Helper.DateTimeZone;
using Invitalia.Misure.Standard2.Utilities.Extensions;
using Microsoft.Extensions.Configuration;
using MicroSV_Radioamatori.BL.DTOs;
using MicroSV_Radioamatori.BL.Enums;
using MicroSV_Radioamatori.BL.Interfaces;
using System;
using System.Linq;
using System.Text.RegularExpressions;

namespace MicroSV_Radioamatori.API.Validators
{
    public class RadioamatoreSRDTOValidator : AbstractValidator<RadioamatoreSRDto>
    {
        private readonly IConfiguration _conf;
        protected readonly string timeZoneId;
        private readonly IRadiamatoreBl _radioamatoreBl;
        private readonly Operation _operation;
        private readonly RequestOrigin _origin;
        private const string REGEX_NUOVI_NOMINATIVI = @"IR\d{1}[a-zA-Z]{3}";

        public RadioamatoreSRDTOValidator(IConfiguration conf, IRadiamatoreBl radioamatoreBl, RequestOrigin requestOrigin, Operation operation)
        {
            this._conf = conf;
            this._radioamatoreBl = radioamatoreBl;
            this._operation = operation;
            this._origin = requestOrigin;
            timeZoneId = _conf["TimeZoneId"];
            Init();
        }

        private void Init()
        {
            switch (_operation)
            {
                case Operation.Insert:
                    ValidaCampiComuni();
                    When(x => x.IsAssegnatarioArt143.HasValue, () =>
                    {
                        When(x => x.IsAssegnatarioArt143.Value, () => AssegnatarioArticolo143Validator())
                        .Otherwise(() => AssegnatarioPersonaFisicaValidator());
                    });
                    break;
                case Operation.CambioStato:
                    IdRadioamatoreValidator();
                    EsistenzaRadioamatoreSRValidator();
                    break;
                default:
                    break;
            }

        }

        protected virtual void ValidaCampiComuni()
        {
            RuleFor(x => x.TipologiaAssegnatario).NotEmpty().WithMessage("{PropertyName} deve essere valorizzato");
            RuleFor(x => x.Utente).NotEmpty().WithMessage("{PropertyName} deve essere valorizzato");
            RuleFor(x => x.CodiceFiscale).NotEmpty().WithMessage("{PropertyName} deve essere valorizzato");
            RuleFor(x => x.Regione).NotEmpty().WithMessage("{PropertyName} deve essere valorizzato");
            RuleFor(x => x.Provincia).NotEmpty().WithMessage("{PropertyName} deve essere valorizzato");
            RuleFor(x => x.Comune).NotEmpty().WithMessage("{PropertyName} deve essere valorizzato");
            RuleFor(x => x.Indirizzo).NotEmpty().WithMessage("{PropertyName} deve essere valorizzato");
            RuleFor(x => x.Email).NotEmpty().WithMessage("{PropertyName} deve essere valorizzato");
            RuleFor(x => x.Telefono).NotEmpty().WithMessage("{PropertyName} deve essere valorizzato");

            CapValidator();
            IdAnagraficaIspettoratoValidator();
            DataRilascioValidator();
            DataScadenzaValidator();
            CodiceNominativoValidator();
            NominativoSRValidator();
            AutorizzazioneSRValidator();
            SuffissoNominativoSRValidator();
            CodiceRegioneValidator();
        }

        private void IdRadioamatoreValidator()
        {
            RuleFor(x => x.IdRadioamatore)
                .NotEmpty().WithMessage("{PropertyName} deve essere valorizzato");
        }

        private void EsistenzaRadioamatoreSRValidator()
        {
            When(x => x.IdRadioamatore != default, () =>
            {
                RuleFor(x => x.IdRadioamatore)
                    .Must(id => { return _radioamatoreBl.GetRadioamatoreSRByIdAsync(id).GetAwaiter().GetResult() != null; })
                    .WithMessage("Non esiste nessun radioamatore SR con {PropertyName} {PropertyValue}");
            });
        }

        private void CodiceNominativoValidator()
        {
            RuleFor(x => x.CodiceNominativo)
                .NotEmpty().WithMessage("{PropertyName} deve essere valorizzato")
                .MaximumLength(10).WithMessage("{PropertyName} deve essere lungo al massimo {MaxLength} caratteri");
        }

        private void DataScadenzaValidator()
        {
            RuleFor(x => x.DataScadenza).NotEmpty().WithMessage("{PropertyName} deve essere valorizzato");
            When(x => !string.IsNullOrWhiteSpace(x.DataScadenza), () =>
            {
                RuleFor(x => x.DataScadenza)
                        .Must(dataScadenza => DateTimeHelper.BeAValidDate(dataScadenza))
                        .WithMessage("Il valore {PropertyValue} del campo {PropertyName} non è una data valida");

                When(x => DateTimeHelper.BeAValidDate(x.DataScadenza), () =>
                {
                    RuleFor(x => x.DataScadenza)
                            .Must(dataScadenza =>
                            {
                                var dataScadenzaDateTime = DateTimeHelper.ParseDataInput(dataScadenza);
                                return dataScadenzaDateTime.Day == 31 && dataScadenzaDateTime.Month == 12;
                            })
                            .WithMessage("I valori di giorno e mese del campo {PropertyName} non è valido, la data scadenza deve essere il 31 dicembre");
                });
            });
        }

        private void DataRilascioValidator()
        {
            RuleFor(x => x.DataRilascio).NotEmpty().WithMessage("{PropertyName} deve essere valorizzato");
            When(x => !string.IsNullOrWhiteSpace(x.DataRilascio), () =>
            {
                RuleFor(x => x.DataRilascio)
                        .Must(dataRilascio => DateTimeHelper.BeAValidDate(dataRilascio))
                        .WithMessage("Il valore {PropertyValue} del campo {PropertyName} non è una data valida");
            });
        }

        private void CapValidator()
        {
            RuleFor(x => x.Cap)
                .NotEmpty().WithMessage("{PropertyName} deve essere valorizzato")
                .MaximumLength(5).WithMessage("{PropertyName} deve essere lungo al massimo {MaxLength} caratteri");
        }

        private void IdAnagraficaIspettoratoValidator()
        {
            RuleFor(x => x.IdAnagraficaIspettorato).NotEmpty().WithMessage("{PropertyName} deve essere valorizzato");
            When(x => !string.IsNullOrWhiteSpace(x.IdAnagraficaIspettorato), () =>
            {
                RuleFor(x => x.IdAnagraficaIspettorato)
                        .Must(idAnagrafica =>
                        {
                            var ispettorati = _radioamatoreBl.GetAnagraficaIspettoratiAsync().GetAwaiter().GetResult();
                            return ispettorati.Any(i => i.IdIspettorato.EqualsIgnoreCaseAndTrim(idAnagrafica));
                        })
                        .WithMessage("Il valore {PropertyValue} del campo {PropertyName} non è un valore valido");

            });
        }

        private void CodiceRegioneValidator()
        {
            When(x => !string.IsNullOrWhiteSpace(x.Regione) && !string.IsNullOrWhiteSpace(x.Provincia), () =>
            {
                RuleFor(x => x)
                        .Must(x =>
                        {
                            var codiceRegione = _radioamatoreBl.GetCodiceRegioneByRegione(x.Regione, x.Provincia).GetAwaiter().GetResult();
                            return !string.IsNullOrWhiteSpace(codiceRegione);
                        })
                        .WithMessage("Regione e provincia non corrispondono a un codice regione");

            });
        }

        protected virtual void AssegnatarioPersonaFisicaValidator()
        {
            RuleFor(x => x.Nome).NotEmpty().WithMessage("{PropertyName} deve essere valorizzato");
            RuleFor(x => x.Cognome).NotEmpty().WithMessage("{PropertyName} deve essere valorizzato");
            RuleFor(x => x.CodiceFiscale).MaximumLength(16).WithMessage("{PropertyName} deve essere lungo al massimo {MaxLength} caratteri");
            RuleFor(x => x.DataDiNascita).NotEmpty().WithMessage("{PropertyName} deve essere valorizzato");
            When(x => !string.IsNullOrWhiteSpace(x.DataDiNascita), () =>
            {
                RuleFor(x => x.DataDiNascita)
                    .Must(dataNascita => DateTimeHelper.BeAValidDate(dataNascita))
                    .WithMessage("Il valore {PropertyValue} del campo {PropertyName} non è una data valida");

                When(x => DateTimeHelper.BeAValidDate(x.DataDiNascita), () =>
                {
                    When(x => x.IsAssegnatarioMinorenne == true, () =>
                    {
                        RuleFor(x => x.DataDiNascita)
                            .Must(dataNascita =>
                            {
                                var dataNascitaTimeStamp = DateTimeHelper.ParseDataInput(dataNascita);
                                var sediciAnniFa = DateTime.UtcNow.AddYears(-16).ConvertTimeFromUtc(timeZoneId);
                                var diciottoAnniFa = DateTime.UtcNow.AddYears(-18).ConvertTimeFromUtc(timeZoneId);

                                return diciottoAnniFa.Date < dataNascitaTimeStamp.Date && dataNascitaTimeStamp.Date <= sediciAnniFa.Date;
                            })
                            .WithMessage("La data di nascita dell'assegnatario non è compresa tra 18 e 16 anni fa");
                    })
                    .Otherwise(() =>
                    {
                        RuleFor(x => x.DataDiNascita)
                        .Must(dataNascita =>
                        {
                            var dataNascitaTimeStamp = DateTimeHelper.ParseDataInput(dataNascita);
                            var diciottoAnniFa = DateTime.UtcNow.AddYears(-18).ConvertTimeFromUtc(timeZoneId);

                            return dataNascitaTimeStamp.Date <= diciottoAnniFa;
                        })
                        .WithMessage("L'assegnatario deve essere maggiorenne");
                    });
                });
            });
        }

        protected virtual void AssegnatarioArticolo143Validator()
        {
            RuleFor(x => x.Denominazione).NotEmpty().WithMessage("{PropertyName} deve essere valorizzato");
            RuleFor(x => x.CodiceFiscale).MaximumLength(11).WithMessage("{PropertyName} deve essere lungo al massimo {MaxLength} caratteri");
        }

        protected virtual void NominativoSRValidator()
        {
            When(x => !string.IsNullOrWhiteSpace(x.CodiceNominativo), () =>
            {
                RuleFor(x => x.CodiceNominativo)
                    .Must(nominativo =>
                    {
                        var nominativi = _radioamatoreBl.GetRadioamatoriAndAutorizzazioniSRByFilterAsync(new RadioamatoreSRFilterDto()
                        {
                            Nominativo = nominativo
                        }).GetAwaiter().GetResult();

                        return !nominativi.Any(n => n.StatoNominativo.EqualsIgnoreCaseAndTrim("ATTIVO") || n.StatoNominativo.EqualsIgnoreCaseAndTrim("IN_ISTRUTTORIA"));
                    })
                    .WithMessage("Attenzione! il Nominativo inserito risulta già presente a sistema");
            });
        }

        protected virtual void AutorizzazioneSRValidator()
        {
            When(x => !string.IsNullOrWhiteSpace(x.CodiceNominativo), () =>
            {
                RuleFor(x => x.CodiceNominativo)
                    .Must(nominativo =>
                    {
                        var autorizzazioni = _radioamatoreBl.GetAutorizzazioneSRByFilterAsync(new AutorizzazioneSRFilterDto()
                        {
                            NominativoStazione = nominativo
                        }).GetAwaiter().GetResult();

                        return !autorizzazioni.Any(n => n.IdStatoAutorizzazione == (int)EnumStatoAutorizzazione.Attiva ||
                                                        n.IdStatoAutorizzazione == (int)EnumStatoAutorizzazione.InIstruttoria ||
                                                        n.IdStatoAutorizzazione == (int)EnumStatoAutorizzazione.InScadenza ||
                                                        n.IdStatoAutorizzazione == (int)EnumStatoAutorizzazione.Rinnovo);
                    })
                    .WithMessage("Attenzione! il Nominativo inserito risulta associato ad un'altra autorizzazione per stazione ripetitrice");
            });
        }

        protected virtual void SuffissoNominativoSRValidator()
        {
            When(x => !string.IsNullOrWhiteSpace(x.CodiceNominativo) && Regex.IsMatch(x.CodiceNominativo, REGEX_NUOVI_NOMINATIVI), () =>
            {
                RuleFor(x => x.CodiceNominativo)
                    .Must(codiceNominativo =>
                    {
                        var suffissoSRDto = _radioamatoreBl.GetSuffissoByPrefissoAndSuffissoAsync(codiceNominativo.Substring(0, 3), codiceNominativo.Substring(3)).GetAwaiter().GetResult();
                        return suffissoSRDto != null && suffissoSRDto.Disponibile;
                    })
                    .WithMessage("Attenzione! il Nominativo inserito risulta già impegnato");
            });
        }
    }
}
