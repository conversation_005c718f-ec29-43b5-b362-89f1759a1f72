﻿using FluentValidation;
using Invitalia.Misure.Standard2.Utilities.Extensions;
using Microsoft.Extensions.Configuration;
using MicroSV_Radioamatori.BL.DTOs;
using MicroSV_Radioamatori.BL.Enums;
using MicroSV_Radioamatori.BL.Interfaces;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace MicroSV_Radioamatori.API.Validators
{
    public class PatenteDTOValidator : AbstractValidator<PatenteDto>
    {
        private readonly IConfiguration _conf;
        protected readonly string timeZoneId;
        private readonly IRadiamatoreBl _radioamatoreBl;
        private readonly Operation _operation;
        private readonly RequestOrigin _origin;
        private List<PatenteDto> patentiByCf;

        public PatenteDTOValidator(IConfiguration conf, IRadiamatoreBl radioamatoreBl, RequestOrigin requestOrigin, Operation operation)
        {
            this._conf = conf;
            this._radioamatoreBl = radioamatoreBl;
            this._operation = operation;
            this._origin = requestOrigin;
            Init();
        }

        private void Init()
        {
            RuleFor(x => x)
                .NotNull().WithMessage("Richiesta patente deve essere valorizzato");

            switch (_operation)
            {
                case Operation.Insert:
                    BaseDtoValidator();
                    AssenzaPatenteValidator();
                    AssenzaAltraPatenteAttivaByCfValidator();

                    if (_origin != RequestOrigin.GEA)
                    {
                        CodiceDomandaValidator();
                    }
                    else
                    {
                        AssenzaPatenteCartaceaValidator();
                    }

                    break;
                case Operation.Update:
                    BaseDtoValidator();
                    PresenzaPatenteValidator();
                    if (_origin != RequestOrigin.GEA)
                    {
                        CodiceDomandaValidator();
                    }

                    break;
                case Operation.CambioStato:
                    CodicePatenteValidator();
                    PresenzaPatenteValidator();
                    CambioStatoPatenteValidator();

                    break;
                case Operation.UploadDocumenti:
                    IdValidator();
                    PresenzaPatenteByIdValidator();
                    DocumentiValorizzatiValidator();
                    DocumentiValidator();
                    break;
                default:
                    break;
            }
        }

        protected virtual void BaseDtoValidator()
        {
            CognomeValidator();
            NomeValidator();
            CodiceFiscaleValidator();
            SessoValidator();
            LuogoNascitaValidator();
            DataNascitaValidator();
            RegioneValidator();
            ProvinciaValidator();
            ComuneValidator();
            CapValidator();
            IndirizzoValidator();
            IspettoratoValidator();
            PecValidator();
            EmailValidator();
            TelefonoValidator();
            UtenteValidator();
        }

        protected virtual void IdValidator()
        {
            RuleFor(x => x.Id)
                .NotEmpty().WithMessage($"{nameof(PatenteDto.Id)} deve essere valorizzato");
        }

        protected virtual void CognomeValidator()
        {
            RuleFor(x => x.Cognome)
                .NotEmpty().WithMessage($"{nameof(PatenteDto.Cognome)} deve essere valorizzato");
        }

        protected virtual void NomeValidator()
        {
            RuleFor(x => x.Nome)
                .NotEmpty().WithMessage($"{nameof(PatenteDto.Nome)} deve essere valorizzato");
        }

        protected virtual void CodiceFiscaleValidator()
        {
            RuleFor(x => x.CodiceFiscale)
                .NotNull().NotEmpty().WithMessage($"{nameof(PatenteDto.CodiceFiscale)} deve essere valorizzato");
        }

        protected virtual void SessoValidator()
        {
            RuleFor(x => x.Sesso)
                .NotEmpty().WithMessage($"{nameof(PatenteDto.Sesso)} deve essere valorizzato");
        }

        protected virtual void LuogoNascitaValidator()
        {
            RuleFor(x => x.LuogoDiNascita)
                .NotEmpty().WithMessage($"{nameof(PatenteDto.LuogoDiNascita)} deve essere valorizzato");

            RuleFor(x => x.CodiceLuogoDiNascita)
                .NotEmpty().WithMessage($"{nameof(PatenteDto.CodiceLuogoDiNascita)} deve essere valorizzato");
        }

        protected virtual void DataNascitaValidator()
        {
            RuleFor(x => x.DataDiNascita)
                .NotEmpty().WithMessage($"{nameof(PatenteDto.DataDiNascita)} deve essere valorizzato");
        }

        protected virtual void RegioneValidator()
        {
            RuleFor(x => x.RegioneResidenza)
                .NotEmpty().WithMessage($"{nameof(PatenteDto.RegioneResidenza)} deve essere valorizzato");

            RuleFor(x => x.CodiceRegioneResidenza)
               .NotEmpty().WithMessage($"{nameof(PatenteDto.CodiceRegioneResidenza)} deve essere valorizzato");
        }

        protected virtual void ProvinciaValidator()
        {
            RuleFor(x => x.ProvinciaResidenza)
                .NotEmpty().WithMessage($"{nameof(PatenteDto.ProvinciaResidenza)} deve essere valorizzato");

            RuleFor(x => x.CodiceProvinciaResidenza)
                .NotEmpty().WithMessage($"{nameof(PatenteDto.CodiceProvinciaResidenza)} deve essere valorizzato");
        }

        protected virtual void ComuneValidator()
        {
            RuleFor(x => x.ComuneResidenza)
                .NotEmpty().WithMessage($"{nameof(PatenteDto.ComuneResidenza)} deve essere valorizzato");

            RuleFor(x => x.CodiceComuneResidenza)
                .NotEmpty().WithMessage($"{nameof(PatenteDto.CodiceComuneResidenza)} deve essere valorizzato");
        }

        protected virtual void IndirizzoValidator()
        {
            RuleFor(x => x.IndirizzoResidenza)
                .NotEmpty().WithMessage($"{nameof(PatenteDto.IndirizzoResidenza)} deve essere valorizzato");
        }

        protected virtual void CapValidator()
        {
            RuleFor(x => x.CapResidenza)
                .NotEmpty().WithMessage($"{nameof(PatenteDto.CapResidenza)} deve essere valorizzato");
        }

        protected virtual void IspettoratoValidator()
        {
            RuleFor(x => x.IdIspettorato)
                .NotEmpty().WithMessage($"{nameof(PatenteDto.IdIspettorato)} deve essere valorizzato");
        }

        protected virtual void PecValidator()
        {
            RuleFor(x => x.Pec)
                .NotEmpty().WithMessage($"{nameof(PatenteDto.Pec)} deve essere valorizzato");
        }

        protected virtual void EmailValidator()
        {
            RuleFor(x => x.Email)
                .NotEmpty().WithMessage($"{nameof(PatenteDto.Email)} deve essere valorizzato");
        }

        protected virtual void TelefonoValidator()
        {
            RuleFor(x => x.Telefono)
                .NotEmpty().WithMessage($"{nameof(PatenteDto.Telefono)} deve essere valorizzato");
        }

        protected virtual void CodiceDomandaValidator()
        {
            RuleFor(x => x.CodiceDomanda)
                .NotEmpty().WithMessage($"{nameof(PatenteDto.CodiceDomanda)} deve essere valorizzato");
        }

        protected virtual void UtenteValidator()
        {
            RuleFor(x => x.Utente)
                .NotEmpty().WithMessage($"{nameof(PatenteDto.Utente)} deve essere valorizzato");
        }

        protected virtual void CodicePatenteValidator()
        {
            RuleFor(x => x.CodicePatente)
                .NotEmpty().WithMessage($"{nameof(PatenteDto.CodicePatente)} deve essere valorizzato");
        }

        protected virtual void StatoValidator()
        {
            RuleFor(x => x.IdStatoPatente)
                .NotEmpty().WithMessage($"{nameof(PatenteDto.IdStatoPatente)} deve essere valorizzato");
        }

        /// <summary>
        /// Se è presente il CodicePatente, verifica che esista già una patente con il codice comunicato
        /// </summary>
        protected virtual void PresenzaPatenteValidator()
        {
            When(x => !string.IsNullOrWhiteSpace(x.CodicePatente), () =>
            {
                RuleFor(x => x.CodicePatente)
                    .Must((codice) => _radioamatoreBl.ExistsPatenteByCodiceAsync(codice).GetAwaiter().GetResult())
                    .WithMessage(x => $"Non esiste nessuna patente con codice {x.CodicePatente}");
            });
        }

        protected virtual void PresenzaPatenteByIdValidator()
        {
            When(x => x.Id != default, () =>
            {
                RuleFor(x => x.Id)
                    .Must((id) => ExistsPatenteById(id))
                    .WithMessage(x => $"Non esiste nessuna patente con id {x.Id}");
            });
        }

        /// <summary>
        /// Se è presente il CodicePatente, verifica che non esista già una patente con il codice comunicato
        /// </summary>
        protected virtual void AssenzaPatenteValidator()
        {
            When(x => !string.IsNullOrWhiteSpace(x.CodicePatente), () =>
            {
                RuleFor(x => x.CodicePatente)
                    .Must((codice) => !_radioamatoreBl.ExistsPatenteByCodiceAsync(codice).GetAwaiter().GetResult())
                    .WithMessage(x => $"Esiste già una patente con codice {x.CodicePatente}");
            });
        }

        /// <summary>
        /// Se è presente il NumeroPatenteCartacea, verifica che non esista già una patente con il numero patente cartacea comunicato
        /// </summary>
        protected virtual void AssenzaPatenteCartaceaValidator()
        {
            When(x => !string.IsNullOrWhiteSpace(x.NumeroPatenteCartacea), () =>
            {
            RuleFor(x => x.NumeroPatenteCartacea)
                .Must((codice) =>
                {
                    var patentiCartacee = _radioamatoreBl.GetPatenteByNumeroCartaceaAsync(codice).GetAwaiter().GetResult();
                    return patentiCartacee == null 
                            || !patentiCartacee.Any(p => p.IdStatoPatente == (int)StatoPatente.RichiestaAccolta
                                                            || p.IdStatoPatente == (int)StatoPatente.RichiestaAmmissione
                                                            || p.IdStatoPatente == (int)StatoPatente.Attiva);
                })
                .WithMessage(x => $"Esiste già una patente con numero patente cartacea {x.NumeroPatenteCartacea}");
        });
        }

    /// <summary>
    /// Se sono presenti CodicePatente e CodiceFiscale, verifica che esista già una patente in stato attiva
    /// abbinata allo stesso CF ma con un codice diverso 
    /// </summary>
    protected virtual void AssenzaAltraPatenteAttivaByCfValidator()
    {
        When(x => !string.IsNullOrWhiteSpace(x.CodiceFiscale), () =>
        {
            RuleFor(x => x.CodiceFiscale)
                .Must((x, cf) => !(HasAltraPatenteByCfAndStatoAsync(x.CodicePatente, cf, (int)StatoPatente.Attiva).GetAwaiter().GetResult()))
                .WithMessage(x => "Il titolare patente risulta già essere in possesso di una patente di radioamatore")
                .Must((x, cf) => !(HasAltraPatenteByCfAndStatoAsync(x.CodicePatente, cf, (int)StatoPatente.RichiestaAmmissione).GetAwaiter().GetResult()))
                .WithMessage(x => "Il titolare patente ha già una richiesta di ammissione all'esame")
                .Must((x, cf) => !(HasAltraPatenteByCfAndStatoAsync(x.CodicePatente, cf, (int)StatoPatente.RichiestaAccolta).GetAwaiter().GetResult()))
                .WithMessage(x => "Il titolare patente ha già una richiesta di ammissione all'esame per la quale è in attesa dell'esito dell'esame");
        });
    }

    /// <summary>
    /// Verifica la legittimità del cambio stato patente in funzione dello stato attuale della patente
    /// </summary>
    protected virtual void CambioStatoPatenteValidator()
    {
        When(x => !string.IsNullOrWhiteSpace(x.CodicePatente), () =>
        {
            RuleFor(x => x)
                .Must((request) =>
                {
                    var cambioConsentito = false;
                    var patente = _radioamatoreBl.GetPatenteByCodiceAsync(request.CodicePatente).GetAwaiter().GetResult();

                    if (patente != null)
                    {
                        //lo stato patente nella request è lo stato TO BE,
                        //bisogna verificare se la patente recuperata da db è in uno stato tale da consentire il cambio con quello comunicato
                        switch (request.IdStatoPatente)
                        {
                            case (int)StatoPatente.RichiestaAccolta:
                            case (int)StatoPatente.RichiestaRigettata:
                                cambioConsentito = (patente.IdStatoPatente == (int)StatoPatente.RichiestaAmmissione);
                                break;
                            case (int)StatoPatente.EsameNonPassato:
                            case (int)StatoPatente.Attiva:
                                cambioConsentito = (patente.IdStatoPatente == (int)StatoPatente.RichiestaAccolta);
                                break;
                            case (int)StatoPatente.Cancellata:
                                cambioConsentito = (patente.IdStatoPatente == (int)StatoPatente.Attiva);
                                break;
                            default:
                                break;
                        }
                    }

                    return cambioConsentito;
                })
                .WithMessage(x => $"La patente con codice {x.CodicePatente} non può essere portata nello stato {((StatoPatente)x.IdStatoPatente).GetAttributeDescription()} in base al suo stato attuale.");
        });
    }

    protected virtual void DocumentiValidator()
    {
        When(dto => dto.Documenti != null && dto.Documenti.Any(), () =>
        {
            AddDocumentoDtoValidator();
        });
    }

    protected virtual void DocumentiValorizzatiValidator()
    {
        RuleFor(dto => dto.Documenti)
            .NotEmpty()
            .WithMessage($"{nameof(AutorizzazioneDto.Documenti)} deve essere valorizzato");
    }

    #region private methods

    private bool ExistsPatenteById(int idPatente)
    {
        return _radioamatoreBl.GetPatenteByIdAsync(idPatente).GetAwaiter().GetResult() != null;
    }

    private async Task<List<PatenteDto>> GetAltrePatentiByCfAsync(string codiceFiscale)
    {
        return patentiByCf ??= await _radioamatoreBl.GetPatentiByCfAsync(codiceFiscale);
    }

    private async Task<bool> HasAltraPatenteByCfAndStatoAsync(string codicePatente, string codiceFiscale, int idStatoPatente)
    {
        var patenti = await GetAltrePatentiByCfAsync(codiceFiscale);
        return patenti != null && patenti.Any(p => p.IdStatoPatente == idStatoPatente && !p.CodicePatente.EqualsIgnoreCaseAndTrim(codicePatente));
    }

    private void AddDocumentoDtoValidator()
    {
        var validatorsFactory = new GenericValidatorsFactory<DocumentoDto>(this._conf, this._radioamatoreBl, "RAD2");
        RuleForEach(x => x.Documenti).SetValidator((IValidator<DocumentoDto>)validatorsFactory.validator);
    }

    #endregion
}
}
