﻿using FluentValidation.Results;
using FluentValidation;
using System.Collections.Generic;
using System.Reflection;
using System;
using Microsoft.Extensions.Configuration;
using MicroSV_Radioamatori.BL.Interfaces;
using System.Linq;

namespace MicroSV_Radioamatori.API.Validators
{
    public class GenericValidatorsFactory<T>
    {
        internal IValidator validator;

        private readonly IConfiguration _conf;
        private readonly IRadiamatoreBl _radioamatoreBl;

        protected readonly string _codiceMisura;
        protected readonly Type _type;
        private List<ValidationResult> results;

        public GenericValidatorsFactory(IConfiguration conf, IRadiamatoreBl radioamatoreBl, string codiceMisura)
        {
            this._conf = conf;
            this._radioamatoreBl = radioamatoreBl;
            this._codiceMisura = codiceMisura;
            this._type = typeof(T);
            SetValidatorsByMisura();
        }

        private void SetValidatorsByMisura()
        {
            try
            {
                string validatorName = $"{_type.Name}Validator{this._codiceMisura}";
                this.validator = Instantiate<IValidator>(validatorName);
            }
            catch (Exception)
            {
                this.validator = Instantiate<IValidator>($"{_type.Name}Validator");
            }
        }

        private T2 Instantiate<T2>(string className)
        {
            Type typeImplemented = typeof(T2);

            Type selectedType = Assembly.GetExecutingAssembly() // i.e. our project/library
                .GetTypes()
                .First(t => typeImplemented.IsAssignableFrom(t) && t.Name.ToLower() == className.ToLower());

            return (T2)Activator.CreateInstance(selectedType, this._conf, this._radioamatoreBl, this._codiceMisura);
        }

        internal void Validate(T dto)
        {
            results = new List<ValidationResult>();

            ValidationContext<T> validationContext = new ValidationContext<T>(dto);
            ValidationResult validationResult = this.validator.Validate(validationContext);
            results.Add(validationResult);
        }

        public List<ValidationFailure> GetFailures()
        {
            List<ValidationFailure> failures = new List<ValidationFailure>();
            foreach (ValidationResult result in results)
            {
                if (!result.IsValid)
                    failures.AddRange(result.Errors);
            }

            return failures;
        }
    }
}
