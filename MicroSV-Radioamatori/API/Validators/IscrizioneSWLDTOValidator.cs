﻿using FluentValidation;
using Inv.Fwk.Helper.DateTimeZone;
using Microsoft.Extensions.Configuration;
using MicroSV_Radioamatori.BL.DTOs;
using MicroSV_Radioamatori.BL.Enums;
using MicroSV_Radioamatori.BL.Interfaces;

namespace MicroSV_Radioamatori.API.Validators
{
    public class IscrizioneSWLDTOValidator : AbstractValidator<IscrizioneSWLDto>
    {
        private readonly IConfiguration _conf;
        protected readonly string timeZoneId;
        private readonly IRadiamatoreBl _radioamatoreBl;
        private readonly Operation _operation;
        private readonly RequestOrigin _origin;

        public IscrizioneSWLDTOValidator(IConfiguration conf, IRadiamatoreBl radioamatoreBl, RequestOrigin requestOrigin, Operation operation)
        {
            this._conf = conf;
            this._radioamatoreBl = radioamatoreBl;
            this._operation = operation;
            this._origin = requestOrigin;
            Init();
        }

        private void Init()
        {
            RuleFor(x => x)
                .NotNull().WithMessage("Richiesta patente deve essere valorizzato");

            switch (_operation)
            {
                case Operation.Insert:
                    BaseDtoValidator();

                    if (_origin == RequestOrigin.FrontEnd)
                    {
                        CodiceDomandaValidator();
                    }

                    break;
                case Operation.CambioStato:
                    IdValidator();
                    PresenzaIscrizioneByIdValidator();
                    break;
                default:
                    break;
            }
        }

        protected virtual void BaseDtoValidator()
        {
            CognomeValidator();
            NomeValidator();
            CodiceFiscaleValidator();
            SessoValidator();
            LuogoNascitaValidator();
            DataNascitaValidator();
            RegioneValidator();
            ProvinciaValidator();
            ComuneValidator();
            IndirizzoValidator();
            CapValidator();
            PecValidator();
            EmailValidator();
            TelefonoValidator();
            TipologiaRichiedenteValidator();
            UtenteValidator();
        }

        protected virtual void IdValidator()
        {
            RuleFor(x => x.Id)
                .NotEmpty().WithMessage("{PropertyName} deve essere valorizzato");
        }

        protected virtual void CognomeValidator()
        {
            RuleFor(x => x.Cognome)
                .NotEmpty().WithMessage("{PropertyName} deve essere valorizzato");
        }

        protected virtual void NomeValidator()
        {
            RuleFor(x => x.Nome)
                .NotEmpty().WithMessage("{PropertyName} deve essere valorizzato");
        }

        protected virtual void CodiceFiscaleValidator()
        {
            RuleFor(x => x.CodiceFiscale)
                .NotEmpty().WithMessage("{PropertyName} deve essere valorizzato");
        }

        protected virtual void SessoValidator()
        {
            RuleFor(x => x.Sesso)
                .NotEmpty().WithMessage("{PropertyName} deve essere valorizzato");
        }

        protected virtual void LuogoNascitaValidator()
        {
            RuleFor(x => x.LuogoNascita)
                .NotEmpty().WithMessage("{PropertyName} deve essere valorizzato");

            RuleFor(x => x.CodiceLuogoNascita)
                .NotEmpty().WithMessage("{PropertyName} deve essere valorizzato");
        }

        protected virtual void DataNascitaValidator()
        {
            RuleFor(x => x.DataNascita)
                .NotEmpty().WithMessage("{PropertyName} deve essere valorizzato")
                .Must(x => DateTimeHelper.BeAValidDate(x)).WithMessage("{PropertyName} non è una data valida");
        }

        protected virtual void RegioneValidator()
        {
            RuleFor(x => x.RegioneResidenza)
                .NotEmpty().WithMessage("{PropertyName} deve essere valorizzato");

            RuleFor(x => x.CodiceRegioneResidenza)
               .NotEmpty().WithMessage("{PropertyName} deve essere valorizzato");
        }

        protected virtual void ProvinciaValidator()
        {
            RuleFor(x => x.ProvinciaResidenza)
                .NotEmpty().WithMessage("{PropertyName} deve essere valorizzato");

            RuleFor(x => x.CodiceProvinciaResidenza)
                .NotEmpty().WithMessage("{PropertyName} deve essere valorizzato");
        }

        protected virtual void ComuneValidator()
        {
            RuleFor(x => x.ComuneResidenza)
                .NotEmpty().WithMessage("{PropertyName} deve essere valorizzato");

            RuleFor(x => x.CodiceComuneResidenza)
                .NotEmpty().WithMessage("{PropertyName} deve essere valorizzato");
        }

        protected virtual void IndirizzoValidator()
        {
            RuleFor(x => x.IndirizzoResidenza)
                .NotEmpty().WithMessage("{PropertyName} deve essere valorizzato");
        }

        protected virtual void CapValidator()
        {
            RuleFor(x => x.CapResidenza)
                .NotEmpty().WithMessage("{PropertyName} deve essere valorizzato");
        }

        protected virtual void IspettoratoValidator()
        {
            RuleFor(x => x.IdAnagraficaIspettorato)
                .NotEmpty().WithMessage("{PropertyName} deve essere valorizzato");
        }

        protected virtual void PecValidator()
        {
            RuleFor(x => x.Pec)
                .NotEmpty().WithMessage("{PropertyName} deve essere valorizzato");
        }

        protected virtual void EmailValidator()
        {
            RuleFor(x => x.Email)
                .NotEmpty().WithMessage("{PropertyName} deve essere valorizzato");
        }

        protected virtual void TelefonoValidator()
        {
            RuleFor(x => x.Telefono)
                .NotEmpty().WithMessage("{PropertyName} deve essere valorizzato");
        }

        protected virtual void UtenteValidator()
        {
            RuleFor(x => x.Utente)
                .NotEmpty().WithMessage("{PropertyName} deve essere valorizzato");
        }

        protected virtual void TipologiaRichiedenteValidator()
        {
            RuleFor(x => x.TipologiaRichiedente)
                .NotEmpty().WithMessage("{PropertyName} deve essere valorizzato");
        }

        protected virtual void CodiceDomandaValidator()
        {
            RuleFor(x => x.CodiceDomanda)
                .NotEmpty().WithMessage("{PropertyName} deve essere valorizzato");
        }

        protected virtual void PresenzaIscrizioneByIdValidator()
        {
            When(x => x.Id.HasValue, () =>
            {
                RuleFor(x => x.Id)
                    .Must(id => (_radioamatoreBl.GetIscrizioneSWLByIdAsync(id.Value).GetAwaiter().GetResult() != null))
                    .WithMessage("Non esiste nessuna iscrizione all'elenco SWL con id {PropertyValue}");
            });
        }
    }
}
