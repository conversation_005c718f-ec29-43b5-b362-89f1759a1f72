﻿using FluentValidation;
using Invitalia.Misure.Standard2.Utilities.Extensions;
using Microsoft.Extensions.Configuration;
using MicroSV_Radioamatori.BL.DTOs;
using MicroSV_Radioamatori.BL.Enums;
using MicroSV_Radioamatori.BL.Interfaces;
using System;
using System.Collections.Generic;
using System.Linq;

namespace MicroSV_Radioamatori.API.Validators
{
    public class AutorizzazioneDTOValidator : AbstractValidator<AutorizzazioneDto>
    {
        protected readonly string timeZoneId;
        private readonly IConfiguration _conf;
        private readonly IRadiamatoreBl _radioamatoreBl;
        private readonly RequestOrigin _requestOrigin;
        private readonly Operation _operation;

        private short _idStatoAutorizzazioneEsistente;
        private AutorizzazioneDto _autorizzazione;
        private RadioamatoreDto _radioamatore;

        public AutorizzazioneDTOValidator(IConfiguration conf, IRadiamatoreBl radioamatoreBl, RequestOrigin requestOrigin, Operation operation)
        {
            this._conf = conf;
            this._radioamatoreBl = radioamatoreBl;
            this._requestOrigin = requestOrigin;
            this._operation = operation;

            Init();
        }

        private void Init()
        {
            RuleFor(x => x)
                .NotNull().WithMessage("Richiesta patente deve essere valorizzato");

            switch (_operation)
            {
                case Operation.Insert:
                case Operation.Update:
                    BaseDtoValidator();

                    if (_requestOrigin == RequestOrigin.FrontEnd)
                    {
                        CodiceDomandaValidator();
                    }
                    else
                    {
                        CodiceFiscaleBackEndValidator();
                        DocumentiValidator();
                    }

                    break;
                case Operation.UpdateVisibilitaAttestato:
                    IdValidator();
                    IsDocumentoAutorizzazioneVisibileFeValidator();
                    PresenzaAutorizzazioneByIdValidator();
                    break;
                case Operation.CambioStato:
                    IdValidator();
                    PresenzaAutorizzazioneByIdValidator();
                    CambioStatoAutorizzazioneValidator();
                    break;
                case Operation.UpdateNominativoAssegnatario:
                    IdValidator();
                    PresenzaAutorizzazioneByIdValidator();
                    IdRadioamatoreValidator();
                    UpdateNominativoValidator();
                    break;
                case Operation.UploadDocumenti:
                    IdValidator();
                    PresenzaAutorizzazioneByIdValidator();
                    DocumentiValorizzatiValidator();
                    DocumentiValidator();
                    break;
                default:
                    break;
            }
        }

        private void BaseDtoValidator()
        {
            When(a => a.IdTipologiaAssegnatario.HasValue && !IsSoggettoArticolo144(a.IdTipologiaAssegnatario.Value), () =>
            {
                NomeValidator();
                CognomeValidator();
                SessoValidator();
                LuogoNascitaValidator();
                DataNascitaValidator();
            })
            .Otherwise(() =>
            {
                DenominazioneValidator();
            });

            CodiceFiscaleValidator();
            RegioneValidator();
            ProvinciaValidator();
            ComuneValidator();
            CapValidator();
            IndirizzoValidator();
            IspettoratoValidator();
            PecValidator();
            EmailValidator();
            TelefonoValidator();
            UtenteValidator();
            TipologiaAssegnatarioValidator();
            TipologiaAutorizzazioneValidator();
            CodiceNominativoValidator();
            IsRinnovoValidator();
            IsDocumentoAutorizzazioneVisibileFeValidator();
        }

        protected virtual void IdValidator()
        {
            RuleFor(x => x.Id)
                .NotEmpty().WithMessage($"{nameof(AutorizzazioneDto.Id)} deve essere valorizzato");
        }

        protected virtual void CognomeValidator()
        {
            RuleFor(x => x.Cognome)
                .NotEmpty().WithMessage($"{nameof(AutorizzazioneDto.Cognome)} deve essere valorizzato");
        }

        protected virtual void NomeValidator()
        {
            RuleFor(x => x.Nome)
                .NotEmpty().WithMessage($"{nameof(AutorizzazioneDto.Nome)} deve essere valorizzato");
        }

        protected virtual void DenominazioneValidator()
        {
            RuleFor(x => x.Denominazione)
                .NotEmpty().WithMessage($"{nameof(AutorizzazioneDto.Denominazione)} deve essere valorizzato");
        }

        protected virtual void CodiceFiscaleValidator()
        {
            RuleFor(x => x.CodiceFiscale)
                .NotNull().NotEmpty().WithMessage($"{nameof(AutorizzazioneDto.CodiceFiscale)} deve essere valorizzato");
        }

        protected virtual void CodiceFiscaleBackEndValidator()
        {
            When(a => !string.IsNullOrWhiteSpace(a.CodiceFiscale), () =>
            {
                RuleFor(a => a.CodiceFiscale)
                    .Must((cfAssegnatario) => !ExistsAutorizzazioneByCfAndStati(cfAssegnatario, new List<short>() { (short)EnumStatoAutorizzazione.Attiva,
                                                                                                                                (short)EnumStatoAutorizzazione.InIstruttoria,
                                                                                                                                (short)EnumStatoAutorizzazione.InScadenza,
                                                                                                                                (short)EnumStatoAutorizzazione.Rinnovo }))
                    .WithMessage((a) => GetErrorMessageByIdStato());
            });
        }

        protected virtual void SessoValidator()
        {
            RuleFor(x => x.Sesso)
                .NotEmpty().WithMessage($"{nameof(AutorizzazioneDto.Sesso)} deve essere valorizzato");
        }

        protected virtual void LuogoNascitaValidator()
        {
            RuleFor(x => x.LuogoDiNascita)
                .NotEmpty().WithMessage($"{nameof(AutorizzazioneDto.LuogoDiNascita)} deve essere valorizzato");

            RuleFor(x => x.CodiceLuogoDiNascita)
                .NotEmpty().WithMessage($"{nameof(AutorizzazioneDto.CodiceLuogoDiNascita)} deve essere valorizzato");
        }

        protected virtual void DataNascitaValidator()
        {
            RuleFor(x => x.DataDiNascita)
                .NotEmpty().WithMessage($"{nameof(AutorizzazioneDto.DataDiNascita)} deve essere valorizzato");
        }

        protected virtual void RegioneValidator()
        {
            RuleFor(x => x.RegioneResidenza)
                .NotEmpty().WithMessage($"{nameof(AutorizzazioneDto.RegioneResidenza)} deve essere valorizzato");

            RuleFor(x => x.CodiceRegioneResidenza)
               .NotEmpty().WithMessage($"{nameof(AutorizzazioneDto.CodiceRegioneResidenza)} deve essere valorizzato");
        }

        protected virtual void ProvinciaValidator()
        {
            RuleFor(x => x.ProvinciaResidenza)
                .NotEmpty().WithMessage($"{nameof(AutorizzazioneDto.ProvinciaResidenza)} deve essere valorizzato");

            RuleFor(x => x.CodiceProvinciaResidenza)
                .NotEmpty().WithMessage($"{nameof(AutorizzazioneDto.CodiceProvinciaResidenza)} deve essere valorizzato");
        }

        protected virtual void ComuneValidator()
        {
            RuleFor(x => x.ComuneResidenza)
                .NotEmpty().WithMessage($"{nameof(AutorizzazioneDto.ComuneResidenza)} deve essere valorizzato");

            RuleFor(x => x.CodiceComuneResidenza)
                .NotEmpty().WithMessage($"{nameof(AutorizzazioneDto.CodiceComuneResidenza)} deve essere valorizzato");
        }

        protected virtual void IndirizzoValidator()
        {
            RuleFor(x => x.IndirizzoResidenza)
                .NotEmpty().WithMessage($"{nameof(AutorizzazioneDto.IndirizzoResidenza)} deve essere valorizzato");
        }

        protected virtual void CapValidator()
        {
            RuleFor(x => x.CapResidenza)
                .NotEmpty().WithMessage($"{nameof(AutorizzazioneDto.CapResidenza)} deve essere valorizzato");
        }

        protected virtual void IspettoratoValidator()
        {
            RuleFor(x => x.IdIspettorato)
                .NotEmpty().WithMessage($"{nameof(AutorizzazioneDto.IdIspettorato)} deve essere valorizzato");
        }

        protected virtual void PecValidator()
        {
            RuleFor(x => x.Pec)
                .NotEmpty().WithMessage($"{nameof(AutorizzazioneDto.Pec)} deve essere valorizzato");
        }

        protected virtual void EmailValidator()
        {
            RuleFor(x => x.Email)
                .NotEmpty().WithMessage($"{nameof(AutorizzazioneDto.Email)} deve essere valorizzato");
        }

        protected virtual void TelefonoValidator()
        {
            RuleFor(x => x.Telefono)
                .NotEmpty().WithMessage($"{nameof(AutorizzazioneDto.Telefono)} deve essere valorizzato");
        }

        protected virtual void CodiceDomandaValidator()
        {
            RuleFor(x => x.CodiceDomanda)
                .NotEmpty().WithMessage($"{nameof(AutorizzazioneDto.CodiceDomanda)} deve essere valorizzato");
        }

        protected virtual void UtenteValidator()
        {
            RuleFor(x => x.Utente)
                .NotEmpty().WithMessage($"{nameof(AutorizzazioneDto.Utente)} deve essere valorizzato");
        }

        protected virtual void TipologiaAssegnatarioValidator()
        {
            RuleFor(x => x.IdTipologiaAssegnatario)
                .NotEmpty().WithMessage($"{nameof(AutorizzazioneDto.IdTipologiaAssegnatario)} deve essere valorizzato");
        }

        protected virtual void TipologiaAutorizzazioneValidator()
        {
            RuleFor(x => x.IdTipologiaAutorizzazione)
                .NotEmpty().WithMessage($"{nameof(AutorizzazioneDto.IdTipologiaAutorizzazione)} deve essere valorizzato");
        }

        protected virtual void CodiceNominativoValidator()
        {
            RuleFor(x => x.CodiceNominativo)
                .NotEmpty().WithMessage($"{nameof(AutorizzazioneDto.CodiceNominativo)} deve essere valorizzato");
        }

        protected virtual void IsRinnovoValidator()
        {
            RuleFor(x => x.IsRinnovo)
                .NotEmpty().WithMessage($"{nameof(AutorizzazioneDto.IsRinnovo)} deve essere valorizzato");
        }

        protected virtual void IsDocumentoAutorizzazioneVisibileFeValidator()
        {
            RuleFor(a => a.IsDocumentoAutorizzazioneVisibileFe)
                .NotEmpty().WithMessage($"{nameof(AutorizzazioneDto.IsDocumentoAutorizzazioneVisibileFe)} deve essere valorizzato");
        }

        protected virtual void PresenzaAutorizzazioneByIdValidator()
        {
            When(x => x.Id != default, () =>
            {
                RuleFor(x => x.Id)
                    .Must((id) => ExistsAutorizzazioneById(id))
                    .WithMessage(x => $"Non esiste nessuna autorizzazione con id {x.Id}");
            });
        }

        protected virtual void CambioStatoAutorizzazioneValidator()
        {
            When(x => x.Id != default, () =>
            {
                RuleFor(x => x)
                    .Must((request) =>
                    {
                        var cambioConsentito = false;
                        var autorizzazione = GetAutorizzazioneById(request.Id);

                        if (autorizzazione != null)
                        {
                            //lo stato autorizzazione nella request è lo stato TO BE,
                            //bisogna verificare se l'autorizzazione recuperata da db è in uno stato tale da consentire il cambio con quello comunicato
                            switch (request.IdStatoAutorizzazione)
                            {
                                case (int)EnumStatoAutorizzazione.Attiva:
                                case (int)EnumStatoAutorizzazione.Cancellata:
                                case (int)EnumStatoAutorizzazione.Rinunciata:
                                    cambioConsentito = true;
                                    break;
                                default:
                                    break;
                            }
                        }

                        return cambioConsentito;
                    })
                    .WithMessage(x => $"L'autorizzazione con id {x.Id} non può essere portata nello stato {((EnumStatoAutorizzazione)x.IdStatoAutorizzazione).GetAttributeDescription()} in base al suo stato attuale.");
            });
        }

        protected virtual void DocumentiValidator()
        {
            When(dto => dto.Documenti != null && dto.Documenti.Any(), () =>
            {
                AddDocumentoDtoValidator();

                //verifico presenza di uno e un solo documento di tipo AUTORIZZAZIONE_GENERALE
                RuleFor(dto => dto.Documenti)
                    .Must(documenti => documenti.Any(doc => doc.TipoDocumento.EqualsIgnoreCaseAndTrim(TipoDocumentoAutorizzazione.AUTORIZZAZIONE_GENERALE.ToString())))
                    .WithMessage($"Deve essere comunicato un documento di tipo {TipoDocumentoAutorizzazione.AUTORIZZAZIONE_GENERALE}");

                RuleFor(dto => dto.Documenti)
                    .Must(documenti => documenti.Where(doc => doc.TipoDocumento.EqualsIgnoreCaseAndTrim(TipoDocumentoAutorizzazione.AUTORIZZAZIONE_GENERALE.ToString())).Count() <= 1)
                    .WithMessage($"Non può essere comunicato più di un documento di tipo {TipoDocumentoAutorizzazione.AUTORIZZAZIONE_GENERALE}");
            });
        }

        protected virtual void DocumentiValorizzatiValidator()
        {
            RuleFor(dto => dto.Documenti)
                .NotEmpty()
                .WithMessage($"{nameof(AutorizzazioneDto.Documenti)} deve essere valorizzato");
        }

        protected virtual void IdRadioamatoreValidator()
        {
            RuleFor(x => x.IdRadioamatore)
                .NotNull()
                .WithMessage($"{nameof(AutorizzazioneDto.IdRadioamatore)} deve essere valorizzato");
        }

        protected virtual void UpdateNominativoValidator()
        {
            When(x => x.IdRadioamatore.HasValue, () =>
            {
                RuleFor(x => x.IdRadioamatore.Value)
                    .Must((id) => ExistsRadioamatoreById(id))
                    .WithMessage("Non esiste un nominativo con id {PropertyValue}")
                    .Must((id) => IsNominativoStandardEAttivoById(id))
                    .WithMessage("Il nominativo avente id {PropertyValue} non è di tipo STANDARD o non è in stato ATTIVO");

                RuleFor(x => x)
                    .Must((request) => IsCodiceFiscaleCorrispondente(request.Id, request.IdRadioamatore.Value))
                    .WithMessage("Il codice fiscale dell'assegnatario dell'autorizzazione non corrisponde al codice fiscale del radioamatore");

                RuleFor(x => x)
                    .Must((request) => !ExistsAutorizzazioneAttivaByIdRadioamatore(request.Id, request.IdRadioamatore.Value))
                    .WithMessage("Esiste già un'autorizzazione in stato attiva abbinata al nominativo del radioamatore");
            });
        }

        private void AddDocumentoDtoValidator()
        {
            var validatorsFactory = new GenericValidatorsFactory<DocumentoDto>(this._conf, this._radioamatoreBl, "RAD3");
            RuleForEach(x => x.Documenti).SetValidator((IValidator<DocumentoDto>)validatorsFactory.validator);
        }

        private bool ExistsAutorizzazioneByCfAndStati(string cfAssegnatario, List<short> statiAutorizzazione)
        {
            var hasAutorizzazione = false;

            var filter = new AutorizzazioneFilterDto()
            {
                CodiceFiscale = cfAssegnatario,
            };
            var autorizzazioni = _radioamatoreBl.GetAutorizzazioneByFilterAsync(filter).GetAwaiter().GetResult();

            foreach (var idStato in statiAutorizzazione)
            {
                if (autorizzazioni.Any(a => a.IdStatoAutorizzazione == idStato))
                {
                    _idStatoAutorizzazioneEsistente = idStato;
                    hasAutorizzazione = true;
                    break;
                }
            }

            return hasAutorizzazione;
        }

        private string GetErrorMessageByIdStato()
        {
            return (_idStatoAutorizzazioneEsistente) switch
            {
                (short)EnumStatoAutorizzazione.Attiva => "Impossibile inserire una nuova autorizzazione generale: l’assegnatario risulta già essere in possesso di autorizzazione generale",
                (short)EnumStatoAutorizzazione.InIstruttoria => "Impossibile inserire una nuova autorizzazione generale: è già presente una richiesta di autorizzazione generale in istruttoria",
                (short)EnumStatoAutorizzazione.InScadenza => "Impossibile inserire una nuova autorizzazione generale: è già presente un’autorizzazione generale in scadenza",
                (short)EnumStatoAutorizzazione.Rinnovo => "Impossibile inserire una nuova autorizzazione generale: è già presente una richiesta di rinnovo dell'autorizzazione generale in istruttoria",
                _ => throw new NotImplementedException()
            };
        }

        private bool IsCodiceNominativoUnivoco(string codiceNominativo)
        {
            var filter = new AutorizzazioneFilterDto()
            {
                Nominativo = codiceNominativo,
            };
            var autorizzazioni = _radioamatoreBl.GetAutorizzazioneByFilterAsync(filter).GetAwaiter().GetResult();

            return autorizzazioni == null || !autorizzazioni.Any(a => a.IdStatoAutorizzazione == (short)EnumStatoAutorizzazione.Attiva || a.IdStatoAutorizzazione == (short)EnumStatoAutorizzazione.InScadenza);
        }

        private bool IsSoggettoArticolo144(short idTipologiaAssegnatario)
        {
            return idTipologiaAssegnatario == (short)TipologiaAssegnatarioAutorizzazione.UniversitaEnti
                    || idTipologiaAssegnatario == (short)TipologiaAssegnatarioAutorizzazione.Scuole
                    || idTipologiaAssegnatario == (short)TipologiaAssegnatarioAutorizzazione.ScuoleMilitari
                    || idTipologiaAssegnatario == (short)TipologiaAssegnatarioAutorizzazione.AssociazioniArticolo144;
        }

        private RadioamatoreDto GetRadioamatoreById(int idRadioamatore)
        {
            return _radioamatore ??= _radioamatoreBl.GetRadioamatoreByIdAsync(idRadioamatore).GetAwaiter().GetResult();
        }

        private bool ExistsRadioamatoreById(int idRadioamatore)
        {
            return GetRadioamatoreById(idRadioamatore) != null;
        }

        private bool IsNominativoStandardEAttivoById(int idRadioamatore)
        {
            var radioamatore = GetRadioamatoreById(idRadioamatore);
            return radioamatore != null && radioamatore.TipoNominativo.EqualsIgnoreCaseAndTrim("STANDARD")  && radioamatore.StatoNominativo.EqualsIgnoreCaseAndTrim("ATTIVO");
        }

        private AutorizzazioneDto GetAutorizzazioneById(int idAutorizzazione)
        {
            return _autorizzazione ??= _radioamatoreBl.GetAutorizzazioneByIdAsync(idAutorizzazione).GetAwaiter().GetResult();
        }

        private bool ExistsAutorizzazioneById(int idAutorizzazione)
        {
            return GetAutorizzazioneById(idAutorizzazione) != null;
        }

        private bool IsCodiceFiscaleCorrispondente(int idAutorizzazione, int idRadioamatore)
        {
            var radioamatore = GetRadioamatoreById(idRadioamatore);
            var autorizzazione = GetAutorizzazioneById(idAutorizzazione);

            return radioamatore != null && autorizzazione != null && radioamatore.CodiceFiscale.EqualsIgnoreCaseAndTrim(autorizzazione.CodiceFiscale);
        }

        private bool ExistsAutorizzazioneAttivaByIdRadioamatore(int idAutorizzazione, int idRadioamatore)
        {

            var filter = new AutorizzazioneFilterDto()
            {
                IdRadioamatore = idRadioamatore
            };

            var autorizzazioni = _radioamatoreBl.GetAutorizzazioneByFilterAsync(filter).GetAwaiter().GetResult();

            return autorizzazioni != null && autorizzazioni.Any(a => a.Id != idAutorizzazione
                                                                        && a.IdStatoAutorizzazione == (short)EnumStatoAutorizzazione.Attiva);
        }

    }
}
