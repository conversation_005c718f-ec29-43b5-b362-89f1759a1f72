﻿using FluentValidation;
using Microsoft.Extensions.Configuration;
using MicroSV_Radioamatori.BL.DTOs;
using MicroSV_Radioamatori.BL.Interfaces;
using Inv.Fwk.Helper.DateTimeZone;

namespace MicroSV_Radioamatori.API.Validators
{
    public class RadioamatoreDTOValidator : AbstractValidator<RadioamatoreDto>
    {
        private readonly IConfiguration _conf;
        protected readonly string timeZoneId;
        private readonly IRadiamatoreBl _radioamatoreBl;

        public bool BeALessDateThanToday(string value)
        {
            return DateTimeHelper.BeALessOrEqualDateThanToday(value, this.timeZoneId);
        }
        public RadioamatoreDTOValidator(IConfiguration conf, IRadiamatoreBl radioamatoreBl)
        {
            this._conf = conf;           
            this._radioamatoreBl = radioamatoreBl;
            Init();
        }
        private void Init()
        {
            RuleFor(x => x)
                .NotNull().WithMessage("Radioamatore deve essere valorizzato");

            CognomeValidator();
            NomeValidator();           
            DataDiNascitaValidator();
            CodiceFiscaleValidator();
        }
        protected virtual void NomeValidator()
        {
            RuleFor(x => x.Nome)
                .NotEmpty().WithMessage($"{nameof(RadioamatoreDto.Nome)} deve essere valorizzato");
        }

        protected virtual void CognomeValidator()
        {
            RuleFor(x => x.Cognome)
                .NotEmpty().WithMessage($"{nameof(RadioamatoreDto.Cognome)} deve essere valorizzato");
        }

        protected virtual void DenominazioneValidator()
        {
            RuleFor(x => x.Denominazione)
                .NotEmpty().WithMessage($"{nameof(RadioamatoreDto.Denominazione)} deve essere valorizzato");
        }

        protected virtual void DataDiNascitaValidator()
        {
            RuleFor(x => x.DataDiNascita)
                .Must(DateTimeHelper.BeAValidDate).WithMessage($"{nameof(RadioamatoreDto.DataDiNascita)} Formato della data non valido")
                .Must(BeALessDateThanToday).WithMessage($"{nameof(RadioamatoreDto.DataDiNascita)} deve essere minore o uguale alla data odierna");
        }

        protected virtual void CodiceFiscaleValidator()
        {
            RuleFor(x => x.CodiceFiscale)
                .NotEmpty().WithMessage($"{nameof(RadioamatoreDto.CodiceFiscale)} deve essere valorizzato")
                .Length(16).WithMessage($"{nameof(RadioamatoreDto.CodiceFiscale)} deve essere di 16 caratteri");
        }
    }
}
