﻿using FluentValidation;
using Inv.Fwk.Helper.DateTimeZone;
using Invitalia.Misure.Standard2.Utilities.Extensions;
using Microsoft.Extensions.Configuration;
using MicroSV_Radioamatori.BL.IncomingDTOs;
using MicroSV_Radioamatori.BL.Interfaces;
using MicroSV_Radioamatori.BL.ValueObject;
using System.Linq;
using System.Text.RegularExpressions;

namespace MicroSV_Radioamatori.API.Validators
{
    public class RadioamatoreDaGenerareDtoValidator : AbstractValidator<RadioamatoreDaGenerareDto>
    {
        private readonly IConfiguration _conf;
        protected readonly string timeZoneId;
        private readonly IRadiamatoreBl _radioamatoreBl;
        private const string _prefissoAssociazione = "IQ";
        private const string _regexNuoviNominativi = @"^(IU[0-8]|IX1|IN3|IV3|IT9|IS0|IA5|IC8|IM0|ID9|IG9|IE9|IB0|IJ7|IL7|IF9|IH9|IP1)[A-Za-z]{3}$";
        private const string _assegnatarioAssociazione = "Associazioni o sezioni delle associazioni dei radioamatori legalmente costituite di cui all’art. 144, comma 1, lettera d) del CdC";


        public bool BeALessDateThanToday(string value)
        {
            return DateTimeHelper.BeALessOrEqualDateThanToday(value, this.timeZoneId);
        }

        public RadioamatoreDaGenerareDtoValidator(IConfiguration conf, IRadiamatoreBl radioamatoreBl)
        {
            this._conf = conf;
            this._radioamatoreBl = radioamatoreBl;
            Init();
        }

        private void Init()
        {
            RuleFor(x => x)
                .NotNull().WithMessage("Radioamatore deve essere valorizzato");

            RuleFor(x => x.CodiceNominativo)
                .NotEmpty().WithMessage("{PropertyName} deve essere valorizzato");

            RuleFor(x => x.TipoAssegnatario)
                .NotEmpty().WithMessage("{PropertyName} deve essere valorizzato");

            When(x => !string.IsNullOrWhiteSpace(x.CodiceNominativo) && !string.IsNullOrWhiteSpace(x.TipoAssegnatario), () =>
            {
                RuleFor(x => x.CodiceNominativo)
                    .Must((x, codice) => HaveValidNominativo(codice, x.TipoAssegnatario.EqualsIgnoreCaseAndTrim(_assegnatarioAssociazione)))
                    .WithMessage("Attenzione! Non è possibile utilizzare questa funzionalità per censire nominativi che presentano questa codifica. Per proseguire, disabilitare il flag Non generare nominativo");
            });

            When(x => !string.IsNullOrWhiteSpace(x.CodiceNominativo) && !string.IsNullOrWhiteSpace(x.TipoAssegnatario), () =>
            {
                RuleFor(x => x.CodiceNominativo)
                    .Must((x, codice) => !EsisteNominativo(codice))
                    .WithMessage("Attenzione! il nominativo inserito è già presente a sistema");
            });
        }

        private bool HaveValidNominativo(string codiceNominativo, bool isAssociazione)
        {
            bool isValid;

            if (isAssociazione)
            {
                isValid = codiceNominativo.Trim().ToUpper().StartsWith(_prefissoAssociazione);
            }
            else
            {
                isValid = !Regex.IsMatch(codiceNominativo.ToUpper().Trim(), _regexNuoviNominativi);
            }

            return isValid;
        }

        private bool EsisteNominativo(string codiceNominativo)
        {
            //if (isStandard)
            //{
            var nominativi = _radioamatoreBl.GetRadioamatoriByCodiceNominantivoAsync(codiceNominativo).GetAwaiter().GetResult();
            return nominativi.Any(s => s.TipoNominativo.EqualsIgnoreCaseAndTrim("STANDARD") && (s.StatoNominativo == EnumStatiRadioamatori.ATTIVO.ToString() || s.StatoNominativo == EnumStatiRadioamatori.IN_ISTRUTTORIA.ToString() ||
            s.StatoNominativo == EnumStatiRadioamatori.GENERATO.ToString()));
            //}


        }
    }
}
