﻿using FluentValidation;
using Invitalia.Misure.FiltriAPI;
using Invitalia.Misure.Utilities.WebApi.OAuthTokenOtherApi;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using MicroSV_Radioamatori.API.Exceptions;
using MicroSV_Radioamatori.BL.Interfaces;
using MicroSV_Radioamatori.BL.Utilities;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace MicroSV_Radioamatori.API.Filters
{
    public class CheckAccessAttachmentFilter : IAsyncActionFilter, IFilterMetadata
    {
        private readonly IRadiamatoreBl _radiamatoreBl;
        private readonly ApiContext _apiContext;

        public CheckAccessAttachmentFilter(ApiContext apiContext, IRadiamatoreBl radiamatoreBl)
        {
            _radiamatoreBl = radiamatoreBl;
            _apiContext = apiContext;
        }
        public async Task OnActionExecutionAsync(ActionExecutingContext context, ActionExecutionDelegate next)
        {
            try
            {
                _apiContext.SetApiContextFromHeaders(context.HttpContext.Request.Headers);
                if (_apiContext.UtenteCompilatore == null)
                {
                    throw new CheckAccessAttachmentFilterException($"{nameof(HttpHeaderParamsName.HEADER_PARAM_UTENTE_CORRENTE_BASE_64)} è obbligatorio");
                }

                switch (_apiContext.CodiceMisura?.ToUpper())
                {
                    case "RAD2":
                        {
                            var patenti = await _radiamatoreBl.GetPatentiByCfAsync(_apiContext.UtenteCompilatore?.CodiceFiscale);

                            if (patenti == null || !patenti.Any())
                            {
                                throw new CheckAccessAttachmentFilterException($"Nessuna patente presente per il CF {_apiContext.UtenteCompilatore?.CodiceFiscale}");
                            }

                            var blobName = (string)context.RouteData.Values["blobName"];
                            if (!patenti.Any(p => p.RiferimentoDocumentoPatente.ExistsAndInvariantEqualsTo(blobName) || p.RiferimentoDocumentoHarec.ExistsAndInvariantEqualsTo(blobName)))
                            {
                                throw new CheckAccessAttachmentFilterException($"Il file non esiste o non appartiene all'utente {_apiContext.UtenteCompilatore.CodiceFiscale}");
                            }

                            break;
                        }
                    case "RAD3":
                        {
                            var autorizzazioni = await _radiamatoreBl.GetAutorizzazioneByFilterAsync(new BL.DTOs.AutorizzazioneFilterDto { CodiceFiscale = _apiContext.UtenteCompilatore?.CodiceFiscale } );

                            if (autorizzazioni == null || !autorizzazioni.Any())
                            {
                                throw new CheckAccessAttachmentFilterException($"Nessuna autorizzazione presente per il CF {_apiContext.UtenteCompilatore?.CodiceFiscale}");
                            }

                            var blobName = (string)context.RouteData.Values["blobName"];
                            if (!autorizzazioni.Any(p => p.RiferimentoDocumentoAutorizzazione.ExistsAndInvariantEqualsTo(blobName)))
                            {
                                throw new CheckAccessAttachmentFilterException($"Il file non esiste o non appartiene all'utente {_apiContext.UtenteCompilatore.CodiceFiscale}");
                            }

                            break;
                        }
                    case "RAD5":
                        {
                            var iscrizioni = await _radiamatoreBl.GetIscrizioniSWLByFilterAsync(new BL.DTOs.IscrizioneSWLFilterDto { CodiceFiscale = _apiContext.UtenteCompilatore?.CodiceFiscale } );

                            if (iscrizioni == null || !iscrizioni.Any())
                            {
                                throw new CheckAccessAttachmentFilterException($"Nessuna autorizzazione presente per il CF {_apiContext.UtenteCompilatore?.CodiceFiscale}");
                            }

                            var blobName = (string)context.RouteData.Values["blobName"];
                            if (!iscrizioni.Any(p => p.RiferimentoDocumentoIscrizione_SWL.ExistsAndInvariantEqualsTo(blobName)))
                            {
                                throw new CheckAccessAttachmentFilterException($"Il file non esiste o non appartiene all'utente {_apiContext.UtenteCompilatore.CodiceFiscale}");
                            }

                            break;
                        }
                    default:
                        throw new CheckAccessAttachmentFilterException($"Codice misura {_apiContext.CodiceMisura} non ancora implementato");
                }
            }
            catch (CheckAccessAttachmentFilterException ex)
            {
                context.Result = new BadRequestObjectResult(new ValidationException(ValidationExceptionExtension.GetValidationError(nameof(CheckAccessAttachmentFilterException), ex.Message)));
                return;
            }
            catch (Exception)
            {
                throw;
            }

            await next();
        }
    }
}
