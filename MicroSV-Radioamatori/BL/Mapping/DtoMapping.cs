﻿using AutoMapper;
using Inv.Fwk.Helper.DateTimeZone;
using MicroSV_Radioamatori.BL.Common;
using MicroSV_Radioamatori.BL.DTOs;
using MicroSV_Radioamatori.DAL.Entities;
using MicroSV_Radioamatori.DAL.Interfaces;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;

namespace MicroSV_Radioamatore.BL.Mapping
{
    public class DtoMapping : Profile
    {
        public DtoMapping()
        {
            CreateMap<Radioamatore, RadioamatoreDto>()
                .ForMember(dest => dest.Cap, opt => opt.MapFrom(src => src.CAP))
                .ForMember(dest => dest.CodiceDomandaFE, opt => opt.MapFrom(src => src.CODICE_DOMANDA_FE))
                .ForMember(dest => dest.CodiceFiscale, opt => opt.MapFrom(src => src.CODICE_FISCALE))
                .ForMember(dest => dest.CodiceNominativo, opt => opt.MapFrom(src => src.CODICE_NOMINATIVO))
                .ForMember(dest => dest.CodiceZona, opt => opt.MapFrom(src => src.CODICE_ZONA != null ? src.CODICE_ZONA.CODICE : string.Empty))
                .ForMember(dest => dest.Cognome, opt => opt.MapFrom(src => src.COGNOME))
                .ForMember(dest => dest.Comune, opt => opt.MapFrom(src => src.COMUNE))
                .ForMember(dest => dest.DataAggiornamento, opt => opt.MapFrom(src => src.DATA_AGG.DateTimeToStringFormatItSafe()))
                .ForMember(dest => dest.DataDiNascita, opt => opt.MapFrom(src => src.DATA_NASCITA.DateTimeToStringFormatItSafe()))
                .ForMember(dest => dest.DataProtocollo, opt => opt.MapFrom(src => src.DATA_PROTOCOLLO.DateTimeToStringFormatItSafe()))
                .ForMember(dest => dest.DataRilascio, opt => opt.MapFrom(src => src.DATA_RILASCIO.DateTimeToStringFormatItSafe()))
                .ForMember(dest => dest.Denominazione, opt => opt.MapFrom(src => src.DENOMINAZIONE))
                .ForMember(dest => dest.IdRadioamatore, opt => opt.MapFrom(src => src.ID_RADIOAMATORE))
                .ForMember(dest => dest.Suffisso, opt => opt.MapFrom(src => src.SUFFISSO != null ? src.SUFFISSO.SUFFISSO : string.Empty))
                .ForMember(dest => dest.Indirizzo, opt => opt.MapFrom(src => src.INDIRIZZO))
                .ForMember(dest => dest.Nome, opt => opt.MapFrom(src => src.NOME))
                .ForMember(dest => dest.Nota, opt => opt.MapFrom(src => src.NOTA))
                .ForMember(dest => dest.Protocollo, opt => opt.MapFrom(src => src.PROTOCOLLO))
                .ForMember(dest => dest.Regione, opt => opt.MapFrom(src => src.REGIONE))
                .ForMember(dest => dest.StatoNominativo, opt => opt.MapFrom(src => src.STATO_NOMINATIVO))
                .ForMember(dest => dest.TipoNominativo, opt => opt.MapFrom(src => src.TIPO_NOMINATIVO))
                .ForMember(dest => dest.Utente, opt => opt.MapFrom(src => src.UTENTE))
                .ForMember(dest => dest.DataCreazione, opt => opt.MapFrom(src => src.DATA_CREAZIONE.DateTimeToStringFormatItSafe()))
                .ForMember(dest => dest.IdAnagraficaIspettorato, opt => opt.MapFrom(src => src.ID_ANAGRAFICA_ISPETTORATO))
                .ForMember(dest => dest.DataProtocolloPresentazioneFe, opt => opt.MapFrom(src => src.DATA_PROTOCOLLO_PRESENTAZIONE_FE.DateTimeToStringFormatItSafe()))
                .ForMember(dest => dest.ProtocolloPresentazioneFe, opt => opt.MapFrom(src => src.PROTOCOLLO_PRESENTAZIONE_FE))
                .ForMember(dest => dest.DataScadenza, opt => opt.MapFrom(src => src.DATA_SCADENZA.DateTimeToStringFormatItSafe()))
                .ReverseMap()
                .ForMember(dest => dest.DATA_SCADENZA, opt => opt.MapFrom(src => !string.IsNullOrEmpty(src.DataScadenza) ? DateTimeHelper.ParseDataInput(src.DataScadenza) : (DateTime?)null));

            CreateMap<Radioamatore, ScadenzaNominativoDto>()
                .ForMember(dest => dest.CodiceFiscale, opt => opt.MapFrom(src => src.CODICE_FISCALE))
                .ForMember(dest => dest.CodiceNominativo, opt => opt.MapFrom(src => src.CODICE_NOMINATIVO))
                .ForMember(dest => dest.Cognome, opt => opt.MapFrom(src => src.COGNOME))
                .ForMember(dest => dest.Denominazione, opt => opt.MapFrom(src => src.DENOMINAZIONE))
                .ForMember(dest => dest.Nome, opt => opt.MapFrom(src => src.NOME))
                .ForMember(dest => dest.StatoNominativo, opt => opt.MapFrom(src => src.STATO_NOMINATIVO))
                .ReverseMap();

            CreateMap<IspettoratoRegione, IspettoratoDto>()
                .ForMember(dest => dest.SiglaRegione, opt => opt.MapFrom(src => src.SIGLA_REGIONE))
                .ForMember(dest => dest.DenominazioneIspettorato, opt => opt.MapFrom(src => src.Ispettorato != null ? src.Ispettorato.DENOMINAZIONE : string.Empty))
                .ForMember(dest => dest.IdIspettorato, opt => opt.MapFrom(src => src.Ispettorato != null ? src.Ispettorato.ID : string.Empty))
                .ForMember(dest => dest.Iban, opt => opt.MapFrom(src => src.Ispettorato.IBAN))
                .ForMember(dest => dest.CcPostale, opt => opt.MapFrom(src => src.Ispettorato.CC_POSTALE)); ;

            CreateMap<AnagraficaIspettorato, IspettoratoDto>()
               .ForMember(dest => dest.DenominazioneIspettorato, opt => opt.MapFrom(src => src.DENOMINAZIONE))
               .ForMember(dest => dest.IdIspettorato, opt => opt.MapFrom(src => src.ID))
               .ForMember(dest => dest.IdSubentro, opt => opt.MapFrom(src => src.ID_SUBENTRO))
               .ForMember(dest => dest.Iban, opt => opt.MapFrom(src => src.IBAN))
               .ForMember(dest => dest.CcPostale, opt => opt.MapFrom(src => src.CC_POSTALE))
               .ForMember(dest => dest.DataDismissione, opt => opt.MapFrom(src => src.DATA_DISMISSIONE))
               .ReverseMap();

            CreateMap<AnagraficaContest, AnagraficaContestContestDto>()
               .ForMember(dest => dest.Codice, opt => opt.MapFrom(src => src.CODICE))
               .ForMember(dest => dest.Denominazione, opt => opt.MapFrom(src => src.DENOMINAZIONE))
               .ForMember(dest => dest.DataInizio, opt => opt.MapFrom(src => src.DATA_INIZIO))
               .ForMember(dest => dest.DataFine, opt => opt.MapFrom(src => src.DATA_FINE));

            CreateMap<Contest, ContestDto>()
                .ForMember(dest => dest.IdRadioamatore, opt => opt.MapFrom(src => src.ID_RADIOAMATORE))
                .ForMember(dest => dest.CodiceContest, opt => opt.MapFrom(src => src.CODICE_ANAGRAFICA_CONTEST))
                .ForMember(dest => dest.DataInizio, opt => opt.MapFrom(src => src.ANAGRAFICA_CONTEST != null ? src.ANAGRAFICA_CONTEST.DATA_INIZIO : (DateTime?)null))
                .ForMember(dest => dest.DataFine, opt => opt.MapFrom(src => src.ANAGRAFICA_CONTEST != null ? src.ANAGRAFICA_CONTEST.DATA_FINE : (DateTime?)null));

            CreateMap<TipoManifestazione, TipoManifestazioneDTO>()
                .ForMember(dest => dest.IdTipoManifestazione, opt => opt.MapFrom(src => src.ID_TIPO_MANIFESTAZIONE))
                .ForMember(dest => dest.Descrizione, opt => opt.MapFrom(src => src.DESCRIZIONE));

            CreateMap<Manifestazione, ManifestazioneDto>()
               .ForMember(dest => dest.DataFine, opt => opt.MapFrom(src => src.DATA_FINE))
               .ForMember(dest => dest.DataInizio, opt => opt.MapFrom(src => src.DATA_INIZIO))
               .ForMember(dest => dest.Denominazione, opt => opt.MapFrom(src => src.DENOMINAZIONE))
               .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ID_MANIFESTAZIONE))
               .ForMember(dest => dest.IdAnagraficaIspettorato, opt => opt.MapFrom(src => src.ID_ANAGRAFICA_ISPETTORATO))
               .ForMember(dest => dest.IdRadioamatore, opt => opt.MapFrom(src => src.ID_RADIOAMATORE))
               .ForMember(dest => dest.IdTipoManifestazione, opt => opt.MapFrom(src => src.ID_TIPOMANIFESTAZIONE))
               .ForMember(dest => dest.SiglaRegione, opt => opt.MapFrom(src => src.SIGLA_REGIONE));

            CreateMap<PrefissoSpeciale, PrefissiSpecialiDto>()
                .ForMember(dest => dest.CodicePrefisso, opt => opt.MapFrom(src => src.CODICE_PREFISSO))
                .ForMember(dest => dest.SiglaRegione, opt => opt.MapFrom(src => src.SIGLA_REGIONE))
                .ForMember(dest => dest.DenominazioneRegione, opt => opt.MapFrom(src => src.REGIONE != null ? src.REGIONE.DENOMINAZIONE : string.Empty));

            CreateMap<Regione, RegioneDto>()
                .ForMember(dest => dest.Sigla, opt => opt.MapFrom(src => src.SIGLA))
                .ForMember(dest => dest.Denominazione, opt => opt.MapFrom(src => src.DENOMINAZIONE))
                .ForMember(dest => dest.CodiceIstat, opt => opt.MapFrom(src => src.CODICE_ISTAT));

            CreateMap<PatenteDto, Patente>()
                .ForMember(dest => dest.CAP_RESIDENZA, opt => opt.MapFrom(src => src.CapResidenza))
                .ForMember(dest => dest.CODICE_FISCALE, opt => opt.MapFrom(src => src.CodiceFiscale))
                .ForMember(dest => dest.CODICE_PATENTE, opt => opt.MapFrom(src => src.CodicePatente))
                .ForMember(dest => dest.CODICE_PROTOCOLLO_ENTRATA, opt => opt.MapFrom(src => src.CodiceProtocolloInEntrata))
                .ForMember(dest => dest.CODICE_PROTOCOLLO_USCITA, opt => opt.MapFrom(src => src.CodiceProtocolloInUscita))
                .ForMember(dest => dest.COGNOME, opt => opt.MapFrom(src => src.Cognome))
                .ForMember(dest => dest.COMUNE_RESIDENZA, opt => opt.MapFrom(src => src.ComuneResidenza))
                .ForMember(dest => dest.EMAIL, opt => opt.MapFrom(src => src.Email))
                .ForMember(dest => dest.ID_PATENTE, opt => opt.MapFrom(src => src.Id))
                .ForMember(dest => dest.INDIRIZZO_RESIDENZA, opt => opt.MapFrom(src => src.IndirizzoResidenza))
                .ForMember(dest => dest.ANAGRAFICA_ISPETTORATO, opt => opt.MapFrom(src => src.Ispettorato))
                .ForMember(dest => dest.LUOGO_NASCITA, opt => opt.MapFrom(src => src.LuogoDiNascita))
                .ForMember(dest => dest.NOME, opt => opt.MapFrom(src => src.Nome))
                .ForMember(dest => dest.CODICE_DOMANDA, opt => opt.MapFrom(src => src.CodiceDomanda))
                .ForMember(dest => dest.NUMERO_PATENTE_CARTACEA, opt => opt.MapFrom(src => src.NumeroPatenteCartacea))
                .ForMember(dest => dest.PEC, opt => opt.MapFrom(src => src.Pec))
                .ForMember(dest => dest.PROVINCIA_RESIDENZA, opt => opt.MapFrom(src => src.ProvinciaResidenza))
                .ForMember(dest => dest.REGIONE_RESIDENZA, opt => opt.MapFrom(src => src.RegioneResidenza))
                .ForMember(dest => dest.RIFERIMENTO_DOCUMENTO_HAREC, opt => opt.MapFrom(src => src.RiferimentoDocumentoHarec))
                .ForMember(dest => dest.RIFERIMENTO_DOCUMENTO_PATENTE, opt => opt.MapFrom(src => src.RiferimentoDocumentoPatente))
                .ForMember(dest => dest.SESSO, opt => opt.MapFrom(src => src.Sesso))
                .ForMember(dest => dest.STATO_PATENTE, opt => opt.MapFrom(src => src.StatoPatente))
                .ForMember(dest => dest.TELEFONO, opt => opt.MapFrom(src => src.Telefono))
                .ForMember(dest => dest.UTENTE, opt => opt.MapFrom(src => src.Utente))
                .ForMember(dest => dest.ID_ANAGRAFICA_ISPETTORATO, opt => opt.MapFrom(src => src.IdIspettorato))
                .ForMember(dest => dest.ID_STATO_PATENTE, opt => opt.MapFrom(src => src.IdStatoPatente))
                .ForMember(dest => dest.COMUNE_RESIDENZA, opt => opt.MapFrom(src => src.ComuneResidenza))
                .ForMember(dest => dest.CODICE_COMUNE_RESIDENZA, opt => opt.MapFrom(src => src.CodiceComuneResidenza))
                .ForMember(dest => dest.CODICE_PROVINCIA_RESIDENZA, opt => opt.MapFrom(src => src.CodiceProvinciaResidenza))
                .ForMember(dest => dest.CODICE_REGIONE_RESIDENZA, opt => opt.MapFrom(src => src.CodiceRegioneResidenza))
                .ForMember(dest => dest.CODICE_LUOGO_NASCITA, opt => opt.MapFrom(src => src.CodiceLuogoDiNascita))
                .ForMember(dest => dest.DATA_NASCITA, opt => opt.MapFrom(src => !string.IsNullOrEmpty(src.DataDiNascita) ? DateTimeHelper.ParseDataInput(src.DataDiNascita) : (DateTime?)null))
                .ForMember(dest => dest.DATA_PROTOCOLLO_ENTRATA, opt => opt.MapFrom(src => !string.IsNullOrEmpty(src.DataProtocolloInEntrata) ? DateTimeHelper.ParseDataInput(src.DataProtocolloInEntrata) : (DateTime?)null))
                .ForMember(dest => dest.DATA_PROTOCOLLO_USCITA, opt => opt.MapFrom(src => !string.IsNullOrEmpty(src.DataProtocolloInUscita) ? DateTimeHelper.ParseDataInput(src.DataProtocolloInUscita) : (DateTime?)null))
                .ForMember(dest => dest.DATA_RILASCIO, opt => opt.MapFrom(src => !string.IsNullOrEmpty(src.DataRilascio) ? DateTimeHelper.ParseDataInput(src.DataRilascio) : (DateTime?)null));

            CreateMap<Patente, PatenteDto>()
                .ForMember(dest => dest.CapResidenza, opt => opt.MapFrom(src => src.CAP_RESIDENZA))
                .ForMember(dest => dest.CodiceFiscale, opt => opt.MapFrom(src => src.CODICE_FISCALE))
                .ForMember(dest => dest.CodicePatente, opt => opt.MapFrom(src => src.CODICE_PATENTE))
                .ForMember(dest => dest.CodiceProtocolloInEntrata, opt => opt.MapFrom(src => src.CODICE_PROTOCOLLO_ENTRATA))
                .ForMember(dest => dest.CodiceProtocolloInUscita, opt => opt.MapFrom(src => src.CODICE_PROTOCOLLO_USCITA))
                .ForMember(dest => dest.Cognome, opt => opt.MapFrom(src => src.COGNOME))
                .ForMember(dest => dest.ComuneResidenza, opt => opt.MapFrom(src => src.COMUNE_RESIDENZA))
                .ForMember(dest => dest.Email, opt => opt.MapFrom(src => src.EMAIL))
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ID_PATENTE))
                .ForMember(dest => dest.IndirizzoResidenza, opt => opt.MapFrom(src => src.INDIRIZZO_RESIDENZA))
                .ForMember(dest => dest.Ispettorato, opt => opt.MapFrom(src => src.ANAGRAFICA_ISPETTORATO))
                .ForMember(dest => dest.LuogoDiNascita, opt => opt.MapFrom(src => src.LUOGO_NASCITA))
                .ForMember(dest => dest.Nome, opt => opt.MapFrom(src => src.NOME))
                .ForMember(dest => dest.CodiceDomanda, opt => opt.MapFrom(src => src.CODICE_DOMANDA))
                .ForMember(dest => dest.NumeroPatenteCartacea, opt => opt.MapFrom(src => src.NUMERO_PATENTE_CARTACEA))
                .ForMember(dest => dest.Pec, opt => opt.MapFrom(src => src.PEC))
                .ForMember(dest => dest.ProvinciaResidenza, opt => opt.MapFrom(src => src.PROVINCIA_RESIDENZA))
                .ForMember(dest => dest.RegioneResidenza, opt => opt.MapFrom(src => src.REGIONE_RESIDENZA))
                .ForMember(dest => dest.RiferimentoDocumentoHarec, opt => opt.MapFrom(src => src.RIFERIMENTO_DOCUMENTO_HAREC))
                .ForMember(dest => dest.RiferimentoDocumentoPatente, opt => opt.MapFrom(src => src.RIFERIMENTO_DOCUMENTO_PATENTE))
                .ForMember(dest => dest.Sesso, opt => opt.MapFrom(src => src.SESSO))
                .ForMember(dest => dest.StatoPatente, opt => opt.MapFrom(src => src.STATO_PATENTE))
                .ForMember(dest => dest.Telefono, opt => opt.MapFrom(src => src.TELEFONO))
                .ForMember(dest => dest.Utente, opt => opt.MapFrom(src => src.UTENTE))
                .ForMember(dest => dest.IdIspettorato, opt => opt.MapFrom(src => src.ID_ANAGRAFICA_ISPETTORATO))
                .ForMember(dest => dest.IdStatoPatente, opt => opt.MapFrom(src => src.ID_STATO_PATENTE))
                .ForMember(dest => dest.CodiceComuneResidenza, opt => opt.MapFrom(src => src.CODICE_COMUNE_RESIDENZA))
                .ForMember(dest => dest.CodiceProvinciaResidenza, opt => opt.MapFrom(src => src.CODICE_PROVINCIA_RESIDENZA))
                .ForMember(dest => dest.CodiceRegioneResidenza, opt => opt.MapFrom(src => src.CODICE_REGIONE_RESIDENZA))
                .ForMember(dest => dest.CodiceLuogoDiNascita, opt => opt.MapFrom(src => src.CODICE_LUOGO_NASCITA))
                .ForMember(dest => dest.DataDiNascita, opt => opt.MapFrom(src => src.DATA_NASCITA != null ? DateTimeHelper.FormattaTime(src.DATA_NASCITA.Value) : null))
                .ForMember(dest => dest.DataProtocolloInEntrata, opt => opt.MapFrom(src => src.DATA_PROTOCOLLO_ENTRATA != null ? DateTimeHelper.FormattaTime(src.DATA_PROTOCOLLO_ENTRATA.Value) : null))
                .ForMember(dest => dest.DataProtocolloInUscita, opt => opt.MapFrom(src => src.DATA_PROTOCOLLO_USCITA != null ? DateTimeHelper.FormattaTime(src.DATA_PROTOCOLLO_USCITA.Value) : null))
                .ForMember(dest => dest.DataRilascio, opt => opt.MapFrom(src => src.DATA_RILASCIO != null ? DateTimeHelper.FormattaTime(src.DATA_RILASCIO.Value) : null));

            CreateMap<StatoPatente, StatoPatenteDto>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ID_STATO_PATENTE))
                .ForMember(dest => dest.Descrizione, opt => opt.MapFrom(src => src.DESCRIZIONE))
                .ReverseMap();

            CreateMap<Autorizzazione, AutorizzazioneDto>()
               .ForMember(dest => dest.CapResidenza, opt => opt.MapFrom(src => src.CAP_RESIDENZA))
               .ForMember(dest => dest.CodiceComuneResidenza, opt => opt.MapFrom(src => src.CODICE_COMUNE_RESIDENZA))
               .ForMember(dest => dest.CodiceDomanda, opt => opt.MapFrom(src => src.CODICE_DOMANDA))
               .ForMember(dest => dest.CodiceFiscale, opt => opt.MapFrom(src => src.CODICE_FISCALE))
               .ForMember(dest => dest.CodiceLuogoDiNascita, opt => opt.MapFrom(src => src.CODICE_LUOGO_NASCITA))
               .ForMember(dest => dest.CodiceProtocolloInEntrata, opt => opt.MapFrom(src => src.CODICE_PROTOCOLLO_ENTRATA))
               .ForMember(dest => dest.CodiceProtocolloInUscita, opt => opt.MapFrom(src => src.CODICE_PROTOCOLLO_USCITA))
               .ForMember(dest => dest.CodiceProvinciaResidenza, opt => opt.MapFrom(src => src.CODICE_PROVINCIA_RESIDENZA))
               .ForMember(dest => dest.CodiceRegioneResidenza, opt => opt.MapFrom(src => src.CODICE_REGIONE_RESIDENZA))
               .ForMember(dest => dest.ComuneResidenza, opt => opt.MapFrom(src => src.COMUNE_RESIDENZA))
               .ForMember(dest => dest.Denominazione, opt => opt.MapFrom(src => src.DENOMINAZIONE))
               .ForMember(dest => dest.Email, opt => opt.MapFrom(src => src.EMAIL))
               .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ID_AUTORIZZAZIONE))
               .ForMember(dest => dest.IdAutorizzazioneRinnovata, opt => opt.MapFrom(src => src.ID_AUTORIZZAZIONE_RINNOVATA))
               .ForMember(dest => dest.IndirizzoResidenza, opt => opt.MapFrom(src => src.INDIRIZZO_RESIDENZA))
               .ForMember(dest => dest.IsDocumentoAutorizzazioneVisibileFe, opt => opt.MapFrom(src => src.DOCUMENTO_AUTORIZZAZIONE_VISIBILE_FE))
               .ForMember(dest => dest.IsRinnovo, opt => opt.MapFrom(src => src.RINNOVO))
               .ForMember(dest => dest.LuogoDiNascita, opt => opt.MapFrom(src => src.LUOGO_NASCITA))
               .ForMember(dest => dest.Nome, opt => opt.MapFrom(src => src.NOME))
               .ForMember(dest => dest.NumeroAutorizzazione, opt => opt.MapFrom(src => src.NUMERO_AUTORIZZAZIONE))
               .ForMember(dest => dest.ProvinciaResidenza, opt => opt.MapFrom(src => src.PROVINCIA_RESIDENZA))
               .ForMember(dest => dest.Pec, opt => opt.MapFrom(src => src.PEC))
               .ForMember(dest => dest.RegioneResidenza, opt => opt.MapFrom(src => src.REGIONE_RESIDENZA))
               .ForMember(dest => dest.RiferimentoDocumentoAutorizzazione, opt => opt.MapFrom(src => src.RIFERIMENTO_DOCUMENTO_AUTORIZZAZIONE))
               .ForMember(dest => dest.Sesso, opt => opt.MapFrom(src => src.SESSO))
               .ForMember(dest => dest.Telefono, opt => opt.MapFrom(src => src.TELEFONO))
               .ForMember(dest => dest.Utente, opt => opt.MapFrom(src => src.UTENTE))
               .ForMember(dest => dest.StatoAutorizzazione, opt => opt.MapFrom(src => src.STATO_AUTORIZZAZIONE))
               .ForMember(dest => dest.TipologiaAssegnatario, opt => opt.MapFrom(src => src.TIPO_ASSEGNATARIO))
               .ForMember(dest => dest.TipologiaAutorizzazione, opt => opt.MapFrom(src => src.TIPO_AUTORIZZAZIONE))
               .ForMember(dest => dest.Radioamatore, opt => opt.MapFrom(src => src.RADIOAMATORE))
               .ForMember(dest => dest.AnagraficaIspettorato, opt => opt.MapFrom(src => src.ANAGRAFICA_ISPETTORATO))
               .ForMember(dest => dest.IdIspettorato, opt => opt.MapFrom(src => src.ID_ANAGRAFICA_ISPETTORATO))
               .ForMember(dest => dest.IdRadioamatore, opt => opt.MapFrom(src => src.ID_RADIOAMATORE))
               .ForMember(dest => dest.IdStatoAutorizzazione, opt => opt.MapFrom(src => src.ID_STATO_AUTORIZZAZIONE))
               .ForMember(dest => dest.IdTipologiaAssegnatario, opt => opt.MapFrom(src => src.ID_TIPO_ASSEGNATARIO))
               .ForMember(dest => dest.IdTipologiaAutorizzazione, opt => opt.MapFrom(src => src.ID_TIPO_AUTORIZZAZIONE))
               .ForMember(dest => dest.DataDiNascita, opt => opt.MapFrom(src => src.DATA_NASCITA.HasValue ? DateTimeHelper.FormattaTime(src.DATA_NASCITA.Value) : null))
               .ForMember(dest => dest.DataProtocolloInEntrata, opt => opt.MapFrom(src => src.DATA_PROTOCOLLO_ENTRATA.HasValue ? DateTimeHelper.FormattaTime(src.DATA_PROTOCOLLO_ENTRATA.Value) : null))
               .ForMember(dest => dest.DataProtocolloInUscita, opt => opt.MapFrom(src => src.DATA_PROTOCOLLO_USCITA.HasValue ? DateTimeHelper.FormattaTime(src.DATA_PROTOCOLLO_USCITA.Value) : null))
               .ForMember(dest => dest.DataRilascio, opt => opt.MapFrom(src => src.DATA_RILASCIO.HasValue ? DateTimeHelper.FormattaTime(src.DATA_RILASCIO.Value) : null))
               .ForMember(dest => dest.DataScadenza, opt => opt.MapFrom(src => src.DATA_SCADENZA.HasValue ? DateTimeHelper.FormattaTime(src.DATA_SCADENZA.Value) : null))
               .ReverseMap()
               .ForMember(dest => dest.DATA_NASCITA, opt => opt.MapFrom(src => !string.IsNullOrWhiteSpace(src.DataDiNascita) ? DateTimeHelper.ParseDataInput(src.DataDiNascita) : (DateTime?)null))
               .ForMember(dest => dest.DATA_PROTOCOLLO_ENTRATA, opt => opt.MapFrom(src => !string.IsNullOrWhiteSpace(src.DataProtocolloInEntrata) ? DateTimeHelper.ParseDataInput(src.DataProtocolloInEntrata) : (DateTime?)null))
               .ForMember(dest => dest.DATA_PROTOCOLLO_USCITA, opt => opt.MapFrom(src => !string.IsNullOrWhiteSpace(src.DataProtocolloInUscita) ? DateTimeHelper.ParseDataInput(src.DataProtocolloInUscita) : (DateTime?)null))
               .ForMember(dest => dest.DATA_RILASCIO, opt => opt.MapFrom(src => !string.IsNullOrWhiteSpace(src.DataRilascio) ? DateTimeHelper.ParseDataInput(src.DataRilascio) : (DateTime?)null))
               .ForMember(dest => dest.DATA_SCADENZA, opt => opt.MapFrom(src => !string.IsNullOrWhiteSpace(src.DataScadenza) ? DateTimeHelper.ParseDataInput(src.DataScadenza) : (DateTime?)null));

            CreateMap<AutorizzazioneSR, AutorizzazioneSRDto>()
               .ForMember(dest => dest.CapResidenza, opt => opt.MapFrom(src => src.CAP_RESIDENZA))
               .ForMember(dest => dest.CodiceComuneResidenza, opt => opt.MapFrom(src => src.CODICE_COMUNE_RESIDENZA))
               .ForMember(dest => dest.CodiceDomanda, opt => opt.MapFrom(src => src.CODICE_DOMANDA))
               .ForMember(dest => dest.CodiceFiscale, opt => opt.MapFrom(src => src.CODICE_FISCALE))
               .ForMember(dest => dest.CodiceLuogoDiNascita, opt => opt.MapFrom(src => src.CODICE_LUOGO_NASCITA))
               .ForMember(dest => dest.CodiceProtocolloInEntrata, opt => opt.MapFrom(src => src.CODICE_PROTOCOLLO_ENTRATA))
               .ForMember(dest => dest.CodiceProtocolloInUscita, opt => opt.MapFrom(src => src.CODICE_PROTOCOLLO_USCITA))
               .ForMember(dest => dest.CodiceProvinciaResidenza, opt => opt.MapFrom(src => src.CODICE_PROVINCIA_RESIDENZA))
               .ForMember(dest => dest.CodiceRegioneResidenza, opt => opt.MapFrom(src => src.CODICE_REGIONE_RESIDENZA))
               .ForMember(dest => dest.ComuneResidenza, opt => opt.MapFrom(src => src.COMUNE_RESIDENZA))
               .ForMember(dest => dest.DataDiNascita, opt => opt.MapFrom(src => src.DATA_NASCITA))
               .ForMember(dest => dest.DataRilascio, opt => opt.MapFrom(src => src.DATA_RILASCIO))
               .ForMember(dest => dest.DataScadenza, opt => opt.MapFrom(src => src.DATA_SCADENZA))
               .ForMember(dest => dest.Denominazione, opt => opt.MapFrom(src => src.DENOMINAZIONE))
               .ForMember(dest => dest.Email, opt => opt.MapFrom(src => src.EMAIL))
               .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ID_AUTORIZZAZIONE_SR))
               .ForMember(dest => dest.IdAutorizzazioneRinnovata, opt => opt.MapFrom(src => src.ID_AUTORIZZAZIONE_RINNOVATA))
               .ForMember(dest => dest.IndirizzoResidenza, opt => opt.MapFrom(src => src.INDIRIZZO_RESIDENZA))
               .ForMember(dest => dest.Rinnovo, opt => opt.MapFrom(src => src.RINNOVO))
               .ForMember(dest => dest.LuogoDiNascita, opt => opt.MapFrom(src => src.LUOGO_NASCITA))
               .ForMember(dest => dest.Nome, opt => opt.MapFrom(src => src.NOME))
               .ForMember(dest => dest.NumeroAutorizzazione, opt => opt.MapFrom(src => src.NUMERO_AUTORIZZAZIONE))
               .ForMember(dest => dest.ProvinciaResidenza, opt => opt.MapFrom(src => src.PROVINCIA_RESIDENZA))
               .ForMember(dest => dest.Pec, opt => opt.MapFrom(src => src.PEC))
               .ForMember(dest => dest.RegioneResidenza, opt => opt.MapFrom(src => src.REGIONE_RESIDENZA))
               .ForMember(dest => dest.Sesso, opt => opt.MapFrom(src => src.SESSO))
               .ForMember(dest => dest.Telefono, opt => opt.MapFrom(src => src.TELEFONO))
               .ForMember(dest => dest.Utente, opt => opt.MapFrom(src => src.UTENTE))
               .ForMember(dest => dest.StatoAutorizzazione, opt => opt.MapFrom(src => src.STATO_AUTORIZZAZIONE))
               .ForMember(dest => dest.TipologiaAssegnatario, opt => opt.MapFrom(src => src.TIPO_ASSEGNATARIO))
               .ForMember(dest => dest.TipologiaAutorizzazione, opt => opt.MapFrom(src => src.TIPO_AUTORIZZAZIONE))
               .ForMember(dest => dest.IdRadioamatoreSR, opt => opt.MapFrom(src => src.ID_RADIOAMATORE_SR))
               .ForMember(dest => dest.IdStazioneRipetitrice, opt => opt.MapFrom(src => src.ID_STAZIONE_RIPETITRICE))
               .ForMember(dest => dest.AnagraficaIspettorato, opt => opt.MapFrom(src => src.ANAGRAFICA_ISPETTORATO))
               .ForMember(dest => dest.IdIspettorato, opt => opt.MapFrom(src => src.ID_ANAGRAFICA_ISPETTORATO))
               .ForMember(dest => dest.IdStatoAutorizzazione, opt => opt.MapFrom(src => src.ID_STATO_AUTORIZZAZIONE))
               .ForMember(dest => dest.IdTipologiaAssegnatario, opt => opt.MapFrom(src => src.ID_TIPO_ASSEGNATARIO))
               .ForMember(dest => dest.IdTipologiaAutorizzazione, opt => opt.MapFrom(src => src.ID_TIPO_AUTORIZZAZIONE))
               .ForMember(dest => dest.StazioneRipetritrice, opt => opt.MapFrom(src => src.STAZIONE_RIPETITRICE))
               .ForMember(dest => dest.NominativoStazioneRipetritrice, opt => opt.MapFrom(src => src.NOMINATIVO_STAZIONE_RIPETITRICE))
               .ForMember(dest => dest.DataProtocolloInEntrata, opt => opt.MapFrom(src => src.DATA_PROTOCOLLO_ENTRATA.HasValue ? DateTimeHelper.FormattaTime(src.DATA_PROTOCOLLO_ENTRATA.Value) : null))
               .ForMember(dest => dest.DataProtocolloInUscita, opt => opt.MapFrom(src => src.DATA_PROTOCOLLO_USCITA.HasValue ? DateTimeHelper.FormattaTime(src.DATA_PROTOCOLLO_USCITA.Value) : null))
               .ReverseMap()
               .ForMember(dest => dest.DATA_PROTOCOLLO_ENTRATA, opt => opt.MapFrom(src => !string.IsNullOrWhiteSpace(src.DataProtocolloInEntrata) ? DateTimeHelper.ParseDataInput(src.DataProtocolloInEntrata) : (DateTime?)null))
               .ForMember(dest => dest.DATA_PROTOCOLLO_USCITA, opt => opt.MapFrom(src => !string.IsNullOrWhiteSpace(src.DataProtocolloInUscita) ? DateTimeHelper.ParseDataInput(src.DataProtocolloInUscita) : (DateTime?)null));

            CreateMap<StatoAutorizzazione, StatoAutorizzazioneDto>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ID_STATO_AUTORIZZAZIONE))
                .ForMember(dest => dest.Descrizione, opt => opt.MapFrom(src => src.DESCRIZIONE))
                .ReverseMap();

            CreateMap<TipoAutorizzazione, TipoAutorizzazioneDto>()
               .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ID_TIPO_AUTORIZZAZIONE))
               .ForMember(dest => dest.Descrizione, opt => opt.MapFrom(src => src.DESCRIZIONE))
               .ReverseMap();

            CreateMap<TipoAssegnatario, TipoAssegnatarioDto>()
               .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ID_TIPO_ASSEGNATARIO))
               .ForMember(dest => dest.Descrizione, opt => opt.MapFrom(src => src.DESCRIZIONE))
               .ReverseMap();

            CreateMap<StazioneRipetitrice, StazioneRipetitriceDto>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ID_STAZIONE_RIPETITRICE))
                .ForMember(dest => dest.Regione, opt => opt.MapFrom(src => src.REGIONE))
                .ForMember(dest => dest.CodiceRegione, opt => opt.MapFrom(src => src.CODICE_REGIONE))
                .ForMember(dest => dest.Provincia, opt => opt.MapFrom(src => src.PROVINCIA))
                .ForMember(dest => dest.CodiceProvincia, opt => opt.MapFrom(src => src.CODICE_PROVINCIA))
                .ForMember(dest => dest.Comune, opt => opt.MapFrom(src => src.COMUNE))
                .ForMember(dest => dest.CodiceComune, opt => opt.MapFrom(src => src.CODICE_COMUNE))
                .ForMember(dest => dest.Indirizzo, opt => opt.MapFrom(src => src.INDIRIZZO))
                .ForMember(dest => dest.Civico, opt => opt.MapFrom(src => src.CIVICO))
                .ForMember(dest => dest.Cap, opt => opt.MapFrom(src => src.CAP))
                .ForMember(dest => dest.IdIspettorato, opt => opt.MapFrom(src => src.ID_ANAGRAFICA_ISPETTORATO))
                .ForMember(dest => dest.Latitudine, opt => opt.MapFrom(src => src.LATITUDINE))
                .ForMember(dest => dest.Longitudine, opt => opt.MapFrom(src => src.LONGITUDINE))
                .ForMember(dest => dest.NomeOperatore, opt => opt.MapFrom(src => src.NOME_OPERATORE))
                .ForMember(dest => dest.CognomeOperatore, opt => opt.MapFrom(src => src.COGNOME_OPERATORE))
                .ForMember(dest => dest.CodiceFiscaleOperatore, opt => opt.MapFrom(src => src.CODICE_FISCALE_OPERATORE))
                .ForMember(dest => dest.NominativoOperatore, opt => opt.MapFrom(src => src.NOMINATIVO_OPERATORE))
                .ForMember(dest => dest.RegioneResidenzaOperatore, opt => opt.MapFrom(src => src.REGIONE_RESIDENZA_OPERATORE))
                .ForMember(dest => dest.CodiceRegioneResidenzaOperatore, opt => opt.MapFrom(src => src.CODICE_REGIONE_RESIDENZA_OPERATORE))
                .ForMember(dest => dest.ProvinciaResidenzaOperatore, opt => opt.MapFrom(src => src.PROVINCIA_RESIDENZA_OPERATORE))
                .ForMember(dest => dest.CodiceProvinciaResidenzaOperatore, opt => opt.MapFrom(src => src.CODICE_PROVINCIA_RESIDENZA_OPERATORE))
                .ForMember(dest => dest.ComuneResidenzaOperatore, opt => opt.MapFrom(src => src.COMUNE_RESIDENZA_OPERATORE))
                .ForMember(dest => dest.CodiceComuneResidenzaOperatore, opt => opt.MapFrom(src => src.CODICE_COMUNE_RESIDENZA_OPERATORE))
                .ForMember(dest => dest.IndirizzoResidenzaOperatore, opt => opt.MapFrom(src => src.INDIRIZZO_RESIDENZA_OPERATORE))
                .ForMember(dest => dest.NumeroCivicoResidenzaOperatore, opt => opt.MapFrom(src => src.NUMERO_CIVICO_RESIDENZA_OPERATORE))
                .ForMember(dest => dest.CapResidenzaOperatore, opt => opt.MapFrom(src => src.CAP_RESIDENZA_OPERATORE))
                .ForMember(dest => dest.EmailOperatore, opt => opt.MapFrom(src => src.EMAIL_OPERATORE))
                .ForMember(dest => dest.TelefonoOperatore, opt => opt.MapFrom(src => src.TELEFONO_OPERATORE))
                .ReverseMap();

            CreateMap<RadioamatoreSR, RadioamatoreSRDto>()
                .ForMember(dest => dest.Cap, opt => opt.MapFrom(src => src.CAP))
                .ForMember(dest => dest.CodiceDomandaFE, opt => opt.MapFrom(src => src.CODICE_DOMANDA_FE))
                .ForMember(dest => dest.CodiceFiscale, opt => opt.MapFrom(src => src.CODICE_FISCALE))
                .ForMember(dest => dest.CodiceNominativo, opt => opt.MapFrom(src => src.CODICE_NOMINATIVO))
                .ForMember(dest => dest.Cognome, opt => opt.MapFrom(src => src.COGNOME))
                .ForMember(dest => dest.Comune, opt => opt.MapFrom(src => src.COMUNE))
                .ForMember(dest => dest.DataAggiornamento, opt => opt.MapFrom(src => src.DATA_AGG.DateTimeToStringFormatItSafe()))
                .ForMember(dest => dest.DataDiNascita, opt => opt.MapFrom(src => src.DATA_NASCITA.DateTimeToStringFormatItSafe()))
                .ForMember(dest => dest.DataProtocollo, opt => opt.MapFrom(src => src.DATA_PROTOCOLLO.DateTimeToStringFormatItSafe()))
                .ForMember(dest => dest.DataRilascio, opt => opt.MapFrom(src => src.DATA_RILASCIO.DateTimeToStringFormatItSafe()))
                .ForMember(dest => dest.DataScadenza, opt => opt.MapFrom(src => src.DATA_SCADENZA.DateTimeToStringFormatItSafe()))
                .ForMember(dest => dest.Denominazione, opt => opt.MapFrom(src => src.DENOMINAZIONE))
                .ForMember(dest => dest.IdRadioamatore, opt => opt.MapFrom(src => src.ID_RADIOAMATORE_SR))
                .ForMember(dest => dest.Indirizzo, opt => opt.MapFrom(src => src.INDIRIZZO))
                .ForMember(dest => dest.Nome, opt => opt.MapFrom(src => src.NOME))
                .ForMember(dest => dest.Nota, opt => opt.MapFrom(src => src.NOTA))
                .ForMember(dest => dest.Protocollo, opt => opt.MapFrom(src => src.PROTOCOLLO))
                .ForMember(dest => dest.Regione, opt => opt.MapFrom(src => src.REGIONE))
                .ForMember(dest => dest.StatoNominativo, opt => opt.MapFrom(src => src.STATO_NOMINATIVO))
                .ForMember(dest => dest.Utente, opt => opt.MapFrom(src => src.UTENTE))
                .ForMember(dest => dest.DataCreazione, opt => opt.MapFrom(src => src.DATA_CREAZIONE.DateTimeToStringFormatItSafe()))
                .ForMember(dest => dest.TipologiaAssegnatario, opt => opt.MapFrom(src => src.TIPOLOGIA_ASSEGNATARIO))
                .ForMember(dest => dest.IdAnagraficaIspettorato, opt => opt.MapFrom(src => src.ID_ANAGRAFICA_ISPETTORATO))
                .ForMember(dest => dest.DataProtocolloPresentazioneFe, opt => opt.MapFrom(src => src.DATA_PROTOCOLLO_PRESENTAZIONE_FE.DateTimeToStringFormatItSafe()))
                .ForMember(dest => dest.ProtocolloPresentazioneFe, opt => opt.MapFrom(src => src.PROTOCOLLO_PRESENTAZIONE_FE))
                .ForMember(dest => dest.IdAnagraficaIspettorato, opt => opt.MapFrom(src => src.ID_ANAGRAFICA_ISPETTORATO))
                .ForMember(dest => dest.AnagraficaIspettorato, opt => opt.MapFrom(src => src.ANAGRAFICA_ISPETTORATO))
                .ForMember(dest => dest.NumeroAutorizzazioneSr, opt => opt.MapFrom(src => src.NUMERO_AUTORIZZAZIONE_SR))
                .ForMember(dest => dest.CodiceDomanda, opt => opt.MapFrom(src => src.CODICE_DOMANDA))
                .ForMember(dest => dest.Pec, opt => opt.MapFrom(src => src.PEC))
                .ForMember(dest => dest.Email, opt => opt.MapFrom(src => src.EMAIL))
                .ForMember(dest => dest.Telefono, opt => opt.MapFrom(src => src.TELEFONO))
                .ForMember(dest => dest.CodiceFiscale, opt => opt.MapFrom(src => src.CODICE_FISCALE))
                .ForMember(dest => dest.IdAnagraficaIspettorato, opt => opt.MapFrom(src => src.ID_ANAGRAFICA_ISPETTORATO))
                .ReverseMap()
                .ForMember(dest => dest.DATA_NASCITA, opt => opt.MapFrom(src => !string.IsNullOrEmpty(src.DataDiNascita) ? DateTimeHelper.ParseDataInput(src.DataDiNascita) : (DateTime?)null))
                .ForMember(dest => dest.DATA_RILASCIO, opt => opt.MapFrom(src => !string.IsNullOrEmpty(src.DataRilascio) ? DateTimeHelper.ParseDataInput(src.DataRilascio) : (DateTime?)null))
                .ForMember(dest => dest.DATA_SCADENZA, opt => opt.MapFrom(src => !string.IsNullOrEmpty(src.DataScadenza) ? DateTimeHelper.ParseDataInput(src.DataScadenza) : (DateTime?)null))
                .ForMember(dest => dest.DATA_PROTOCOLLO, opt => opt.MapFrom(src => !string.IsNullOrEmpty(src.DataProtocollo) ? DateTimeHelper.ParseDataInput(src.DataScadenza) : (DateTime?)null));

            CreateMap<Autorizzazione, ScadenzaAutorizzazioneDto>()
               .ForMember(dest => dest.Nome, opt => opt.MapFrom(src => src.NOME))
               .ForMember(dest => dest.Cognome, opt => opt.MapFrom(src => src.COGNOME))
               .ForMember(dest => dest.Denominazione, opt => opt.MapFrom(src => src.DENOMINAZIONE))
               .ForMember(dest => dest.CodiceFiscale, opt => opt.MapFrom(src => src.CODICE_FISCALE))
               .ForMember(dest => dest.NumeroAutorizzazione, opt => opt.MapFrom(src => src.NUMERO_AUTORIZZAZIONE))
               .ReverseMap();

            CreateMap<RadioamatoreSR, ScadenzaNominativoSRDto>()
                .ForMember(dest => dest.CodiceFiscale, opt => opt.MapFrom(src => src.CODICE_FISCALE))
                .ForMember(dest => dest.CodiceNominativo, opt => opt.MapFrom(src => src.CODICE_NOMINATIVO))
                .ForMember(dest => dest.Cognome, opt => opt.MapFrom(src => src.COGNOME))
                .ForMember(dest => dest.Denominazione, opt => opt.MapFrom(src => src.DENOMINAZIONE))
                .ForMember(dest => dest.Nome, opt => opt.MapFrom(src => src.NOME))
                .ForMember(dest => dest.StatoNominativo, opt => opt.MapFrom(src => src.STATO_NOMINATIVO))
                .ReverseMap();

            CreateMap<AutorizzazioneSR, ScadenzaAutorizzazioneSRDto>()
               .ForMember(dest => dest.Nome, opt => opt.MapFrom(src => src.NOME))
               .ForMember(dest => dest.Cognome, opt => opt.MapFrom(src => src.COGNOME))
               .ForMember(dest => dest.Denominazione, opt => opt.MapFrom(src => src.DENOMINAZIONE))
               .ForMember(dest => dest.CodiceFiscale, opt => opt.MapFrom(src => src.CODICE_FISCALE))
               .ForMember(dest => dest.NumeroAutorizzazione, opt => opt.MapFrom(src => src.NUMERO_AUTORIZZAZIONE))
               .ReverseMap();

            CreateMap<IscrizioneSWL, IscrizioneSWLDto>()
                .ForMember(dest => dest.CapResidenza, opt => opt.MapFrom(src => src.CAP_RESIDENZA))
                .ForMember(dest => dest.CodiceComuneResidenza, opt => opt.MapFrom(src => src.CODICE_COMUNE_RESIDENZA))
                .ForMember(dest => dest.CodiceDomanda, opt => opt.MapFrom(src => src.CODICE_DOMANDA))
                .ForMember(dest => dest.CodiceFiscale, opt => opt.MapFrom(src => src.CODICE_FISCALE))
                .ForMember(dest => dest.CodiceLuogoNascita, opt => opt.MapFrom(src => src.CODICE_LUOGO_NASCITA))
                .ForMember(dest => dest.CodiceProtocolloEntrata, opt => opt.MapFrom(src => src.CODICE_PROTOCOLLO_ENTRATA))
                .ForMember(dest => dest.CodiceProtocolloUscita, opt => opt.MapFrom(src => src.CODICE_PROTOCOLLO_USCITA))
                .ForMember(dest => dest.CodiceProvinciaResidenza, opt => opt.MapFrom(src => src.CODICE_PROVINCIA_RESIDENZA))
                .ForMember(dest => dest.CodiceRegioneResidenza, opt => opt.MapFrom(src => src.CODICE_REGIONE_RESIDENZA))
                .ForMember(dest => dest.Cognome, opt => opt.MapFrom(src => src.COGNOME))
                .ForMember(dest => dest.ComuneResidenza, opt => opt.MapFrom(src => src.COMUNE_RESIDENZA))
                .ForMember(dest => dest.DataNascita, opt => opt.MapFrom(src => src.DATA_NASCITA.HasValue ? DateTimeHelper.FormattaTime(src.DATA_NASCITA.Value) : null))
                .ForMember(dest => dest.DataProtocolloEntrata, opt => opt.MapFrom(src => src.DATA_PROTOCOLLO_ENTRATA.HasValue ? DateTimeHelper.FormattaTime(src.DATA_PROTOCOLLO_ENTRATA.Value) : null))
                .ForMember(dest => dest.DataProtocolloUscita, opt => opt.MapFrom(src => src.DATA_PROTOCOLLO_USCITA.HasValue ? DateTimeHelper.FormattaTime(src.DATA_PROTOCOLLO_USCITA.Value) : null))
                .ForMember(dest => dest.DataRilascio, opt => opt.MapFrom(src => src.DATA_RILASCIO.HasValue ? DateTimeHelper.FormattaTime(src.DATA_RILASCIO.Value) : null))
                .ForMember(dest => dest.Email, opt => opt.MapFrom(src => src.EMAIL))
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ID_ISCRIZIONE_SWL))
                .ForMember(dest => dest.IdAnagraficaIspettorato, opt => opt.MapFrom(src => src.ID_ANAGRAFICA_ISPETTORATO))
                .ForMember(dest => dest.IdStatoIscrizioneSWL, opt => opt.MapFrom(src => src.ID_STATO_ISCRIZIONE_SWL))
                .ForMember(dest => dest.TipologiaRichiedente, opt => opt.MapFrom(src => src.TIPOLOGIA_RICHIEDENTE))
                .ForMember(dest => dest.IndirizzoResidenza, opt => opt.MapFrom(src => src.INDIRIZZO_RESIDENZA))
                .ForMember(dest => dest.Ispettorato, opt => opt.MapFrom(src => src.ANAGRAFICA_ISPETTORATO))
                .ForMember(dest => dest.LuogoNascita, opt => opt.MapFrom(src => src.LUOGO_NASCITA))
                .ForMember(dest => dest.Nome, opt => opt.MapFrom(src => src.NOME))
                .ForMember(dest => dest.NumeroIscrizioneSWL, opt => opt.MapFrom(src => src.NUMERO_ISCRIZIONE_SWL))
                .ForMember(dest => dest.ProvinciaResidenza, opt => opt.MapFrom(src => src.PROVINCIA_RESIDENZA))
                .ForMember(dest => dest.Pec, opt => opt.MapFrom(src => src.PEC))
                .ForMember(dest => dest.RegioneResidenza, opt => opt.MapFrom(src => src.REGIONE_RESIDENZA))
                .ForMember(dest => dest.RiferimentoDocumentoIscrizione_SWL, opt => opt.MapFrom(src => src.RIFERIMENTO_DOCUMENTO_ISCRIZIONE_SWL))
                .ForMember(dest => dest.Sesso, opt => opt.MapFrom(src => src.SESSO))
                .ForMember(dest => dest.StatoIscrizioneSWL, opt => opt.MapFrom(src => src.STATO_ISCRIZIONE_SWL))
                .ForMember(dest => dest.Telefono, opt => opt.MapFrom(src => src.TELEFONO))
                .ForMember(dest => dest.Utente, opt => opt.MapFrom(src => src.UTENTE))
                .ReverseMap()
                .ForMember(dest => dest.DATA_NASCITA, opt => opt.MapFrom(src => !string.IsNullOrWhiteSpace(src.DataNascita) ? DateTimeHelper.ParseDataInput(src.DataNascita) : (DateTime?)null))
                .ForMember(dest => dest.DATA_PROTOCOLLO_ENTRATA, opt => opt.MapFrom(src => !string.IsNullOrWhiteSpace(src.DataProtocolloEntrata) ? DateTimeHelper.ParseDataInput(src.DataProtocolloEntrata) : (DateTime?)null))
                .ForMember(dest => dest.DATA_PROTOCOLLO_USCITA, opt => opt.MapFrom(src => !string.IsNullOrWhiteSpace(src.DataProtocolloUscita) ? DateTimeHelper.ParseDataInput(src.DataProtocolloUscita) : (DateTime?)null))
                .ForMember(dest => dest.DATA_RILASCIO, opt => opt.MapFrom(src => !string.IsNullOrWhiteSpace(src.DataRilascio) ? DateTimeHelper.ParseDataInput(src.DataRilascio) : (DateTime?)null));

            CreateMap<StatoIscrizioneSWL, StatoIscrizioneSWLDto>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ID_STATO_ISCRIZIONE_SWL))
                .ForMember(dest => dest.Descrizione, opt => opt.MapFrom(src => src.DESCRIZONE))
                .ReverseMap();

            CreateMap<SuffissoSR, SuffissoSRDto>()
                .ForMember(dest => dest.IdSussissoSR, opt => opt.MapFrom(src => src.ID_SUFFISSO_SR))
                .ForMember(dest => dest.Disponibile, opt => opt.MapFrom(src => src.DISPONIBILE))
                .ForMember(dest => dest.Prefisso, opt => opt.MapFrom(src => src.PREFISSO))
                .ForMember(dest => dest.Suffisso, opt => opt.MapFrom(src => src.SUFFISSO))
                .ReverseMap();
        }

        public static Radioamatore EstrapolaRadioamatoreDalJson(JObject domanda, IZonaMappingDal zonaMappingDal)
        {
            JToken dati = domanda.SelectToken("Dati");
            JToken assegnatario = domanda.SelectToken("Assegnatario");
            JToken firmatario = domanda.SelectToken("Firmatario");
            bool isStandard = Convert.ToBoolean(dati.SelectToken("IsStandard"));
            bool isStandardMinorenne = Convert.ToBoolean(dati.SelectToken("IsStandardMinorenne"));
            bool isStandardArt144Cdc = Convert.ToBoolean(dati.SelectToken("IsStandardArt144Cdc"));
            bool isSpecialePerContest = Convert.ToBoolean(dati.SelectToken("IsSpecialePerContest"));
            bool isSpecialePerManifestazione = Convert.ToBoolean(dati.SelectToken("IsSpecialePerManifestazione"));
            bool isSpecialePerManifestazioneArt144Cdc = Convert.ToBoolean(dati.SelectToken("IsSpecialePerManifestazioneArt144Cdc"));

            Radioamatore ret = new Radioamatore();

            ret.TIPOLOGIA_ASSEGNATARIO = GetValue(assegnatario, "TipoAssegnatario");

            if (isStandard || isStandardMinorenne || isStandardArt144Cdc || isSpecialePerManifestazione || isSpecialePerManifestazioneArt144Cdc)
            {
                // dati assegnatario
                ret.CAP = GetValue(assegnatario, "CapDiResidenza");
                ret.CODICE_DOMANDA_FE = GetValue(dati, "Protocollo");
                ret.CODICE_FISCALE = GetValue(assegnatario, "CodiceFiscale");
                ret.CODICE_ZONA_DENOMINAZIONE = GetValue(assegnatario, "Zona");
                ret.COGNOME = GetValue(assegnatario, "Cognome");
                ret.COMUNE = GetValue(assegnatario, "DenominazioneComuneDiResidenza");
                if (isStandardMinorenne)
                {
                    ret.DATA_NASCITA = DateTime.ParseExact(GetValue(assegnatario, "DataDiNascita"), "dd/MM/yyyy", CultureInfo.InvariantCulture);
                }
                else
                {
                    ret.DATA_NASCITA = DateTime.ParseExact(GetValue(firmatario, "DataDiNascita"), "dd/MM/yyyy", CultureInfo.InvariantCulture);
                }

                // TODO ret.DATA_PROTOCOLLO // data protocollo Mise
                // TODO ret.DATA_RILASCIO // data di esito positivo da BE
                ret.DENOMINAZIONE = GetValue(assegnatario, "Denominazione");
                ret.INDIRIZZO = $"{GetValue(assegnatario, "IndirizzoDiResidenza")} {GetValue(assegnatario, "NumeroCivicoDiResidenza")}";
                ret.IS_ZONA_COMUNE_SPECIALE = Convert.ToBoolean(GetValue(assegnatario, "IsZonaComuneSpeciale"));
                ret.NOME = GetValue(assegnatario, "Nome");
                // TODO ret.PROTOCOLLO // protocollo Mise
                ret.PROVINCIA = GetValue(assegnatario, "SiglaProvinciaDiResidenza");
                ret.REGIONE = GetValue(assegnatario, "DenominazioneRegioneDiResidenza");
                ret.SIGLA_REGIONE = GetValue(assegnatario, "SiglaRegioneDiResidenza");
                ret.STATO_NOMINATIVO = "GENERATO";
                ret.ID_ANAGRAFICA_ISPETTORATO = GetValue(assegnatario, "IdIspettoratoDiCompetenza");
                ret.TIPO_NOMINATIVO = isStandard || isStandardMinorenne || isStandardArt144Cdc ? "STANDARD" : "MANIFESTAZIONE";
                // TODO ret.UTENTE
            }
            else if (isSpecialePerContest)
            {
                ret.CAP = GetValue(assegnatario, "CapDiResidenza");
                ret.CODICE_DOMANDA_FE = GetValue(dati, "Protocollo");
                ret.CODICE_FISCALE = GetValue(firmatario, "CodiceFiscale");
                ret.CODICE_ZONA_DENOMINAZIONE = GetValue(assegnatario, "Zona");
                ret.COGNOME = GetValue(firmatario, "Cognome");
                ret.COMUNE = GetValue(assegnatario, "DenominazioneComuneDiResidenza");
                ret.DATA_NASCITA = DateTime.ParseExact(GetValue(firmatario, "DataDiNascita"), "dd/MM/yyyy", CultureInfo.InvariantCulture);
                // TODO ret.DATA_PROTOCOLLO // data protocollo Mise
                // TODO ret.DATA_RILASCIO // data di esito positivo da BE
                ret.DENOMINAZIONE = GetValue(assegnatario, "Denominazione");
                ret.INDIRIZZO = $"{GetValue(assegnatario, "IndirizzoDiResidenza")} {GetValue(assegnatario, "NumeroCivicoDiResidenza")}";
                ret.NOME = GetValue(firmatario, "Nome");
                // TODO ret.PROTOCOLLO // protocollo Mise
                ret.PROVINCIA = GetValue(assegnatario, "SiglaProvinciaDiResidenza");
                ret.REGIONE = GetValue(assegnatario, "DenominazioneRegioneDiResidenza");
                ret.SIGLA_REGIONE = GetValue(assegnatario, "SiglaRegioneDiResidenza");
                ret.STATO_NOMINATIVO = "GENERATO";
                ret.TIPO_NOMINATIVO = "CONTEST";
                ret.ID_ANAGRAFICA_ISPETTORATO = GetValue(assegnatario, "IdIspettoratoDiCompetenza");
                // TODO ret.UTENTE
            }
            else
            {
                throw new Exception("Domanda non riconducibile a una tipologia tra standard, manifestazione e contest");
            }

            // Set id_codice_zona
            CodiceZona codiceZona = zonaMappingDal.GetCodiceZonaAsync(ret.CODICE_ZONA_DENOMINAZIONE).GetAwaiter().GetResult();
            if (codiceZona == null)
            {
                throw new Exception($"Codice zona {ret.CODICE_ZONA_DENOMINAZIONE} presente nella domanda {ret.CODICE_DOMANDA_FE} non trovato sul DB radioamatori.");
            }
            ret.ID_CODICE_ZONA = codiceZona.ID_CODICE_ZONA;
            ret.CODICE_ZONA_STRING = codiceZona.CODICE;
            ret.TIPO_ZONA = codiceZona.TIPO;

            return ret;
        }
        public static Radioamatore EstrapolaRadioamatoreAutorizzazioniDalJson(JObject domanda, IZonaMappingDal zonaMappingDal)
        {
            JToken AppRichiestaAutorizzazioneRadioamatore = domanda.SelectToken("resultSet[0].AppRichiestaAutorizzazioneRadioamatore");
            JToken dati = AppRichiestaAutorizzazioneRadioamatore.SelectToken("Dati");
            JToken assegnatario = AppRichiestaAutorizzazioneRadioamatore.SelectToken("Assegnatario");
            JToken metadatiBO = AppRichiestaAutorizzazioneRadioamatore.SelectToken("MetadatiBO");
            JToken firmatario = AppRichiestaAutorizzazioneRadioamatore.SelectToken("Firmatario");

            bool isStandard = dati?.Value<int?>("IdTipologiaAssegnatario") == 1;

            bool isStandardMinorenne = Convert.ToBoolean(dati?.SelectToken("IsAssegnatarioMinorenne"));
            bool isStandardArt144Cdc = Convert.ToBoolean(dati?.SelectToken("IsAssegnatarioSoggettoArt144"));

            Radioamatore ret = new Radioamatore();

            ret.TIPOLOGIA_ASSEGNATARIO = GetValue(assegnatario, "IdTipologiaAssegnatario");

            if (isStandard || isStandardMinorenne || isStandardArt144Cdc)
            {
                // dati assegnatario
                ret.CAP = GetValue(assegnatario, "CapDiResidenza");
                ret.CODICE_DOMANDA_FE = GetValue(dati, "Protocollo");
                ret.CODICE_FISCALE = GetValue(assegnatario, "CodiceFiscale");
                ret.CODICE_ZONA_DENOMINAZIONE = GetValue(assegnatario, "Zona");
                ret.COGNOME = GetValue(assegnatario, "Cognome");
                ret.COMUNE = GetValue(assegnatario, "DenominazioneComuneDiResidenza");
                if (isStandardMinorenne)
                {
                    ret.DATA_NASCITA = DateTime.ParseExact(GetValue(assegnatario, "DataDiNascita"), "dd/MM/yyyy", CultureInfo.InvariantCulture);
                }
                else
                {
                    ret.DATA_NASCITA = DateTime.ParseExact(GetValue(firmatario.SelectToken("DatiAnagrafici"), "DataDiNascita"), "dd/MM/yyyy", CultureInfo.InvariantCulture);
                }

                // TODO ret.DATA_PROTOCOLLO // data protocollo Mise
                // TODO ret.DATA_RILASCIO // data di esito positivo da BE
                ret.DENOMINAZIONE = GetValue(assegnatario, "Denominazione");
                ret.INDIRIZZO = $"{GetValue(assegnatario, "IndirizzoDiResidenza")} {GetValue(assegnatario, "NumeroCivicoDiResidenza")}";
                ret.IS_ZONA_COMUNE_SPECIALE = bool.TryParse((GetValue(assegnatario, "IsZonaComuneSpeciale")),out bool zonaSpeciale);
                ret.NOME = GetValue(assegnatario, "Nome");
                // TODO ret.PROTOCOLLO // protocollo Mise
                ret.PROVINCIA = GetValue(assegnatario, "SiglaProvinciaDiResidenza");
                ret.REGIONE = GetValue(assegnatario, "DenominazioneRegioneDiResidenza");
                ret.SIGLA_REGIONE = GetValue(assegnatario, "SiglaRegioneDiResidenza");
                ret.STATO_NOMINATIVO = "GENERATO";
                ret.ID_ANAGRAFICA_ISPETTORATO = GetValue(assegnatario, "IdIspettoratoDiCompetenza");
                ret.TIPO_NOMINATIVO = isStandard || isStandardMinorenne || isStandardArt144Cdc ? "STANDARD" : "MANIFESTAZIONE";
                // TODO ret.UTENTE
            }
            else
            {
                throw new Exception("Domanda non riconducibile a una tipologia tra standard, manifestazione e contest");
            }

            // Set id_codice_zona
            CodiceZona codiceZona = zonaMappingDal.GetCodiceZonaAsync(ret.CODICE_ZONA_DENOMINAZIONE).GetAwaiter().GetResult();
            if (codiceZona == null)
            {
                throw new Exception($"Codice zona {ret.CODICE_ZONA_DENOMINAZIONE} presente nella domanda {ret.CODICE_DOMANDA_FE} non trovato sul DB radioamatori.");
            }
            ret.ID_CODICE_ZONA = codiceZona.ID_CODICE_ZONA;
            ret.CODICE_ZONA_STRING = codiceZona.CODICE;
            ret.TIPO_ZONA = codiceZona.TIPO;

            return ret;
        }
        public static Manifestazione EstrapolaManifestazioneDalJson(JObject domanda)
        {
            JToken manifestazione = domanda.SelectToken("Manifestazione");

            Manifestazione ret = new Manifestazione();
            ret.DATA_FINE = DateTime.ParseExact(GetValue(manifestazione, "DataFine"), "dd/MM/yyyy", CultureInfo.InvariantCulture);
            ret.DATA_INIZIO = DateTime.ParseExact(GetValue(manifestazione, "DataInizio"), "dd/MM/yyyy", CultureInfo.InvariantCulture);
            ret.DENOMINAZIONE = GetValue(manifestazione, "Denominazione");
            ret.ID_TIPOMANIFESTAZIONE = int.Parse(GetValue(manifestazione, "IdTipoManifestazione"));
            ret.ID_ANAGRAFICA_ISPETTORATO = GetValue(manifestazione, "IdIspettoratoDiCompetenza");
            ret.SIGLA_REGIONE = GetValue(manifestazione, "SiglaRegione");
            ret.NOMINATIVO_PROPOSTO = GetValue(manifestazione, "Nominativo");
            return ret;
        }

        public static List<Contest> EstrapolaElencoContestDalJson(JObject domanda, out string codiceNominativoProposto)
        {
            JToken contest = domanda.SelectToken("Contest");
            JArray elencoContest = GetJArray((JObject)contest, "ElencoContest");
            codiceNominativoProposto = GetValue(contest, "Nominativo");
            return elencoContest.Select(x => new Contest { CODICE_ANAGRAFICA_CONTEST = GetValue(x, "Codice") }).ToList();
        }


        private static string GetValue(JToken token, string valueName)
        {
            JObject obj = (JObject)token;
            if (obj == null || !obj.ContainsKey(valueName)) return ""; //  throw new ArgumentOutOfRangeException("Key " + valueName + " non presente");

            return obj.SelectToken(valueName)?.ToString();
        }

        private static JArray GetJArray(JObject json, string tokenName)
        {
            if (!json.ContainsKey(tokenName)) throw new ArgumentOutOfRangeException("Token " + tokenName + " non presente");

            var token = json.SelectTokens(tokenName).ToList().First().ToList();
            return JArray.Parse(JsonConvert.SerializeObject(token));
        }

    }
}
