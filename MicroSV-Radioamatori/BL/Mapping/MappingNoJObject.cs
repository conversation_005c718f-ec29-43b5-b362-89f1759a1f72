﻿using AutoMapper;
using Microsoft.AspNetCore.Components.Forms;
using MicroSV_Radioamatori.BL.DTOs;
using MicroSV_Radioamatori.BL.IncomingDTOs;
using MicroSV_Radioamatori.DAL.Entities;
using MicroSV_Radioamatori.DAL.Interfaces;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Threading.Tasks;

namespace MicroSV_Radioamatori.BL.Mapping
{
    public class MappingNoJObject
    {

        public static List<Contest> MapIncomingToDBContest(ContestDaGenerareDto contest, out string codiceNominativoProposto)
        {
            var ret = contest.ListaContest.Select(c => new Contest()
            {
                CODICE_ANAGRAFICA_CONTEST = c.Codice
            })
                .ToList();

            codiceNominativoProposto = contest.NominativoContest;
            return ret;
        }

        public static async Task<Manifestazione> MapIncomingToDBManifestazione(ManifestazioneDaGenerareDto manifestazione, IManifestazioneDal manifestazioneDal, IIspettoratoDal ispettoratoDal)
        {
            try
            {
                Manifestazione ret = new Manifestazione();
                //VERIFICARE Conversioni date
                //ret.DATA_FINE = DateTime.ParseExact(manifestazione.DataFine.Value.ToString(), "dd/MM/yyyy", CultureInfo.InvariantCulture);
                //ret.DATA_INIZIO = DateTime.ParseExact(manifestazione.DataInizio.Value.ToString(), "dd/MM/yyyy", CultureInfo.InvariantCulture);
                ret.DATA_INIZIO = manifestazione.DataInizio.Value;
                ret.DATA_FINE = manifestazione.DataFine.Value;
                ret.DENOMINAZIONE = manifestazione.Denominazione;
                var tipiManifestazione = await manifestazioneDal.GetTipologieManifestazioni();
                ret.ID_TIPOMANIFESTAZIONE = tipiManifestazione
                                                .Where(m => m.DESCRIZIONE.ToLower() == manifestazione.TipoManifestazione.Trim().ToLower())
                                                .Select(m => m.ID_TIPO_MANIFESTAZIONE)
                                                .FirstOrDefault();

                var anagraficheIspettorato = await ispettoratoDal.GetAnagraficaIspettoratiAsync();
                ret.ID_ANAGRAFICA_ISPETTORATO = anagraficheIspettorato
                                                .Where(isp => isp.DENOMINAZIONE.ToLower() == manifestazione.IspettoratoManifestazione.Trim().ToLower())
                                                .Select(isp => isp.ID)
                                                .FirstOrDefault();

                ret.SIGLA_REGIONE = manifestazione.RegioneUbicazione.sigla == "BAS" ? manifestazione.SiglaProvinciaBasilicata : manifestazione.RegioneUbicazione.sigla;
                ret.NOMINATIVO_PROPOSTO = manifestazione.NominativoManifestazione;
                return ret;
            }
            catch (Exception ex)
            {
                throw ex;
            }

        }

        public static async Task<Radioamatore> MapIncomingToDBRadioamatore(
            RadioamatoreDaGenerareDto radioamatore,
            bool isStandard,
            bool isContest,
            bool isManifestazione,
            bool isManifestazione44,
            IZonaMappingDal zonaMappingDal,
            IIspettoratoDal ispettoratoDal,
            string statoNominatovDaGenerare)
        {

            Radioamatore ret = new Radioamatore();


            ret.CAP = radioamatore.Cap;
            ret.CODICE_FISCALE = radioamatore.CodiceFiscale;
            if (isStandard)
            {
                ret.CODICE_ZONA_DENOMINAZIONE = radioamatore.Zona;
                ret.IS_ZONA_COMUNE_SPECIALE = radioamatore.IsComuneSpeciale.Value;                
            }
            else
            {
                ret.CODICE_ZONA_DENOMINAZIONE = "SCONOSCIUTA";
                ret.IS_ZONA_COMUNE_SPECIALE = false;
            }

            if (radioamatore.TipoAssegnatario.Trim().ToLower().Contains("144"))
            {
                ret.DENOMINAZIONE = radioamatore.Cognome;
            }
            else
            {
                ret.COGNOME = radioamatore.Cognome;
            }

            ret.NOME = radioamatore.Nome;
            ret.COMUNE = radioamatore.Comune.nome;
            ret.DATA_NASCITA = radioamatore.DataNascita;
            ret.INDIRIZZO = radioamatore.Indirizzo;
            // verificare                       

            ret.PROVINCIA = radioamatore.Provincia.sigla;
            ret.REGIONE = radioamatore.Regione.denominazione;
            ret.SIGLA_REGIONE = radioamatore.Regione.sigla;

            var anagraficheIspettorato = await ispettoratoDal.GetAnagraficaIspettoratiAsync();
            ret.ID_ANAGRAFICA_ISPETTORATO = anagraficheIspettorato
                                            .Where(isp => isp.DENOMINAZIONE.ToLower() == radioamatore.Ispettorato.Trim().ToLower())
                                            .Select(isp => isp.ID)
                                            .FirstOrDefault();

            ret.STATO_NOMINATIVO = statoNominatovDaGenerare;
            ret.TIPO_NOMINATIVO = isStandard ? "STANDARD" :
                                  ((isManifestazione || isManifestazione44) ? "MANIFESTAZIONE" :
                                  "CONTEST");

            // Set id_codice_zona
            CodiceZona codiceZona = await zonaMappingDal.GetCodiceZonaAsync(ret.CODICE_ZONA_DENOMINAZIONE);
            if (codiceZona == null)
            {
                throw new Exception($"Codice zona {ret.CODICE_ZONA_DENOMINAZIONE} presente nella domanda {ret.CODICE_DOMANDA_FE} non trovato sul DB radioamatori.");
            }
            ret.ID_CODICE_ZONA = codiceZona.ID_CODICE_ZONA;
            ret.CODICE_ZONA_STRING = codiceZona.CODICE;
            ret.TIPO_ZONA = codiceZona.TIPO;


            return ret;

        }
    }
}
