﻿using System;

namespace MicroSV_Radioamatori.BL.Common
{
    public static class ExtensionString
    {
        public static string ToUpperSafe(this string str)
        {
            if (string.IsNullOrWhiteSpace(str))
            {
                return string.Empty;
            }

            return str.ToUpper();
        }

        public static string DateTimeToStringFormatItSafe(this DateTime? date)
        {
            if (!date.HasValue) return null;
            return date.Value.ToString("dd/MM/yyyy hh:mm:ss");
        }
    }
}

