﻿using Newtonsoft.Json;
using System;

namespace MicroSV_Radioamatori.BL.DTOs
{
    public class ContestDto
    {
        [JsonProperty("idRadioamatore")]
        public int IdRadioamatore { get; set; }
        [JsonProperty("codiceContest")]
        public string CodiceContest { get; set; }
        [JsonProperty("dataInizio")]
        public DateTime DataInizio { get; set; }
        [JsonProperty("dataFine")]
        public DateTime DataFine { get; set; }
    }
}
