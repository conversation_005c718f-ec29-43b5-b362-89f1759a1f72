﻿using Invitalia.Misure.Standard2.Utilities.Extensions;
using MicroSV_Radioamatori.DAL.Entities;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;

namespace MicroSV_Radioamatori.BL.DTOs
{
    public class RadioamatoreSRDto
    {
        [JsonProperty("idRadioamatore")]
        public int IdRadioamatore { get; set; }

        [JsonProperty("suffisso")]
        public string Suffisso { get; set; }

        [JsonProperty("idAnagraficaIspettorato")]
        public string IdAnagraficaIspettorato { get; set; }

        [JsonProperty("codiceZona")]
        public string CodiceZona { get; set; }

        [JsonProperty("nome")]
        public string Nome { get; set; }

        [JsonProperty("cognome")]
        public string Cognome { get; set; }

        [JsonProperty("denominazione")]
        public string Denominazione { get; set; }

        [JsonProperty("dataDiNascita")]
        public string DataDiNascita { get; set; }

        [JsonProperty("codiceFiscale")]
        public string CodiceFiscale { get; set; }

        [JsonProperty("indirizzo")]
        public string Indirizzo { get; set; }

        [JsonProperty("regione")]
        public string Regione { get; set; }

        [JsonProperty("comune")]
        public string Comune { get; set; }

        [JsonProperty("provincia")]
        public string Provincia { get; set; }

        [JsonProperty("cap")]
        public string Cap { get; set; }

        [JsonProperty("codiceNominativo")]
        public string CodiceNominativo { get; set; }

        [JsonProperty("statoNominativo")]
        public string StatoNominativo { get; set; }

        [JsonProperty("dataRilascio")]
        public string DataRilascio { get; set; }

        [JsonProperty("utente")]
        public string Utente { get; set; }

        [JsonProperty("DataAggiornamento")]
        public string DataAggiornamento { get; set; }

        [JsonProperty("protocollo")]
        public string Protocollo { get; set; }

        [JsonProperty("dataProtocollo")]
        public string DataProtocollo { get; set; }

        [JsonProperty("nota")]
        public string Nota { get; set; }

        [JsonProperty("codiceDomandaFE")]
        public string CodiceDomandaFE { get; set; }

        [JsonProperty("DataCreazione")]
        public string DataCreazione { get; set; }

        [JsonProperty("ProtocolloPresentazioneFe")]
        public string ProtocolloPresentazioneFe { get; set; }

        [JsonProperty("DataProtocolloPresentazioneFe")]
        public string DataProtocolloPresentazioneFe { get; set; }

        [JsonProperty("codiceRegioneStazioneRipetitrice")]
        public string CodiceRegioneStazioneRipetitrice { get; set; }

        [JsonProperty("codiceProvinciaStazioneRipetitrice")]
        public string CodiceProvinciaStazioneRipetitrice { get; set; }

        [JsonProperty("numeroAutorizzazioneSr")]
        public string NumeroAutorizzazioneSr { get; set; }

        [JsonProperty("dataScadenza")]
        public string DataScadenza { get; set; }

        [JsonProperty("codiceDomanda")]
        public string CodiceDomanda { get; set; }

        [JsonProperty("pec")]
        public string Pec { get; set; }

        [JsonProperty("email")]
        public string Email { get; set; }

        [JsonProperty("telefono")]
        public string Telefono { get; set; }

        [JsonProperty("tipologiaAssegnatario")]
        public string TipologiaAssegnatario { get; set; }

        private bool? _isAssegnatarioMinorenne;
        public bool? IsAssegnatarioMinorenne
        {
            get
            {
                if (!string.IsNullOrEmpty(TipologiaAssegnatario) && !_isAssegnatarioMinorenne.HasValue)
                {
                    _isAssegnatarioMinorenne = TipologiaAssegnatario.EqualsIgnoreCaseAndTrim("Radioamatore minorenne");
                }

                return _isAssegnatarioMinorenne;
            }
        }

        private bool? _isAssegnatarioArt143;
        public bool? IsAssegnatarioArt143
        {
            get
            {
                if (!string.IsNullOrEmpty(TipologiaAssegnatario) && !_isAssegnatarioArt143.HasValue)
                {
                    _isAssegnatarioArt143 = !TipologiaAssegnatario.EqualsIgnoreCaseAndTrim("Radioamatore") && 
                                            !TipologiaAssegnatario.EqualsIgnoreCaseAndTrim("Radioamatore minorenne");
                }

                return _isAssegnatarioArt143;
            }
        }

        #region Navigation Porperty
        public IspettoratoDto AnagraficaIspettorato { get; set; }
        #endregion

    }
}
