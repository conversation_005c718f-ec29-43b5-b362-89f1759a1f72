﻿using MicroSV_Radioamatori.BL.IncomingDTOs;
using MicroSV_Radioamatori.DAL.Entities;
using System;

namespace MicroSV_Radioamatori.BL.DTOs
{
    public class PatenteFilterDto
    {
        public string Cognome { get; set; }
        public string Nome { get; set; }
        public string CodiceFiscale { get; set; }
        public DateTime? DataDiNascita { get; set; }
        public string RegioneResidenza { get; set; }
        public string ProvinciaResidenza { get; set; }
        public string IdIspettorato { get; set; }
        public string NumeroPatente { get; set; }
        public string IdStatoPatente { get; set; }
        public DateTime? DataRilascio { get; set; }
    }
}
