﻿using Newtonsoft.Json;

namespace MicroSV_Radioamatori.BL.DTOs
{
    public class DocumentoDto
    {
        [JsonProperty("tipoDocumento")]
        public string TipoDocumento { get; set; }

        [JsonProperty("fileBase64")]
        public string FileBase64 { get; set; }

        [JsonProperty("fileName")]
        public string FileName { get; set; }

        [JsonProperty("estensione")]
        public string Estensione { get; set; }
    }
}
