﻿using Newtonsoft.Json;
using System;

namespace MicroSV_Radioamatori.BL.DTOs
{
    public class AnagraficaContestContestDto
    {
        [JsonProperty("codice")]
        public string Codice { get; set; }
        [JsonProperty("denominazione")]
        public string Denominazione { get; set; }
        [JsonProperty("dataInizio")]
        public DateTime? DataInizio { get; set; }
        [JsonProperty("dataFine")]
        public DateTime? DataFine { get; set; }
    }
}
