﻿using Newtonsoft.Json;

namespace MicroSV_Radioamatori.BL.DTOs
{
    public class AllegatoBlobDownloadDto
    {
        [JsonProperty("id")]
        public string Id { get; set; }

        [JsonProperty("fileName")]
        public string FileName { get; set; }

        [JsonProperty("stringBase64")]
        public string StringBase64 { get; set; }

        [JsonProperty("extension")]
        public string Extension { get; set; }
    }
}
