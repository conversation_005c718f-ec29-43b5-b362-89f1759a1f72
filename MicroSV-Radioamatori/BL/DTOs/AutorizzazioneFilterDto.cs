﻿using Newtonsoft.Json;
using System;

namespace MicroSV_Radioamatori.BL.DTOs
{
    public class AutorizzazioneFilterDto : AutorizzazioneFilterDtoBase
    {
        [JsonProperty("idIspettorato")]
        public string IdIspettorato { get; set; }
    }


    public class AutorizzazioneFilterDtoBase
    {
        [JsonProperty("cognome")]
        public string Cognome { get; set; }

        [JsonProperty("nome")]
        public string Nome { get; set; }

        [JsonProperty("denominazione")]
        public string Denominazione { get; set; }

        [JsonProperty("codiceFiscale")]
        public string CodiceFiscale { get; set; }

        [JsonProperty("dataDiNascita")]
        public DateTime? DataDiNascita { get; set; }

        [JsonProperty("codiceRegione")]
        public string CodiceRegione { get; set; }

        [JsonProperty("regione")]
        public string Regione { get; set; }

        [JsonProperty("codiceProvincia")]
        public string CodiceProvincia { get; set; }

        [JsonProperty("provincia")]
        public string Provincia { get; set; }
        

        [JsonProperty("numeroAutorizzazione")]
        public string NumeroAutorizzazione { get; set; }

        [JsonProperty("idStatoAutorizzazione")]
        public int? IdStatoAutorizzazione { get; set; }

        [JsonProperty("dataRilascio")]
        public DateTime? DataRilascio { get; set; }

        [JsonProperty("nominativo")]
        public string Nominativo { get; set; }

        [JsonProperty("idTipologiaAutorizzazione")]
        public int? IdTipologiaAutorizzazione { get; set; }

        [JsonProperty("idRadioamatore")]
        public int? IdRadioamatore { get; set; }
    }
}
