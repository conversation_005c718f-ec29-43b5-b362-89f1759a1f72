﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace MicroSV_Radioamatori.BL.DTOs
{
    public class EsitoValidazioneDto
    {
        public string CodiceNominativo { get; set; }
        public string StatoNominativo { get; set; }
        public bool Successo { get; set; }
        public bool ProseguiSuFEAbilitato { get; set; }
        public bool ErroreGeA { get; set; }
        public List<string> Errori { get; set; } = new();
    }
}
