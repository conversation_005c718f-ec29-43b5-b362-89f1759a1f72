﻿using Newtonsoft.Json;
using System.Collections.Generic;

namespace MicroSV_Radioamatori.BL.DTOs
{
    public class ProtocolloUscitaDto
    {
        [JsonProperty("codiceProtocolloInUscita")]
        public string CodiceProtocolloInUscita { get; set; }

        [JsonProperty("dataProtocolloInUscita")]
        public string DataProtocolloInUscita { get; set; }

        [JsonProperty("utente")]
        public string Utente { get; set; }

        [JsonProperty("codiceDomandaFE")]
        public string CodiceDomandaFE { get; set; }

        [JsonProperty("documenti")]
        public List<DocumentoDto> Documenti { get; set; }

        #region RAD - Nominativo

        [JsonProperty("codiceNominativo")]
        public string CodiceNominativo { get; set; }

        [JsonProperty("dataRilascioNominativo")]
        public string DataRilascioNominativo { get; set; }

        #endregion

        #region RAD2 - Patenti

        [JsonProperty("codicePatente")]
        public string CodicePatente { get; set; }

        [JsonProperty("dataRilascioPatente")]
        public string DataRilascioPatente { get; set; }

        #endregion

        #region RAD3/RAD4 - Autorizzazioni

        [JsonProperty("idAutorizzazione")]
        public int? IdAutorizzazione { get; set; }

        [JsonProperty("dataRilascioAutorizzazione")]
        public string DataRilascioAutorizzazione { get; set; }

        [JsonProperty("codiceFiscaleTitolarePatente")]
        public string CodiceFiscaleTitolarePatente { get; set; }

        #endregion

        #region RAD5 - Iscrizioni elenco SWL

        [JsonProperty("idIscrizioneSWL")]
        public int? IdIscrizioneSWL { get; set; }

        [JsonProperty("dataRilascioIscrizioneSWL")]
        public string DataRilascioIscrizioneSWL { get; set; }

        #endregion
    }
}
