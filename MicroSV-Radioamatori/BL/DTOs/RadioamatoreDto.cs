﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;

namespace MicroSV_Radioamatori.BL.DTOs
{
    public class RadioamatoreDto
    {
        [JsonProperty("idRadioamatore")]
        public int IdRadioamatore { get; set; }        
        
        [JsonProperty("suffisso")]
        public string Suffisso { get; set; }
        
        [J<PERSON><PERSON>roperty("idAnagraficaIspettorato")]
        public string IdAnagraficaIspettorato { get; set; }
        
        [JsonProperty("codiceZona")]
        public string CodiceZona { get; set; } 
        
        [JsonProperty("nome")]
        public string Nome { get; set; }
        
        [JsonProperty("cognome")]
        public string Cognome { get; set; }
        
        [JsonProperty("denominazione")]
        public string Denominazione { get; set; }
        
        [JsonProperty("dataNascita")]
        public string DataDiNascita { get; set; }
        
        [JsonProperty("codiceFiscale")]
        public string CodiceFiscale { get; set; }
        
        [JsonProperty("indirizzo")]
        public string Indirizzo { get; set; }
        
        [JsonProperty("regione")]
        public string Regione { get; set; }
        
        [JsonProperty("comune")]
        public string Comune { get; set; }
        
        [JsonProperty("provincia")]
        public string Provincia { get; set; }
        [JsonProperty("cap")]
        public string Cap { get; set; }
        [JsonProperty("codiceNominativo")]
        public string CodiceNominativo { get; set; }
        [JsonProperty("tipoNominativo")]
        public string TipoNominativo { get; set; }
        [JsonProperty("statoNominativo")]
        public string StatoNominativo { get; set; }
        [JsonProperty("dataRilascio")]
        public string DataRilascio { get; set; }
        [JsonProperty("utente")]
        public string Utente { get; set; }
        [JsonProperty("DataAggiornamento")]
        public string DataAggiornamento { get; set; }
        [JsonProperty("protocollo")]
        public string Protocollo { get; set; }
        [JsonProperty("dataProtocollo")]
        public string DataProtocollo { get; set; }
        [JsonProperty("nota")]
        public string Nota { get; set; }
        [JsonProperty("codiceDomandaFE")]
        public string CodiceDomandaFE { get; set; }
        [JsonProperty("DataCreazione")]
        public string DataCreazione { get; set; }
        [JsonProperty("ProtocolloPresentazioneFe")]
        public string ProtocolloPresentazioneFe { get; set; }
        [JsonProperty("DataProtocolloPresentazioneFe")]
        public string DataProtocolloPresentazioneFe { get; set; }

        [JsonProperty("dataScadenza")]
        public string DataScadenza { get; set; }

        #region Naigation Porperty
        public List<ContestDto> ElencoContest { get; set; }
        public ManifestazioneDto Manifestazione { get; set; }
        #endregion

    }
}
