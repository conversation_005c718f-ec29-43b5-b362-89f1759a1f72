﻿using Newtonsoft.Json;

namespace MicroSV_Radioamatori.BL.DTOs
{
    public class StazioneRipetitriceDto
    {
        [JsonProperty("id")]
        public int? Id { get; set; }
        
        [JsonProperty("regione")]
        public string Regione { get; set; }
        
        [Json<PERSON>roperty("codiceRegione")]
        public string CodiceRegione { get; set; }
        
        [Json<PERSON>roperty("provincia")]
        public string Provincia { get; set; }
        
        [JsonProperty("codiceProvincia")]
        public string CodiceProvincia{ get; set; }
        
        [JsonProperty("comune")]
        public string Comune { get; set; }
        
        [JsonProperty("codiceComune")]
        public string CodiceComune { get; set; }
        
        [JsonProperty("indirizzo")]
        public string Indirizzo { get; set; }
        
        [JsonProperty("civico")]
        public string Civico { get; set; }
        
        [JsonProperty("cap")]
        public string Cap { get; set; }
        
        [Json<PERSON>roperty("idIspettorato")]
        public string IdIspettorato { get; set; }
        
        [JsonProperty("latitudine")]
        public string Latitudine { get; set; }
        
        [Json<PERSON>roperty("longitudine")]
        public string <PERSON><PERSON>udine { get; set; }
        
        [JsonProperty("nomeOperatore")]
        public string NomeOperatore { get; set; }
        
        [JsonProperty("cognomeOperatore")]
        public string CognomeOperatore { get; set; }
        
        [JsonProperty("codiceFiscaleOperatore")]
        public string CodiceFiscaleOperatore { get; set; }
        
        [JsonProperty("nominativoOperatore")]
        public string NominativoOperatore { get; set; }
        
        [JsonProperty("regioneResidenzaOperatore")]
        public string RegioneResidenzaOperatore { get; set; }
        
        [JsonProperty("codiceRegioneResidenzaOperatore")]
        public string CodiceRegioneResidenzaOperatore { get; set; }
        
        [JsonProperty("provinciaResidenzaOperatore")]
        public string ProvinciaResidenzaOperatore { get; set; }
        
        [JsonProperty("codiceProvinciaResidenzaOperatore")]
        public string CodiceProvinciaResidenzaOperatore { get; set; }
        
        [JsonProperty("comuneResidenzaOperatore")]
        public string ComuneResidenzaOperatore { get; set; }
        
        [JsonProperty("codiceComuneResidenzaOperatore")]
        public string CodiceComuneResidenzaOperatore { get; set; }
        
        [JsonProperty("indirizzoResidenzaOperatore")]
        public string IndirizzoResidenzaOperatore { get; set; }
        
        [JsonProperty("numeroCivicoResidenzaOperatore")]
        public string NumeroCivicoResidenzaOperatore { get; set; }
        
        [JsonProperty("capResidenzaOperatore")]
        public string CapResidenzaOperatore { get; set; }
        
        [JsonProperty("emailOperatore")]
        public string EmailOperatore { get; set; }
        
        [JsonProperty("telefonoOperatore")]
        public string TelefonoOperatore { get; set; }

        #region Navigation properties
        [JsonProperty("ispettorato")]
        public IspettoratoDto Ispettorato { get; set; }
        #endregion
    }
}
