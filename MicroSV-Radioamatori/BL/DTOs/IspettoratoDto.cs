﻿using Newtonsoft.Json;
using System;

namespace MicroSV_Radioamatori.BL.DTOs
{
    public class IspettoratoDto
    {
        [JsonProperty("idIspettorato")]
        public string IdIspettorato { get; set; }

        [JsonProperty("idSubentro")]
        public string Id<PERSON><PERSON><PERSON> { get; set; }

        [Json<PERSON>roperty("denominazioneIspettorato")]
        public string DenominazioneIspettorato { get; set; }

        [JsonProperty("siglaRegione")]
        public string SiglaRegione { get; set; }

        [JsonProperty("iban")]
        public string Iban { get; set; }

        [JsonProperty("ccPostale")]
        public string CcPostale { get; set; }

        [JsonProperty("dataDismissione")]
        public DateTime? DataDismissione { get; set; }

    }
}
