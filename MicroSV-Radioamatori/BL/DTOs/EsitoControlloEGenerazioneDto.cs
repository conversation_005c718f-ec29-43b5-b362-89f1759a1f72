﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace MicroSV_Radioamatori.BL.DTOs
{
    public class EsitoControlloEGenerazioneDto
    {
        public bool SuccessoControlli { get; set; }
        public bool ProseguiSuFEAbilitato { get; set; }
        public List<string> ErroriControlli { get; set; } = new();
        public bool SuccessoGenerazione { get; set; }
        public string ErroreGenerazione { get; set; }
        public CodiceNominativoDto NominativoGenerato { get; set; }
    }
}
