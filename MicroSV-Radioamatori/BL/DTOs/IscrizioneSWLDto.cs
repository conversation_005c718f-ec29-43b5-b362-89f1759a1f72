﻿using Newtonsoft.Json;

namespace MicroSV_Radioamatori.BL.DTOs
{
    public class IscrizioneSWLDto
    {
        [JsonProperty("id")]
        public int? Id { get; set; }

        [JsonProperty("codiceDomanda")]
        public string CodiceDomanda{ get; set; }

        [JsonProperty("cognome")]
        public string Cognome { get; set; }

        [JsonProperty("nome")]
        public string Nome { get; set; }

        [JsonProperty("codiceFiscale")]
        public string CodiceFiscale { get; set; }

        [JsonProperty("sesso")]
        public string Sesso { get; set; }

        [JsonProperty("luogoNascita")]
        public string Luogo<PERSON>ascita { get; set; }

        [JsonProperty("codiceLuogoNascita")]
        public string CodiceLuogoNascita { get; set; }

        [JsonProperty("dataNascita")]
        public string DataNascita { get; set; }

        [JsonProperty("regioneResidenza")]
        public string RegioneResidenza { get; set; }

        [JsonProperty("codiceRegioneResidenza")]
        public string CodiceRegioneResidenza { get; set; }

        [JsonProperty("provinciaResidenza")]
        public string ProvinciaResidenza { get; set; }

        [JsonProperty("codiceProvinciaResidenza")]
        public string CodiceProvinciaResidenza { get; set; }

        [JsonProperty("comuneResidenza")]
        public string ComuneResidenza { get; set; }

        [JsonProperty("codiceComuneResidenza")]
        public string CodiceComuneResidenza { get; set; }

        [JsonProperty("indirizzoResidenza")]
        public string IndirizzoResidenza { get; set; }

        [JsonProperty("capResidenza")]
        public string CapResidenza { get; set; }

        [JsonProperty("idAnagraficaIspettorato")]
        public string IdAnagraficaIspettorato { get; set; }

        [JsonProperty("pec")]
        public string Pec { get; set; }

        [JsonProperty("email")]
        public string Email { get; set; }

        [JsonProperty("telefono")]
        public string Telefono { get; set; }

        [JsonProperty("tipologiaRichiedente")]
        public string TipologiaRichiedente { get; set; }

        [JsonProperty("numeroIscrizioneSWL")]
        public string NumeroIscrizioneSWL { get; set; }

        [JsonProperty("idStatoIscrizioneSWL")]
        public int? IdStatoIscrizioneSWL { get; set; }

        [JsonProperty("dataRilascio")]
        public string DataRilascio { get; set; }

        [JsonProperty("riferimentoDocumentoIscrizione_SWL")]
        public string RiferimentoDocumentoIscrizione_SWL { get; set; }

        [JsonProperty("codiceProtocolloEntrata")]
        public string CodiceProtocolloEntrata { get; set; }

        [JsonProperty("dataProtocolloEntrata")]
        public string DataProtocolloEntrata { get; set; }

        [JsonProperty("codiceProtocolloUscita")]
        public string CodiceProtocolloUscita { get; set; }

        [JsonProperty("dataProtocolloUscita")]
        public string DataProtocolloUscita { get; set; }

        [JsonProperty("utente")]
        public string Utente { get; set; }


        #region Navigation Properties

        public IspettoratoDto Ispettorato { get; set; }
        public StatoIscrizioneSWLDto StatoIscrizioneSWL { get; set; }

        #endregion
    }
}
