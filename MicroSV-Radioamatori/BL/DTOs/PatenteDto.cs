﻿using MicroSV_Radioamatori.DAL.Entities;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;

namespace MicroSV_Radioamatori.BL.DTOs
{
    public class PatenteDto
    {
        [JsonProperty("id")]
        public int Id { get; set; }

        [JsonProperty("codicePatente")]
        public string CodicePatente { get; set; }

        [JsonProperty("cognome")]
        public string Cognome { get; set; }

        [JsonProperty("nome")]
        public string Nome { get; set; }
        
        [JsonProperty("codiceFiscale")]
        public string CodiceFiscale { get; set; }
        
        [JsonProperty("sesso")]
        public string Sesso { get; set; }
        
        [JsonProperty("luogoDiNascita")]
        public string Luogo<PERSON>i<PERSON>ascita { get; set; }

        [JsonProperty("codiceLuogoDiNascita")]
        public string CodiceLuogoDiNascita { get; set; }

        [JsonProperty("dataDiNascita")]
        public string DataDiNascita { get; set; }
        
        [JsonProperty("regioneResidenza")]
        public string RegioneResidenza { get; set; }
        [JsonProperty("codiceRegioneResidenza")]
        public string CodiceRegioneResidenza { get; set; }

        [JsonProperty("provinciaResidenza")]
        public string ProvinciaResidenza { get; set; }
        
        [JsonProperty("codiceProvinciaResidenza")]
        public string CodiceProvinciaResidenza { get; set; }

        [JsonProperty("comuneResidenza")]
        public string ComuneResidenza { get; set; }

        [JsonProperty("codiceComuneResidenza")]
        public string CodiceComuneResidenza { get; set; }

        [JsonProperty("indirizzoResidenza")]
        public string IndirizzoResidenza { get; set; }
        
        [JsonProperty("capResidenza")]
        public string CapResidenza { get; set; }
        
        [JsonProperty("pec")]
        public string Pec { get; set; }
        
        [JsonProperty("email")]
        public string Email { get; set; }
        
        [JsonProperty("telefono")]
        public string Telefono { get; set; }
        
        [JsonProperty("riferimentoDocumentoPatente")]
        public string RiferimentoDocumentoPatente { get; set; }

        [JsonProperty("riferimentoDocumentoHarec")]
        public string RiferimentoDocumentoHarec { get; set; }

        [JsonProperty("dataRilascio")]
        public string DataRilascio { get; set; }

        [JsonProperty("codiceProtocolloInEntrata")]
        public string CodiceProtocolloInEntrata { get; set; }

        [JsonProperty("dataProtocolloInEntrata")]
        public string DataProtocolloInEntrata { get; set; }

        [JsonProperty("codiceProtocolloInUscita")]
        public string CodiceProtocolloInUscita { get; set; }

        [JsonProperty("dataProtocolloInUscita")]
        public string DataProtocolloInUscita { get; set; }

        [JsonProperty("codiceDomanda")]
        public string CodiceDomanda { get; set; }

        [JsonProperty("numeroPatenteCartacea")]
        public string NumeroPatenteCartacea { get; set; }

        [JsonProperty("utente")]
        public string Utente { get; set; }

        [JsonProperty("idStatoPatente")]
        public int IdStatoPatente { get; set; }

        [JsonProperty("idIspettorato")]
        public string IdIspettorato { get; set; }

        [JsonProperty("documenti")]
        public List<DocumentoDto> Documenti { get; set; }

        #region Navigation Properties

        public IspettoratoDto Ispettorato { get; set; }
        public StatoPatenteDto StatoPatente { get; set; }

        #endregion
    }
}
