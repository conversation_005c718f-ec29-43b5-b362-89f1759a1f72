﻿namespace MicroSV_Radioamatori.BL.DTOs
{
    public class ParametriRicercaRadioamatoreDto : ParametriRicercaRadioamatoreDtoBase
    {
        public string IdIspettorato { get; set; }
    }

    public class ParametriRicercaRadioamatoreDtoBase
    {
        public string Nome { get; set; }
        public string Cognome { get; set; }
        public string CodiceFiscale { get; set; }
        public string CodiceProtocollo { get; set; }
        public string DataNascita { get; set; }
        public string DataRilascio { get; set; }
        public string Nominativo { get; set; }
        public string Provincia { get; set; }
        public string Regione { get; set; }
        public string StatoNominativo { get; set; }
        public string TipologiaNominativo { get; set; }
        public string DataScadenza { get; set; }
    }
}
