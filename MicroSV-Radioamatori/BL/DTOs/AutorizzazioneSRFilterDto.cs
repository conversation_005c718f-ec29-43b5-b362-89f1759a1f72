﻿using Newtonsoft.Json;
using System;

namespace MicroSV_Radioamatori.BL.DTOs
{
    public class AutorizzazioneSRFilterDto : AutorizzazioneSRFilterDtoBase
    {
        [JsonProperty("idIspettorato")]
        public string IdIspettorato { get; set; }
    }

    public class AutorizzazioneSRFilterDtoBase
    {
        [JsonProperty("cognome")]
        public string Cognome { get; set; }

        [JsonProperty("nome")]
        public string Nome { get; set; }

        [JsonProperty("denominazione")]
        public string Denominazione { get; set; }

        [JsonProperty("codiceFiscale")]
        public string CodiceFiscale { get; set; }

        [JsonProperty("dataDiNascita")]
        public DateTime? DataDiNascita { get; set; }

        [JsonProperty("codiceRegione")]
        public string CodiceRegione { get; set; }        

        [JsonProperty("numeroAutorizzazione")]
        public string NumeroAutorizzazione { get; set; }

        [JsonProperty("idStatoAutorizzazione")]
        public int? IdStatoAutorizzazione { get; set; }

        [JsonProperty("dataRilascio")]
        public DateTime? DataRilascio { get; set; }

        [JsonProperty("nominativoOperatore")]
        public string NominativoOperatore { get; set; }

        [JsonProperty("idTipologiaAutorizzazione")]
        public int? IdTipologiaAutorizzazione { get; set; }

        [JsonProperty("statoAutorizzazione")]
        public string StatoAutorizzazione { get; set; }

        [JsonProperty("nominativoStazione")]
        public string NominativoStazione { get; set; }

        [JsonProperty("codiceRegioneStazione")]
        public string CodiceRegioneStazione { get; set; }

        [JsonProperty("codiceProvinciaStazione")]
        public string CodiceProvinciaStazione { get; set; }

        [JsonProperty("codiceComuneStazione")]
        public string CodiceComuneStazione { get; set; }

        [JsonProperty("idRadioamatore")]
        public int? IdRadioamatore { get; set; }

        [JsonProperty("idAutorizzazioneSR")]
        public int? IdAutorizzazioneSR { get; set; }
    }
}
