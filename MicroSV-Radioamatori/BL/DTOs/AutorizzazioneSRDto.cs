﻿using MicroSV_Radioamatori.DAL.Entities;
using Newtonsoft.Json;
using System;

namespace MicroSV_Radioamatori.BL.DTOs
{
    public class AutorizzazioneSRDto
    {
        [JsonProperty("id")]
        public int? Id { get; set; }

        [JsonProperty("codiceDomanda")]
        public string CodiceDomanda { get; set; }

        [JsonProperty("cognome")]
        public string Cognome { get; set; }
        
        [JsonProperty("nome")]
        public string Nome { get; set; }
        
        [JsonProperty("denominazione")]
        public string Denominazione { get; set; }
        
        [JsonProperty("codiceFiscale")]
        public string CodiceFiscale { get; set; }
        
        [JsonProperty("sesso")]
        public string Sesso { get; set; }
        
        [JsonProperty("luogoDiNascita")]
        public string LuogoDiNascita { get; set; }
        
        [JsonProperty("codiceLuogoDiNascita")]
        public string CodiceLuogoDiNascita { get; set; }
        
        [JsonProperty("dataDiNascita")]
        public DateTime? DataDiNascita { get; set; }
        
        [JsonProperty("regioneResidenza")]
        public string RegioneResidenza { get; set; }
        
        [JsonProperty("codiceRegioneResidenza")]
        public string CodiceRegioneResidenza { get; set; }
        
        [JsonProperty("provinciaResidenza")]
        public string ProvinciaResidenza { get; set; }
        
        [JsonProperty("codiceProvinciaResidenza")]
        public string CodiceProvinciaResidenza { get; set; }
        
        [JsonProperty("comuneResidenza")]
        public string ComuneResidenza { get; set; }
        
        [JsonProperty("codiceComuneResidenza")]
        public string CodiceComuneResidenza { get; set; }
        
        [JsonProperty("indirizzoResidenza")]
        public string IndirizzoResidenza { get; set; }
        
        [JsonProperty("capResidenza")]
        public string CapResidenza { get; set; }
        
        [JsonProperty("idIspettorato")]
        public string IdIspettorato { get; set; }
        
        [JsonProperty("pec")]
        public string Pec { get; set; }
        
        [JsonProperty("email")]
        public string Email { get; set; }
        
        [JsonProperty("telefono")]
        public string Telefono { get; set; }
        
        [JsonProperty("idTipologiaAssegnatario")]
        public int? IdTipologiaAssegnatario { get; set; }
        
        [JsonProperty("rinnovo")]
        public bool? Rinnovo { get; set; }
        
        [JsonProperty("idAutorizzazioneRinnovata")]
        public int? IdAutorizzazioneRinnovata { get; set; }
        
        [JsonProperty("numeroAutorizzazione")]
        public string NumeroAutorizzazione { get; set; }
        
        [JsonProperty("idTipologiaAutorizzazione")]
        public int? IdTipologiaAutorizzazione { get; set; }
        
        [JsonProperty("idStatoAutorizzazione")]
        public int? IdStatoAutorizzazione { get; set; }
        
        [JsonProperty("dataRilascio")]
        public DateTime? DataRilascio { get; set; }
        
        [JsonProperty("dataScadenza")]
        public DateTime? DataScadenza { get; set; }
        
        [JsonProperty("idRadioamatoreSR")]
        public int? IdRadioamatoreSR { get; set; }
        
        [JsonProperty("codiceProtocolloInEntrata")]
        public string CodiceProtocolloInEntrata { get; set; }
        
        [JsonProperty("dataProtocolloInEntrata")]
        public string DataProtocolloInEntrata { get; set; }
        
        [JsonProperty("codiceProtocolloInUscita")]
        public string CodiceProtocolloInUscita { get; set; }
        
        [JsonProperty("dataProtocolloInUscita")]
        public string DataProtocolloInUscita { get; set; }
        
        [JsonProperty("utente")]
        public string Utente { get; set; }
        
        [JsonProperty("idStazioneRipetitrice")]
        public int? IdStazioneRipetitrice { get; set; }

        #region Navigation properties

        [JsonProperty("tipologiaAssegnatario")]
        public TipoAssegnatarioDto TipologiaAssegnatario { get; set; }
        
        [JsonProperty("tipologiaAutorizzazione")]
        public TipoAutorizzazioneDto TipologiaAutorizzazione { get; set; }
        
        [JsonProperty("statoAutorizzazione")]
        public StatoAutorizzazioneDto StatoAutorizzazione { get; set; }
        
        [JsonProperty("anagraficaIspettorato")]
        public IspettoratoDto AnagraficaIspettorato { get; set; }
        
        [JsonProperty("stazioneRipetritrice")]
        public StazioneRipetitriceDto StazioneRipetritrice { get; set; }
        
        [JsonProperty("nominativoStazioneRipetritrice")]
        public RadioamatoreSRDto NominativoStazioneRipetritrice { get; set; }

        #endregion
    }
}
