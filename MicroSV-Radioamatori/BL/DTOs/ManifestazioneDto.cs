﻿using Newtonsoft.Json;
using System;

namespace MicroSV_Radioamatori.BL.DTOs
{
    public class ManifestazioneDto
    {
        [JsonProperty("Id")]
        public int? Id { get; set; }
        [JsonProperty("idRadioamatore")]
        public int IdRadioamatore { get; set; }
        [JsonProperty("idAnagraficaIspettorato")]
        public string IdAnagraficaIspettorato { get; set; }
        [JsonProperty("idTipoManifestazione")]
        public int IdTipoManifestazione { get; set; }
        [JsonProperty("denominazione")]
        public string Denominazione { get; set; }
        [JsonProperty("siglaRegione")]
        public string SiglaRegione { get; set; }
        [JsonProperty("dataInizio")]
        public DateTime DataInizio { get; set; }
        [JsonProperty("dataFine")]
        public DateTime DataFine { get; set; }
    }
}
