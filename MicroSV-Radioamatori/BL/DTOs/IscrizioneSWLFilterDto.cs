﻿using System;

namespace MicroSV_Radioamatori.BL.DTOs
{
    public class IscrizioneSWLFilterDto : IscrizioneSWLFilterDtoBase
    {       
        public string IdIspettorato { get; set; }
    }

    public class IscrizioneSWLFilterDtoBase
    {
        public string Cognome { get; set; }
        public string Nome { get; set; }
        public string CodiceFiscale { get; set; }
        public string RegioneResidenza { get; set; }
        public string ProvinciaResidenza { get; set; }
        public string NumeroIscrizioneSWL { get; set; }
        public string IdStatoIscrizioneSWL { get; set; }
        public DateTime? DataRilascio { get; set; }
    }
}
