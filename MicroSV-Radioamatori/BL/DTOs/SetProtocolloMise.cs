﻿using Newtonsoft.Json;


namespace MicroSV_Radioamatori.BL.DTOs
{
    public class SetProtocolloMise
    {
        [JsonProperty("codiceDomandaFE")]
        public string CodiceDomandaFE { get; set; }
        [JsonProperty("codiceProtocollo")]
        public string CodiceProtocollo { get; set; }
        [JsonProperty("dataProtocollo")]
        public string DataProtocollo { get; set; }
        [JsonProperty("numeroIscrizioneSWL")]
        public string NumeroIscrizioneSWL { get; set; }
    }
}
