﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;

namespace MicroSV_Radioamatori.BL.DTOs
{
    public class RadioamatoreSRFilterDto : RadioamatoreSRFilterDtoBase
    {
      
        [JsonProperty("idIspettorato")]
        public string IdIspettorato { get; set; }       

    }

    public class RadioamatoreSRFilterDtoBase
    {
        [JsonProperty("cognome")]
        public string Cognome { get; set; }

        [JsonProperty("nome")]
        public string Nome { get; set; }

        [JsonProperty("denominazione")]
        public string Denominazione { get; set; }

        [JsonProperty("codiceFiscale")]
        public string CodiceFiscale { get; set; }

        [JsonProperty("dataDiNascita")]
        public DateTime? DataDiNascita { get; set; }

        [JsonProperty("regione")]
        public string Regione { get; set; }

        [JsonProperty("provincia")]
        public string Provincia { get; set; }

        [JsonProperty("statiNominativo")]
        public List<string> StatiNominativo { get; set; }

        [JsonProperty("dataRilascio")]
        public DateTime? DataRilascio { get; set; }

        [JsonProperty("nominativo")]
        public string Nominativo { get; set; }

    }
}
