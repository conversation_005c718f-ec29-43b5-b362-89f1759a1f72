﻿using Invitalia.Misure.Standard2.Utilities.DTO;
using Newtonsoft.Json;

namespace MicroSV_Radioamatori.BL.DTOs
{
    public class BoolResultRadioamatoreDto : BoolResultDTO
    {
        [JsonProperty("statoNominativo")]
        public string statoNominativo { get; set; }

        [JsonProperty("codiceNominativo")]
        public string codiceNominativo { get; set; }
        [JsonProperty("codiceFiscale")]
        public string codiceFiscale { get; set; }

        [JsonProperty("codiceDomandaFE")]
        public string codiceDomandaFE { get; set; }

    }
}
