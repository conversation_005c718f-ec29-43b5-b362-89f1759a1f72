﻿using System.Text.RegularExpressions;
using System;

namespace MicroSV_Radioamatori.BL.Utilities
{
    public static class UtilitiesUploadDocument
    {
        public static string PulisciNomeFile(this string nomeFile)
        {
            if (string.IsNullOrEmpty(nomeFile)) return string.Empty;

            string outFile = "";
            var ind = nomeFile.LastIndexOf(".");
            if (ind != -1)
            {
                string prefix = EliminaCaratteriSpuri(nomeFile.Substring(0, ind)).Trim();
                string est = nomeFile.Substring(ind).Trim();
                outFile = prefix + est;
            }
            else
                outFile = EliminaCaratteriSpuri(nomeFile).Trim();
            Regex rgx = new Regex(@"\.+");
            outFile = rgx.Replace(outFile, ".");
            return outFile;
        }

        public static string EliminaCaratteriSpuri(this string nomeFile)
        {
            if (string.IsNullOrEmpty(nomeFile)) return string.Empty;

            string chrStr = "#%&*:<>?/{|}~";
            string outStr = nomeFile.Trim();
            for (Int32 i = 0; i <= chrStr.Length - 1; i++)
                outStr = outStr.Replace(chrStr[i].ToString(), string.Empty);
            // *
            // *** Elimina i caratteri "spuri" finali
            // *
            while (outStr.Substring(outStr.Length - 1, 1) == "." || outStr.Substring(outStr.Length - 1, 1) == " " || outStr.Substring(outStr.Length - 1, 1) == "-" || outStr.Substring(outStr.Length - 1, 1) == "_")
                outStr = outStr.Substring(0, outStr.Length - 1);
            return outStr;
        }

        public static bool ExistsAndInvariantEqualsTo(this string stringValue, string compareItem)
        {
            if (string.IsNullOrWhiteSpace(stringValue)) return false;

            return stringValue.Equals(compareItem, StringComparison.InvariantCultureIgnoreCase);
        }
    }
}
