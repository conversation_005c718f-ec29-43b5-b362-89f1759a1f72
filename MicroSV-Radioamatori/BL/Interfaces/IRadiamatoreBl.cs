﻿using MicroSV_Radioamatori.BL.DTOs;
using MicroSV_Radioamatori.BL.Enums;
using MicroSV_Radioamatori.BL.IncomingDTOs;
using MicroSV_Radioamatori.DAL.Entities;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace MicroSV_Radioamatori.BL.Interfaces
{
    public interface IRadiamatoreBl
    {
        Task<List<RadioamatoreDto>> GetRadioamatoriByCodiceFiscaleAsync(string codiceFiscale, bool filtraStati = true);
        Task<BoolResultRadioamatoreDto> GetRadioamatoriByCodiceFiscaleResultBoolAsync(string codiceFiscale);
        Task<BoolResultRadioamatoreDto> GetRadioamatoriByCodiceNominativoResultBoolAsync(string codiceNominativo, string nome, string cognome, string datanascita);
        Task<List<RadioamatoreDto>> GetRadioamatoriByCodiceNominantivoAsync(string codiceNominativo);
        Task<RadioamatoreDto> GetRadioamatoreByIdAsync(int idRadioamatore);
        Task<RadioamatoreDto> GetRadioamatoreByCodiceDomandaFEAsync(string codiceDomandaFE);
        Task<List<RadioamatoreDto>> GetRadioamatoriByAnagraficaAsync(string nome, string cognome, string datanascita);
        Task<List<RadioamatoreDto>> GetRadioamatoriByParametriAsync(ParametriRicercaRadioamatore parametri);
        Task<List<RadioamatoreDto>> GetRadioamatoriByIspettoratoConGestioneSubentriAsync(ParametriRicercaRadioamatoreBase parametri, string idIspettorato);
        Task<List<RadioamatoreDto>> GetRadioamatoriAsync();
        Task<List<RadioamatoreDto>> GetRadioamatoriByCodiceContestAsync(string codiceContest);
        Task AnnullaCodiceNominativoAsync(string codiceDomandaFE);
        Task AnnullaCodiceNominativoByIdAsync(int idRadioamatore);
        Task<BoolResultRadioamatoreDto> GetRadioamatoriByAnagraficaResultBoolAsync(string nome, string cognome, string datanascita);
        Task<List<IspettoratoDto>> GetAnagraficaIspettoratiAsync(bool? visualizzaAncheDismessi=null);
        Task<IspettoratoDto> GetIspettoratoBySiglaRegioneAsync(string siglaRegione);
        Task<List<ZonaDto>> GetZonaAsync(string siglaRegione, string siglaProvincia, string codiceIstatComune);
        Task<List<AnagraficaContestContestDto>> GetAnagraficaContestAsync();
        Task<List<AnagraficaContestContestDto>> GetContestbyRadioamatoreAsync(string idRadioamatore);
        Task<List<TipoManifestazioneDTO>> GetTipologieManifestazioni();
        Task<List<ManifestazioneDto>> GetManifestazioniByRadioamatoriAsync(int idradioamatore);
        Task<List<PrefissiSpecialiDto>> GetPrefissiNominativiSpecialiAsync();
        Task<EsitoValidazioneDto> ControllaNominativoPerGenerazioneAsync(RadioamatoreDaGenerareDto radioamatoreDaGenerare, bool isStandard, bool isContest, bool isManifestazione, bool isManifestazioneArt44);
        Task<EsitoValidazioneDto> ControllaNominativoPerIstruttoriaAsync(RadioamatoreDaGenerareDto radioamatoreDaGenerare, bool isStandard, bool isContest, bool isManifestazione, bool isManifestazioneArt44);
        Task UpdateCodiceFiscaleDataRilascio(int idRadioamatore, CodiceFiscaleDataRilascio codiceFiscaleProtocolloDataRilascio);
        Task UpdateDataRilascioAsync(int idRadioamatore, CodiceFiscaleDataRilascio codiceFiscaleProtocolloDataRilascio);
        Task UpdateCodiceFiscaleAsync(int idRadioamatore, CodiceFiscaleDataRilascio codiceFiscaleProtocolloDataRilascio);
        Task<CodiceNominativoDto> GeneraNominativoStandardAsync(string tipoApplicazione, JObject domanda);
        Task<CodiceNominativoDto> GeneraNominativoContestAsync(string tipoApplicazione, JObject domanda);
        Task<CodiceNominativoDto> GeneraNominativoManifestazioneAsync(string tipoApplicazione, JObject domanda);
        Task<CodiceNominativoDto> GeneraNominativoAssociazioneAsync(string tipoApplicazione, JObject domanda);
        Task<CodiceNominativoDto> GeneraNominativoGeaAsync(string tipoApplicazione, RadioamatoreDaGenerareDto inputRad, bool isStandard, bool isContest, bool isManifestazione, bool isManifestazione44);
        Task SetProtocolloMisePresentazioneAsync(string codiceDomandaFE, DateTime dataProtocollo, string numeroProtocollo);
        Task<List<ScadenzaNominativoDto>> SetScadenzaNominativiAsync(bool isReadOnly);
        Task<List<RegioneDto>> GetRegioniAsync();
        Task<string> GetCodiceRegioneByRegione(string regione, string provincia);
        Task<int> InserisciNominativoStandardAsync(RadioamatoreDaGenerareDto radioamatoreDaGenerare);
        Task<int> SetProtocolloUscitaNominativoAsync(ProtocolloUscitaDto protocolloUscitaDto);

        //Patenti
        Task<List<PatenteDto>> GetPatentiByFilterAsync(PatenteFilterDto filtro);
        Task<List<PatenteDto>> GetPatentiByIspettoratoConGestioneSubentriAsync(PatenteFilterDto filtro);
        Task<string> InsertPatenteAsync(string idRisorsa, PatenteDto patente, RequestOrigin requestOrigin);
        Task<int> UpdatePatenteAsync(PatenteDto patente);
        Task<string> GestisciCambioStatoPatenteAsync(PatenteDto patenteDto, Enums.StatoPatente statoPatente);
        Task<PatenteDto> GetPatenteByIdAsync(int idPatente);
        Task<PatenteDto> GetPatenteByCodiceAsync(string codicePatente);
        Task<List<PatenteDto>> GetPatenteByNumeroCartaceaAsync(string numeroPatenteCartacea);
        Task<List<PatenteDto>> GetPatentiByCfAsync(string codiceFiscale);
        Task<bool> ExistsPatenteByCodiceAsync(string codicePatente);
        Task<int> SetProtocolloPatenteAsync(string codiceDomandaFE, DateTime dataProtocollo, string protocollo);
        Task<string> SetProtocolloUscitaPatenteAsync(string idRisorsa, ProtocolloUscitaDto protocolloUscitaDto);
        Task<PatenteDto> UploadDocumentoPatenteAsync(string idRisorsa, PatenteDto patenteDto);

        //Autorizzazioni
        Task<AutorizzazioneDto> GetAutorizzazioneByIdAsync(int idAutorizzazione);
        Task<AutorizzazioneDto> GetAutorizzazioneByNumeroAsync(string numeroAutorizzazone);
        Task<List<AutorizzazioneDto>> GetAutorizzazioneByFilterAsync(AutorizzazioneFilterDto filtro);
        Task<List<AutorizzazioneDto>> GetAutorizzazioniByCodiceFiscaleAsync(string codiceFiscale);
        Task<List<AutorizzazioneDto>> GetAutorizzazioniByIspettoratoConGestioneSubentriAsync(AutorizzazioneFilterDtoBase filtro, string idSpettorato);
        Task<bool> ExistsAutorizzazioneByNumeroAsync(string numeroAutorizzazone);
        Task<ResponseInsertAutorizzazioneDto> InsertAutorizzazioneAsync(string idRisorsa, AutorizzazioneDto autorizzazioneDto, RequestOrigin requestOrigin);
        Task<int> GestisciCambioStatoAutorizzazioneAsync(AutorizzazioneDto autorizzazioneDto, EnumStatoAutorizzazione statoAutorizzazione);
        Task<int> SetProtocolloAutorizzazioneAsync(string codiceDomandaFE, DateTime dataProtocollo, string protocollo);
        Task<int> SetProtocolloUscitaAutorizzazioneAsync(string idRisorsa, ProtocolloUscitaDto protocolloUscitaDto);
        Task<List<ScadenzaAutorizzazioneDto>> SetScadenzaAutorizzazioniAsync(bool isReadOnly);
        Task<int> UpdateAutorizzazioneAsync(AutorizzazioneDto autorizzazione);
        Task<int> UpdateVisibilitaAttestatoAsync(int idAutorizzazione, bool isAttestatoVisibileFE);
        Task<int> UpdateIdRadioamatoreAsync(int idRadioamatore, int idAutorizzazione);
        Task<int> UploadDocumentoAutorizzazioneAsync(string idRisorsa, AutorizzazioneDto autorizzazioneDto);

        //Autorizzazioni SR
        Task<ResponseInsertAutorizzazioneDto> InsertAutorizzazioneSRCompletaAsync(AutorizzazioneSRCompletaDto autorizzazioneCompleta, Operation operation, RequestOrigin origin);
        Task<RadioamatoreSR> GeneraNominativoSR(RadioamatoreSRDto dto, string codiceRegione, bool generaNominativo = true);
        Task<int> InsertStazioneRipetitriceAsync(StazioneRipetitriceDto dto);
        Task<ResponseInsertAutorizzazioneDto> InsertAutorizzazioneSRAsync(AutorizzazioneSRDto dto);
        Task<ResponseInsertAutorizzazioneDto> InsertAutorizzazioneSRAndStazioneAsync(AutorizzazioneSRDto dto);
        Task<List<AutorizzazioneSRDto>> GetAutorizzazioneSRByFilterAsync(AutorizzazioneSRFilterDto filtro);
        Task<List<AutorizzazioneSRDto>> GetAutorizzazioniSRByIspettoratoConGestioneSubentriAsync(AutorizzazioneSRFilterDtoBase filtro, string idSpettorato);
        Task<List<string>> GetNominativiAutorizzazioniSRInScadenzaByCodiceFiscale(string codiceFiscale);
        Task<int> SetProtocolloAutorizzazioneSRAsync(string codiceDomandaFE, DateTime dataProtocollo, string protocollo);
        Task<List<ScadenzaAutorizzazioneSRDto>> SetScadenzaAutorizzazioniSRAsync(bool isReadOnly);
        Task<List<RadioamatoreSRDto>> GetRadioamatoriAndAutorizzazioniSRByFilterAsync(RadioamatoreSRFilterDto filtro);
        Task<List<RadioamatoreSRDto>> GetRadiomatoriSRByFilter(RadioamatoreSRFilterDto filtro);
        Task<List<RadioamatoreSRDto>> GetRadiomatoriSRByIspettoratoFilter(RadioamatoreSRFilterDtoBase filtro, string idIspettorato);
        Task<List<ScadenzaNominativoSRDto>> SetScadenzaNominativiSRAsync(bool isReadOnly);
        Task<AutorizzazioneSRDto> GetAutorizzazioneSRByIdAsync(int idAutorizzazione);
        Task<int> GestisciCambioStatoAutorizzazioneSRAsync(AutorizzazioneSRDto autorizzazioneSrDto, EnumStatoAutorizzazione statoAutorizzazione);
        Task<int> GestisciCambioStatoNominativoSRAsync(RadioamatoreSRDto radioamatoreSrDto, EnumStatoNominativo statoNominativo);
        Task<RadioamatoreSRDto> GetRadioamatoreSRByIdAsync(int idRadioamatore);
        Task<int> UpdateIdRadioamatoreSRAsync(int idRadioamatore, int idAutorizzazione);
        Task<int> SetProtocolloUscitaAutorizzazioneSRAsync(ProtocolloUscitaDto protocolloUscitaDto);
        Task<SuffissoSRDto> GetSuffissoByPrefissoAndSuffissoAsync(string prefisso, string suffisso);

        //Allegati
        Task<AllegatoBlobDownloadDto> DownloadAllegatoByBlobName(string codiceMisura, string blobName);

        //Iscrizioni SWL
        Task<int> InsertIscrizioneSWLAsync(string idRisorsa, IscrizioneSWLDto iscrizione, RequestOrigin requestOrigin);
        Task<int> GestisciCambioStatoIscrizioneSWLAsync(IscrizioneSWLDto iscrizioneDto, Enums.EnumStatoIscrizioneSWL statoIscrizione);
        Task<int> SetProtocolloUscitaIscrizioneSWLAsync(string idRisorsa, ProtocolloUscitaDto protocolloUscitaDto);
        Task<IscrizioneSWLDto> GetIscrizioneSWLByIdAsync(int idIscrizioneSWL);
        Task<List<IscrizioneSWLDto>> GetIscrizioniSWLByFilterAsync(IscrizioneSWLFilterDto filtro);
        Task<List<IscrizioneSWLDto>> GetIscrizioniSWLByIspettoratoConGestioneSubentriAsync(IscrizioneSWLFilterDtoBase filtro, string idIspettorato);
        Task<int> SetProtocolloIscrizioneSWLAsync(string codiceDomandaFE, DateTime dataProtocollo, string protocollo, string numeroIscrizioneSWL);
    }
}
