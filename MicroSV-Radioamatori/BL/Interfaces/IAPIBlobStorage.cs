﻿using Invitalia.Misure.FiltriAPI.Constants;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json.Linq;
using Refit;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace MicroSV_Radioamatori.BL.Interfaces
{
    public interface IAPIBlobStorage
    {
        [Get("/api/blobstorage/documents/blobname/{blobName}")]
        Task<JObject> GetAllegatoByBlobName(
                                    [Header(HttpHeaderParamsName.HEADER_PARAM_CODICE_MISURA)] string codiceMisura,
                                    string blobName);

        [Post("/api/blobstorage/documents")]
        Task<JObject> SalvaAllegato(
                                    [Header(HttpHeaderParamsName.HEADER_PARAM_ID_RISORSA)] string idRisorsa,
                                    [Header(HttpHeaderParamsName.HEADER_PARAM_TIPO_APPLICAZIONE)] string fase,
                                    [Header(HttpHeaderParamsName.HEADER_PARAM_TIPO_SOGGETTO)] string sottoFase,
                                    [Header(HttpHeaderParamsName.HEADER_PARAM_CODICE_MISURA)] string codiceMisura,
                                    [Body] IDictionary<string, string> inputDto);

        [Delete("/api/blobstorage/documents/blobname/{blobName}")]
        Task<JObject> DeleteAllegatoByBlobName(
                                    [Header(HttpHeaderParamsName.HEADER_PARAM_CODICE_MISURA)] string codiceMisura,
                                    string blobName);
    }
}
