﻿using Newtonsoft.Json.Linq;
using System;

namespace MicroSV_Radioamatori.BL.Extensions
{
    public static class NewtonsoftExtensions
    {
        public static T GetValueIgnoreCase<T>(this JToken jToken, string propertyName)
        {
            return jToken.ToObject<JObject>().GetValueIgnoreCase<T>(propertyName);
        }

        public static T GetValueIgnoreCase<T>(this JObject jObject, string propertyName)
        {
            var value = jObject?.GetValue(propertyName, StringComparison.OrdinalIgnoreCase);
            return value != null ? value.Value<T>() : default(T);
        }

    }
}
