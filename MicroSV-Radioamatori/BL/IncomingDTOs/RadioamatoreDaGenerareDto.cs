using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace MicroSV_Radioamatori.BL.IncomingDTOs
{
    public class RadioamatoreDaGenerareDto
    {
        // standard
        public string Nome { get; set; }
        public string Cognome { get; set; }
        public string TipoAssegnatario { get; set; }
        public string CodiceFiscale { get; set; }
        public Regione Regione { get; set; }
        public Provincia Provincia { get; set; }
        public Comune Comune { get; set; }
        public bool? IsComuneSpeciale { get; set; }
        public string Indirizzo { get; set; }
        public string Cap { get; set; }
        public string Pec { get; set; }
        public string Email { get; set; }
        public string Telefono { get; set; }
        public string Ispettorato { get; set; }
        public string Zona { get; set; }
        public string CodiceProtocollo { get; set; }
        public DateTime? DataProtocollo { get; set; }
        public bool? IsGenerazioneNominativo { get; set; }
        public string CodiceNominativo { get; set; }

        // manifestazione
        public DateTime? DataNascita { get; set; }
        public DateTime? DataRilascio { get; set; }
        public string NominativoDiChiamata { get; set; }
        public string SezioneAssociazione { get; set; }
        public ManifestazioneDaGenerareDto Manifestazione { get; set; }

        // contest
        public ContestDaGenerareDto Contest { get; set; }
    }

    public class ManifestazioneDaGenerareDto
    {
        public DateTime? DataInizio { get; set; }
        public DateTime? DataFine { get; set; }
        public string Denominazione { get; set; }
        public string TipoManifestazione { get; set; }
        public Regione RegioneUbicazione { get; set; }
        public bool IsBasilicata { get; set; }
        public string SiglaProvinciaBasilicata { get; set; }
        public string NominativoManifestazione { get; set; }
        public string IspettoratoManifestazione { get; set; }
    }

    public class ContestDaGenerareDto
    {
        public List<ContestItemDaGenerareDto> ListaContest { get; set; }
        public string NominativoContest { get; set; }

    }

    public class ContestItemDaGenerareDto
    {
        public string Denominazione { get; set; }
        public DateTime? DataInizio { get; set; }
        public DateTime? DataFine { get; set; }
        public string Codice { get; set; }
    }

    public class Provincia
    {
        public string nome { get; set; }
        public string sigla { get; set; }
        public string codice { get; set; }
        public string regione { get; set; }
    }

    public class Coordinate
    {
        public double lat { get; set; }
        public double lng { get; set; }
    }

    public class Comune
    {
        public string codice { get; set; }
        public string nome { get; set; }
        public string nomeStraniero { get; set; }
        public string codiceCatastale { get; set; }
        public string cap { get; set; }
        public string prefisso { get; set; }
        public Provincia provincia { get; set; }
        public string email { get; set; }
        public string pec { get; set; }
        public string telefono { get; set; }
        public string fax { get; set; }
        public Coordinate coordinate { get; set; }
    }


    public class Regione
    {
        public string sigla { get; set; }
        public string denominazione { get; set; }
    }

}