﻿using System;

namespace MicroSV_Radioamatori.BL.IncomingDTOs
{
    public class ParametriRicercaRadioamatoreBase
    {
        public string Nome { get; set; }
        public string Cognome { get; set; }
        public string CodiceFiscale { get; set; }
        public string CodiceProtocollo { get; set; }
        public DateTime? DataNascita { get; set; }
        public DateTime? DataRilascio { get; set; }  
        public string Nominativo { get; set; }
        public string Provincia { get; set; }
        public string Regione { get; set; }
        public string StatoNominativo { get; set; }
        public string TipologiaNominativo { get; set; }
        public DateTime? DataScadenza { get; set; }
    }

    public class ParametriRicercaRadioamatore : ParametriRicercaRadioamatoreBase
    {
        public string Ispettorato { get; set; }
    }
}
