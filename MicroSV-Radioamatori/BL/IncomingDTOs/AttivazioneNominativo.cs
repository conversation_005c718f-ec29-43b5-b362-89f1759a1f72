﻿using System;

namespace MicroSV_Radioamatori.BL.IncomingDTOs
{
    public class AttivazioneNominativo
    {
        public StatoNominativo StatoNominativo { get; set; }
        public DateTime? DataRilascio { get; set; }
        public string NumeroProtocolloGea { get; set; }
        public DateTime? DataProtocolloGea { get; set; }
    }

    public enum StatoNominativo
    {
        INVARIATO = 0,
        GENERATO = 1,
        ATTIVO = 2
    }
}
