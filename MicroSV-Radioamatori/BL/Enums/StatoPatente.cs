﻿using System.ComponentModel;

namespace MicroSV_Radioamatori.BL.Enums
{
    public enum StatoPatente
    {
        [Description("Richiesta ammissione")]
        RichiestaAmmissione = 1,
        [Description("Richiesta rigettata")]
        <PERSON><PERSON>Rigettata,
        [Description("Richiesta accolta")]
        <PERSON>staAccolta,
        [Description("Esame non passato")]
        EsameNonPassato,
        [Description("Attiva")]
        Attiva,
        [Description("Cancellata")]
        Cancellata
    }
}
