﻿using System.ComponentModel;

namespace MicroSV_Radioamatori.BL.Enums
{
    public enum TipologiaAssegnatarioAutorizzazioneSR
    {
        [Description("Radioamatore")]
        Radioamatore = 1,
        [Description("Radioamatore minorenne")]
        RadioamatoreMinorenne = 2,
        [Description("Associazioni o sezioni delle associazioni dei radioamatori legalmente costituite di cui all’art. 143, comma 1, lettera d) del CdC")]
        AssociazioneArt243 = 3
    }
}
