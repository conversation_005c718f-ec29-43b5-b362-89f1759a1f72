﻿using System.ComponentModel;

namespace MicroSV_Radioamatori.BL.Enums
{
    public enum TipologiaAssegnatarioAutorizzazione
    {
        [Description("Radioamatore")]
        Radioamatore = 1,
        [Description("Radioamatore minorenne")]
        RadioamatoreMinorenne = 2,
        [Description("Università ed Enti di ricerca scientifica e tecnologica di cui all’art. 144, comma 1, lettera a) del CdC")]
        UniversitaEnti = 3,
        [Description("Scuole ed istituti di istruzione di ogni ordine e grado, statali e legalmente riconosciuti, ad eccezione delle scuole elementari di cui all’art. 144, comma 1, lettera b) del CdC")]
        Scuole = 4,
        [Description("Scuole e corsi di istruzione militare di cui all’art. 144, comma 1, lettera c) del CdC")]
        ScuoleMilitari = 5,
        [Description("Associazioni o sezioni delle associazioni dei radioamatori legalmente costituite di cui all’art. 144, comma 1, lettera d) del CdC")]
        AssociazioniArticolo144 = 6,
        [Description("Associazioni o sezioni delle associazioni dei radioamatori legalmente costituite di cui all’art. 143, comma 1, del CdC")]
        AssociazioniArticolo143 = 7
    }
}
