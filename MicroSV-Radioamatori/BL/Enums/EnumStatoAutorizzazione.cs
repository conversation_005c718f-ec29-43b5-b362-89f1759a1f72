﻿using System.ComponentModel;

namespace MicroSV_Radioamatori.BL.Enums
{
    public enum EnumStatoAutorizzazione
    {
        [Description("In istruttoria")]
        InIstruttoria = 1,
        [Description("Attiva")]
        Attiva,
        [Description("In scadenza")]
        InScadenza,
        [Description("Scaduta")]
        <PERSON>aduta,
        [Description("Rinnovo - In Istruttoria")]
        Rinnovo,
        [Description("Cancellata")]
        Cancellata,
        [Description("Rinunciata")]
        <PERSON><PERSON>unciata,
        [Description("Disattivata")]
        Disattivata
    }
}