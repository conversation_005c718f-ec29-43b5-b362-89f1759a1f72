﻿using System.ComponentModel;


namespace MicroSV_Radioamatori.BL.ValueObject
{
    public enum EnumStatiRadioamatori
    {
        [Description("ANNULLATO")]
        ANNULLATO = 0,
        [Description("ARCHIVIATA")]
        ARCHIVIATA = 1,
        [Description("ATTIVO")]
        ATTIVO = 2,
        [Description("GENERATO")]
        GENERATO = 3,
        [Description("IN_ISTRUTTORIA")]
        IN_ISTRUTTORIA = 4,
        [Description("SCADUTO")]
        SCADUTO = 5
    }
}
