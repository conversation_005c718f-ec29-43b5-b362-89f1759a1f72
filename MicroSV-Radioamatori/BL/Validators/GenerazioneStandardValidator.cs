﻿using FluentValidation;
using MicroSV_Radioamatori.BL.IncomingDTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace MicroSV_Radioamatori.BL.Validators
{
    public class GenerazioneStandardValidator : AbstractValidator<RadioamatoreDaGenerareDto>
    {
        
        public GenerazioneStandardValidator(RadioamatoreDaGenerareDto radioamatore)
        {

            When(c => c.TipoAssegnatario.ToLower().Contains("minorenne"), () =>
            {
                RuleFor(c => c.DataNascita)
                    .NotEmpty().WithMessage($"{nameof(RadioamatoreDaGenerareDto.DataNascita)} non può essere vuota")
                    .Must(BeBetween18and16YearsAgo).WithMessage($"{nameof(RadioamatoreDaGenerareDto.DataNascita)} deve essere compresa tra 16 e 18 anni fa");
                    

            });


        }
        protected bool BeBetween18and16YearsAgo(DateTime? value)
        {
            return DateTime.Now.AddYears(-18) < value && DateTime.Now.AddYears(-16) >= value;
        }

    }
    
}
