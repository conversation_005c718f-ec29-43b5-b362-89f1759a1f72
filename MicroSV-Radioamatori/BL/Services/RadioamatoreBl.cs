﻿using AutoMapper;
using Inv.Fwk.Helper.DateTimeZone;
using Invitalia.Misure.FiltriAPI;
using Invitalia.Misure.Standard2.Utilities.Extensions;
using Invitalia.Misure.Utilities.WebApi.Refit.XRM;
using Microsoft.Extensions.Configuration;
using MicroSV_Radioamatore.BL.Mapping;
using MicroSV_Radioamatori.BL.Common;
using MicroSV_Radioamatori.BL.DTOs;
using MicroSV_Radioamatori.BL.Enums;
using MicroSV_Radioamatori.BL.Extensions;
using MicroSV_Radioamatori.BL.IncomingDTOs;
using MicroSV_Radioamatori.BL.Interfaces;
using MicroSV_Radioamatori.BL.Mapping;
using MicroSV_Radioamatori.BL.Utilities;
using MicroSV_Radioamatori.BL.ValueObject;
using MicroSV_Radioamatori.DAL.Entities;
using MicroSV_Radioamatori.DAL.Interfaces;
using Newtonsoft.Json.Linq;
using Polly;
using Refit;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Threading.Tasks;

namespace MicroSV_Radioamatori.BL.Services
{
    public class RadioamatoreBl : IRadiamatoreBl
    {
        private readonly IRadioamatoriDal _radioamatoreDal;
        private readonly IIspettoratoDal _ispettoratoDal;
        private readonly IZonaMappingDal _zonaMappingDal;
        private readonly IContestDal _contestDal;
        private readonly IManifestazioneDal _tipoManifestazioneDal;
        private readonly ISuffissiDal _suffissiDal;
        private readonly IPrefissiSpecialiDal _prefissiSpecialiDal;
        private readonly IRegioneDal _regioneDal;
        private readonly IPatenteDal _patenteDal;
        private readonly IAutorizzazioneDal _autorizzazioneDal;
        private readonly IAutorizzazioneSRDal _autorizzazioneSRDal;
        private readonly IIscrizioniSWLDal _iscrizioneSWLDal;
        private readonly IConfiguration _conf;
        private readonly ApiContext _apiContext;
        private readonly IMapper _mapper;
        protected readonly HttpClientFactory _httpClientFactory;
        protected readonly int NUMBER_OF_RETRIES;
        protected readonly double TIMEOUT_SEC;

        private const string CODICE_MISURA_RAD3 = "RAD3";
        private const string CODICE_MISURA_RAD5 = "RAD5";

        private const string TIPO_APPLICAZIONE_RDA = "RDA";
        private const string TIPO_APPLICAZIONE_RDP = "RDP";
        private const string TIPO_APPLICAZIONE_RDI = "RDI";

        private const string POST_PRESENTAZIONE = "PostPresentazione";
        private static object LOCK = new object();

        public RadioamatoreBl(
            IRadioamatoriDal radioamatoreDal,
            IIspettoratoDal ispettoratoDal,
            IZonaMappingDal zonaMappingDal,
            IContestDal contestDal,
            IManifestazioneDal tipoManifestazioneDal,
            ISuffissiDal suffissiDal,
            IPrefissiSpecialiDal prefissiSpecialiDal,
            IRegioneDal regioneDal,
            IPatenteDal patenteDal,
            IAutorizzazioneDal autorizzazioneDal,
            IAutorizzazioneSRDal autorizzazioneSRDal,
            IIscrizioniSWLDal iscrizioniSWLDal,
            IConfiguration conf,
            HttpClientFactory httpClient,
            IMapper mapper,
            ApiContext apiContext)
        {
            this._radioamatoreDal = radioamatoreDal;
            this._ispettoratoDal = ispettoratoDal;
            this._zonaMappingDal = zonaMappingDal;
            this._contestDal = contestDal;
            this._tipoManifestazioneDal = tipoManifestazioneDal;
            this._suffissiDal = suffissiDal;
            this._prefissiSpecialiDal = prefissiSpecialiDal;
            this._regioneDal = regioneDal;
            this._patenteDal = patenteDal;
            this._autorizzazioneDal = autorizzazioneDal;
            this._autorizzazioneSRDal = autorizzazioneSRDal;
            this._iscrizioneSWLDal = iscrizioniSWLDal;
            this._conf = conf;
            this._mapper = mapper;
            this._apiContext = apiContext;
            this._httpClientFactory = httpClient;

            NUMBER_OF_RETRIES = _httpClientFactory.GetNumberOfRetries(conf);
            TIMEOUT_SEC = _httpClientFactory.GetTimeout(conf);
        }

        #region Nominativi

        public async Task<List<RadioamatoreDto>> GetRadioamatoriByCodiceFiscaleAsync(string codiceFiscale, bool filtraStati = true)
        {
            var entities = await this._radioamatoreDal.GetRadioamatoriByCodiceFiscaleAsync(codiceFiscale, filtraStati);
            var elencoContest = await this._contestDal.GetContesByListRadioamatoritAsync(entities.Select(x => x.ID_RADIOAMATORE).ToList());

            var dto = this._mapper.Map<List<RadioamatoreDto>>(entities);
            dto.ForEach(x =>
            {
                x.ElencoContest = this._mapper.Map<List<ContestDto>>(elencoContest.Where(c => c.ID_RADIOAMATORE == x.IdRadioamatore).ToList());
            });

            return dto;
        }

        public async Task<BoolResultRadioamatoreDto> GetRadioamatoriByCodiceFiscaleResultBoolAsync(string codiceFiscale)
        {
            var modelloCF = await _radioamatoreDal.GetRadioamatoriByCodiceFiscaleAsync(codiceFiscale);
            if (modelloCF != null && modelloCF.Count > 0)
            {
                return new BoolResultRadioamatoreDto()
                {
                    Result = true,
                    statoNominativo = modelloCF.FirstOrDefault().STATO_NOMINATIVO,
                    codiceNominativo = modelloCF.FirstOrDefault().CODICE_NOMINATIVO,
                    codiceFiscale = modelloCF.FirstOrDefault().CODICE_FISCALE,
                    codiceDomandaFE = modelloCF.FirstOrDefault().CODICE_DOMANDA_FE
                };
            }
            else
            {
                return new BoolResultRadioamatoreDto()
                {
                    Result = false,
                    Errors = new List<string>()
                    {
                        "Nessun Codice fiscale trovato"
                    }
                };
            }
        }

        public async Task<BoolResultRadioamatoreDto> GetRadioamatoriByAnagraficaResultBoolAsync(string nome, string cognome, string datanascita)
        {
            var modelloCF = await this._radioamatoreDal.GetRadioamatoriByAnagraficaAsync(nome, cognome, datanascita);
            if (modelloCF != null && modelloCF.Count > 0)
            {
                return new BoolResultRadioamatoreDto()
                {
                    Result = true,
                    Errors = new List<string>()
                    {
                        "Nominativo Esistente"
                    },
                    statoNominativo = modelloCF.FirstOrDefault().STATO_NOMINATIVO,
                    codiceNominativo = modelloCF.FirstOrDefault().CODICE_NOMINATIVO,
                    codiceFiscale = modelloCF.FirstOrDefault().CODICE_FISCALE,
                    codiceDomandaFE = modelloCF.FirstOrDefault().CODICE_DOMANDA_FE
                };
            }
            else
            {
                return new BoolResultRadioamatoreDto()
                {
                    Result = false,
                    Errors = new List<string>()
                    {
                        "Nessun Nominativo trovato"
                    }
                };
            }

        }

        public async Task<BoolResultRadioamatoreDto> GetRadioamatoriByCodiceNominativoResultBoolAsync(string codicenominativo, string nome, string cognome, string datanascita)
        {
            var modelloCN = await this._radioamatoreDal.GetRadioamatoriByCodiceNominantivoAsync(codicenominativo);
            if (modelloCN != null && modelloCN.Count > 0)
            {
                var risultato = modelloCN.Where(c => c.NOME.Equals(nome, StringComparison.InvariantCultureIgnoreCase) && c.COGNOME.Equals(cognome, StringComparison.InvariantCultureIgnoreCase) && c.DATA_NASCITA.Value.ToString("dd/MM/yyyy").Equals(datanascita, StringComparison.InvariantCultureIgnoreCase));

                if (risultato != null && risultato.Any())
                {
                    return new BoolResultRadioamatoreDto()
                    {
                        Result = true
                    };
                }
                else
                {
                    return new BoolResultRadioamatoreDto()
                    {
                        Result = false,
                        Errors = new List<string>()
                        {
                            "Nessuna corrispondenza con nome, cognome e data di nascita"
                        }
                    };
                }
            }
            else
            {
                return new BoolResultRadioamatoreDto()
                {
                    Result = false,
                    Errors = new List<string>()
                    {
                        "Nessun Codice nominativo trovato"
                    }
                };
            }
        }

        public async Task<RadioamatoreDto> GetRadioamatoreByIdAsync(int idRadioamatore)
        {
            return this._mapper.Map<RadioamatoreDto>((await this._radioamatoreDal.GetRadioamatoreByIdAsync(idRadioamatore)));
        }

        public async Task<RadioamatoreDto> GetRadioamatoreByCodiceDomandaFEAsync(string codiceDomandaFE)
        {
            return this._mapper.Map<RadioamatoreDto>((await this._radioamatoreDal.GetRadioamatoreByCodiceDomandaFEAsync(codiceDomandaFE)));
        }

        public async Task<List<RadioamatoreDto>> GetRadioamatoriByCodiceNominantivoAsync(string codiceNominativo)
        {
            return this._mapper.Map<List<RadioamatoreDto>>((await this._radioamatoreDal.GetRadioamatoriByCodiceNominantivoAsync(codiceNominativo)));
        }

        public async Task<List<RadioamatoreDto>> GetRadioamatoriByAnagraficaAsync(string nome, string cognome, string datanascita)
        {
            return this._mapper.Map<List<RadioamatoreDto>>((await this._radioamatoreDal.GetRadioamatoriByAnagraficaAsync(nome, cognome, datanascita)));
        }

        public async Task<List<RadioamatoreDto>> GetRadioamatoriAsync()
        {
            return this._mapper.Map<List<RadioamatoreDto>>((await this._radioamatoreDal.GetRadioamatoriAsync()));
        }

        public async Task<List<RadioamatoreDto>> GetRadioamatoriByParametriAsync(ParametriRicercaRadioamatore parametri)
        {
            var dto = new ParametriRicercaRadioamatoreDto()
            {
                CodiceProtocollo = parametri.CodiceProtocollo.ToUpperSafe(),
                Cognome = parametri.Cognome.ToUpperSafe(),
                CodiceFiscale = parametri.CodiceFiscale.ToUpperSafe(),
                DataNascita = parametri.DataNascita.HasValue ? parametri.DataNascita.Value.ToString("yyyy-MM-dd") : "",
                DataRilascio = parametri.DataRilascio.HasValue ? parametri.DataRilascio.Value.ToString("yyyy-MM-dd") : "",
                Nome = parametri.Nome.ToUpperSafe(),
                Nominativo = parametri.Nominativo.ToUpperSafe(),
                Provincia = parametri.Provincia.ToUpperSafe(),
                Regione = parametri.Regione.ToUpperSafe(),
                StatoNominativo = parametri.StatoNominativo.ToUpperSafe(),
                TipologiaNominativo = parametri.TipologiaNominativo.ToUpperSafe(),
                IdIspettorato = parametri.Ispettorato.ToUpperSafe(),
                DataScadenza = parametri.DataScadenza.HasValue ? parametri.DataScadenza.Value.ToString("yyyy-MM-dd") : "",
            };
            var list = (await this._radioamatoreDal.GetRadioamatoriByParametriAsync(dto));
            var ret = this._mapper.Map<List<RadioamatoreDto>>(list);
            return ret;
        }

        //Task<List<RadioamatoreDto>> GetRadioamatoriByIspettoratoAsync(ParametriRicercaRadioamatore parametri, string idIspettorato);

        public async Task<List<RadioamatoreDto>> GetRadioamatoriByIspettoratoConGestioneSubentriAsync(ParametriRicercaRadioamatoreBase parametri, string idIspettorato)
        {
            var dto = new ParametriRicercaRadioamatoreDto()
            {
                CodiceProtocollo = parametri.CodiceProtocollo.ToUpperSafe(),
                Cognome = parametri.Cognome.ToUpperSafe(),
                CodiceFiscale = parametri.CodiceFiscale.ToUpperSafe(),
                DataNascita = parametri.DataNascita.HasValue ? parametri.DataNascita.Value.ToString("yyyy-MM-dd") : "",
                DataRilascio = parametri.DataRilascio.HasValue ? parametri.DataRilascio.Value.ToString("yyyy-MM-dd") : "",
                Nome = parametri.Nome.ToUpperSafe(),
                Nominativo = parametri.Nominativo.ToUpperSafe(),
                Provincia = parametri.Provincia.ToUpperSafe(),
                Regione = parametri.Regione.ToUpperSafe(),
                StatoNominativo = parametri.StatoNominativo.ToUpperSafe(),
                TipologiaNominativo = parametri.TipologiaNominativo.ToUpperSafe(),
                IdIspettorato = idIspettorato,
                DataScadenza = parametri.DataScadenza.HasValue ? parametri.DataScadenza.Value.ToString("yyyy-MM-dd") : "",
            };
            var list = (await this._radioamatoreDal.GetRadioamatoriByIspettoratoConGestioneSubentriAsync(dto));
            var ret = this._mapper.Map<List<RadioamatoreDto>>(list);
            return ret;
        }

        public async Task UpdateCodiceFiscaleDataRilascio(int idRadioamatore, CodiceFiscaleDataRilascio codiceFiscaleProtocolloDataRilascio)
        {
            string codiceFiscale = codiceFiscaleProtocolloDataRilascio.CodiceFiscale.ToUpper();
            var dataRilascio = codiceFiscaleProtocolloDataRilascio.DataRilascio;
            await this._radioamatoreDal.UpdateCodiceFiscaleCodiceProtocolloDataRilascioAsync(idRadioamatore, codiceFiscale, dataRilascio);

        }

        public async Task UpdateDataRilascioAsync(int idRadioamatore, CodiceFiscaleDataRilascio codiceFiscaleProtocolloDataRilascio)
        {
            await this._radioamatoreDal.UpdateDataRilascioAsync(idRadioamatore, codiceFiscaleProtocolloDataRilascio.DataRilascio);
        }

        public async Task UpdateCodiceFiscaleAsync(int idRadioamatore, CodiceFiscaleDataRilascio codiceFiscaleProtocolloDataRilascio)
        {
            await this._radioamatoreDal.UpdateCodiceFiscaleAsync(idRadioamatore, codiceFiscaleProtocolloDataRilascio.CodiceFiscale);
        }

        public async Task AnnullaCodiceNominativoAsync(string codiceDomandaFE)
        {
            await _radioamatoreDal.AnnullaNominativoAsync(codiceDomandaFE);
        }

        public async Task AnnullaCodiceNominativoByIdAsync(int idRadioamatore)
        {
            await _radioamatoreDal.AnnullaNominativoByIdAsync(idRadioamatore);
        }

        public async Task SetProtocolloMisePresentazioneAsync(string codiceDomandaFE, DateTime dataProtocollo, string protocollo)
        {
            await _radioamatoreDal.SetProtocolloMisePresentazioneAsync(codiceDomandaFE, dataProtocollo, protocollo);
        }

        public async Task<List<IspettoratoDto>> GetAnagraficaIspettoratiAsync(bool? visualizzaAncheDismessi = null)
        {
            return this._mapper.Map<List<IspettoratoDto>>((await this._ispettoratoDal.GetAnagraficaIspettoratiAsync(visualizzaAncheDismessi)));
        }

        public async Task<List<RegioneDto>> GetRegioniAsync()
        {
            return this._mapper.Map<List<RegioneDto>>((await this._regioneDal.GetRegioniAsync()));
        }

        public async Task<IspettoratoDto> GetIspettoratoBySiglaRegioneAsync(string siglaRegione)
        {
            return this._mapper.Map<IspettoratoDto>((await this._ispettoratoDal.GetIspettoratoBySiglaRegioneAsync(siglaRegione)));
        }

        public async Task<List<PrefissiSpecialiDto>> GetPrefissiNominativiSpecialiAsync()
        {
            return this._mapper.Map<List<PrefissiSpecialiDto>>(await this._prefissiSpecialiDal.GetPrefissiNominativiSpecialiAsync());
        }

        public async Task<List<ZonaDto>> GetZonaAsync(string siglaRegione, string siglaProvincia, string codiceIstatComune)
        {
            var mapping = await this._zonaMappingDal.GetMappingAsync(siglaRegione, siglaProvincia, codiceIstatComune);

            if (mapping is null)
            {
                mapping = await this._zonaMappingDal.GetMappingAsync(siglaRegione, siglaProvincia, "ALL");
            }

            var zone = new List<ZonaDto>();

            if (mapping is not null)
            {
                if (mapping.COMUNE_SPECIALE)
                {
                    zone.Add(new ZonaDto { Denominazione = mapping.Regione.DENOMINAZIONE });
                    zone.Add(new ZonaDto { Denominazione = mapping.ZONA });
                }
                else
                {
                    zone.Add(new ZonaDto { Denominazione = mapping.ZONA });
                }

                foreach (var zona in zone)
                {
                    CodiceZona codiceZona = _zonaMappingDal.GetCodiceZonaAsync(zona.Denominazione).GetAwaiter().GetResult();
                    if (codiceZona == null)
                    {
                        throw new Exception($"Zona {zona.Denominazione} presente nella domanda {zona.Denominazione} non trovato sul DB radioamatori.");
                    }
                    else
                    {
                        zona.IsZonaSpeciale = codiceZona.TIPO?.Trim().Equals("S") ?? false;
                    }
                }
            }

            return zone;
        }

        public async Task<List<AnagraficaContestContestDto>> GetAnagraficaContestAsync()
        {
            return this._mapper.Map<List<AnagraficaContestContestDto>>((await this._contestDal.GetAnagraficaContestAsync()));
        }

        public async Task<List<AnagraficaContestContestDto>> GetContestbyRadioamatoreAsync(string idRadioamatore)
        {
            return this._mapper.Map<List<AnagraficaContestContestDto>>((await this._contestDal.GetContestbyRadioamatoreAsync(idRadioamatore)));
        }

        public async Task<List<RadioamatoreDto>> GetRadioamatoriByCodiceContestAsync(string codiceContest)
        {
            return this._mapper.Map<List<RadioamatoreDto>>((await this._radioamatoreDal.GetRadioamatoriByCodiceContestAsync(codiceContest)));
        }

        public async Task<List<TipoManifestazioneDTO>> GetTipologieManifestazioni()
        {
            return this._mapper.Map<List<TipoManifestazioneDTO>>((await this._tipoManifestazioneDal.GetTipologieManifestazioni()));
        }

        public async Task<CodiceNominativoDto> GeneraNominativoStandardAsync(string tipoApplicazione, JObject domanda)
        {
            var now = DateTimeHelper.GetZoneDateTimeNow(this._conf["TimeZoneId"]);
            var nowStr = DateTimeHelper.FormattaNow(this._conf["TimeZoneId"]);

            // STEP 0 - estrapolo i dati dal json di domanda
            Radioamatore radioamatore = DtoMapping.EstrapolaRadioamatoreAutorizzazioniDalJson(domanda, this._zonaMappingDal);

            // STEP 1 - prenotazione del suffisso in funzione della zona nella sezione assegnatario
            Suffisso suffissoPrenotato = (await this._suffissiDal.PrenotaSuffissoAsync(radioamatore.TIPO_ZONA?.Trim() ?? string.Empty));
            radioamatore.CODICE_NOMINATIVO = $"{radioamatore.CODICE_ZONA_STRING}{suffissoPrenotato.SUFFISSO}";
            radioamatore.ID_SUFFISSO = suffissoPrenotato.ID_SUFFISSO;

            // STEP 2 - creazione record radioamatore
            try
            {
                radioamatore.DATA_AGG = now;
                radioamatore.DATA_CREAZIONE = now;
                radioamatore.UTENTE = tipoApplicazione;
                radioamatore.ID_RADIOAMATORE = await this._radioamatoreDal.GeneraNominativoStandardAsync(radioamatore);
            }
            catch (Exception ex)
            {
                if (suffissoPrenotato != null)
                {
                    await this._suffissiDal.RollbackPrenotazioneSuffissoAsync(suffissoPrenotato.ID_SUFFISSO);
                }
                throw;
            }

            return new CodiceNominativoDto()
            {
                IdRadioamatore = radioamatore.ID_RADIOAMATORE,
                CodiceNominativoGenerato = radioamatore.CODICE_NOMINATIVO,
                DataNominativoGenerato = nowStr
            };
        }

        public async Task<CodiceNominativoDto> GeneraNominativoAssociazioneAsync(string tipoApplicazione, JObject domanda)
        {
            var now = DateTimeHelper.GetZoneDateTimeNow(this._conf["TimeZoneId"]);
            var nowStr = DateTimeHelper.FormattaNow(this._conf["TimeZoneId"]);

            // STEP 0 - estrapolo i dati dal json di domanda
            Radioamatore radioamatore = DtoMapping.EstrapolaRadioamatoreDalJson(domanda, this._zonaMappingDal);

            // STEP 1 - Genero il codice nominativo
            // Se il nominativo per manifestazione non è stato proposto allora il sistema dovrà generare un Nominativo in stato "Generato"

            radioamatore.CODICE_NOMINATIVO = (await this.GeneraCodiceNominativoAssociazioneAsync(radioamatore.SIGLA_REGIONE, radioamatore.PROVINCIA));


            // STEP 2 - creazione record radioamatore
            radioamatore.UTENTE = tipoApplicazione;
            radioamatore.DATA_AGG = now;
            radioamatore.DATA_CREAZIONE = now;
            radioamatore.ID_RADIOAMATORE = await this._radioamatoreDal.GeneraNominativoStandardAsync(radioamatore);

            return new CodiceNominativoDto()
            {
                IdRadioamatore = radioamatore.ID_RADIOAMATORE,
                CodiceNominativoGenerato = radioamatore.CODICE_NOMINATIVO,
                DataNominativoGenerato = nowStr
            };
        }

        public async Task<CodiceNominativoDto> GeneraNominativoManifestazioneAsync(string tipoApplicazione, JObject domanda)
        {
            var now = DateTimeHelper.GetZoneDateTimeNow(this._conf["TimeZoneId"]);
            var nowStr = DateTimeHelper.FormattaNow(this._conf["TimeZoneId"]);

            // STEP 0 - estrapolo i dati dal json di domanda
            Radioamatore radioamatore = DtoMapping.EstrapolaRadioamatoreDalJson(domanda, this._zonaMappingDal);
            Manifestazione manifestazione = DtoMapping.EstrapolaManifestazioneDalJson(domanda);

            // STEP 1 - Genero il codice nominativo
            // Se il nominativo per manifestazione non è stato proposto allora il sistema dovrà generare un Nominativo in stato "Generato"
            if (string.IsNullOrWhiteSpace(manifestazione.NOMINATIVO_PROPOSTO))
            {
                radioamatore.CODICE_NOMINATIVO = (await this.GeneraCodiceNominativoManifestazioneAsync(manifestazione.ID_TIPOMANIFESTAZIONE, manifestazione.SIGLA_REGIONE));
            }
            else
            {
                radioamatore.CODICE_NOMINATIVO = manifestazione.NOMINATIVO_PROPOSTO;
            }

            // STEP 2 - creazione record radioamatore
            radioamatore.UTENTE = tipoApplicazione;
            radioamatore.DATA_AGG = now;
            radioamatore.DATA_CREAZIONE = now;
            radioamatore.ID_RADIOAMATORE = await this._radioamatoreDal.GeneraNominativoManifestazioneAsync(radioamatore, manifestazione);

            return new CodiceNominativoDto()
            {
                IdRadioamatore = radioamatore.ID_RADIOAMATORE,
                CodiceNominativoGenerato = radioamatore.CODICE_NOMINATIVO,
                DataNominativoGenerato = nowStr
            };
        }

        public async Task<CodiceNominativoDto> GeneraNominativoContestAsync(string tipoApplicazione, JObject domanda)
        {
            var now = DateTimeHelper.GetZoneDateTimeNow(this._conf["TimeZoneId"]);
            var nowStr = DateTimeHelper.FormattaNow(this._conf["TimeZoneId"]);

            // STEP 0 - estrapolo i dati dal json di domanda
            Radioamatore radioamatore = DtoMapping.EstrapolaRadioamatoreDalJson(domanda, this._zonaMappingDal);
            string codiceNominativoProposto;
            List<Contest> elencoContest = DtoMapping.EstrapolaElencoContestDalJson(domanda, out codiceNominativoProposto);

            // STEP 1 - Genero il codice nominativo
            // Se il nominativo per manifestazione non è stato proposto allora il sistema dovrà generare un Nominativo in stato "Generato"
            if (string.IsNullOrWhiteSpace(codiceNominativoProposto))
            {
                radioamatore.CODICE_NOMINATIVO = (await this.GeneraCodiceNominativoContestAsync(radioamatore.SIGLA_REGIONE, radioamatore.PROVINCIA));
            }
            else
            {
                radioamatore.CODICE_NOMINATIVO = codiceNominativoProposto;
            }

            // STEP 2 - creazione record radioamatore
            radioamatore.DATA_AGG = now;
            radioamatore.DATA_CREAZIONE = now;
            radioamatore.UTENTE = tipoApplicazione;
            radioamatore.ID_RADIOAMATORE = await this._radioamatoreDal.GeneraNominativoContestAsync(radioamatore, elencoContest);

            return new CodiceNominativoDto()
            {
                IdRadioamatore = radioamatore.ID_RADIOAMATORE,
                CodiceNominativoGenerato = radioamatore.CODICE_NOMINATIVO,
                DataNominativoGenerato = nowStr
            };
        }

        public async Task<EsitoValidazioneDto> ControllaNominativoPerGenerazioneAsync(RadioamatoreDaGenerareDto radioamatoreDaGenerare, bool isStandard, bool isContest, bool isManifestazione, bool isManifestazioneArt44)
        {
            var esito = new EsitoValidazioneDto()
            {
                Successo = true,
                ProseguiSuFEAbilitato = true
            };

            if (!radioamatoreDaGenerare.TipoAssegnatario.ToLower().Contains("minorenne"))
            {
                if (radioamatoreDaGenerare.DataNascita.HasValue)
                {
                    var dateTo = DateTime.Now.AddYears(-18);
                    if (dateTo < radioamatoreDaGenerare.DataNascita)
                    {
                        esito.Successo = false;
                        esito.ProseguiSuFEAbilitato = false;
                        esito.Errori.Add($"L'assegnatario non risulta essere maggiorenne");
                    }
                }

                else if (!radioamatoreDaGenerare.TipoAssegnatario.Contains("144"))
                    throw new Exception($"Data di nascita non valorizzata ma obbligatoria");

            }

            if (isStandard)
            {

                var radioamatoriPerCF = await this._radioamatoreDal.GetRadioamatoriByCodiceFiscaleAsync(radioamatoreDaGenerare.CodiceFiscale);

                List<Radioamatore> radioamatoriPerAssegnatario = new List<Radioamatore>();

                radioamatoriPerAssegnatario = radioamatoriPerCF;


                // controllo che che l'Assegnatario non abbia già associato un Nominativo di chiamata in stato In Istruttoria o Attivo
                var nominativiPresenti = radioamatoriPerAssegnatario.Where(c => c.TIPO_NOMINATIVO.ToLower() == "standard" && (c.STATO_NOMINATIVO.ToLower() == "attivo" || c.STATO_NOMINATIVO.ToLower().Contains("istruttoria"))).ToList();
                if (nominativiPresenti.Count > 0)
                {
                    var nominativo = nominativiPresenti.First();
                    esito.Successo = false;
                    esito.ProseguiSuFEAbilitato = false;
                    esito.Errori.Add($"Esiste già un Nominativo di chiamata in stato {nominativo.STATO_NOMINATIVO} per l’assegnatario indicato.");
                }

                // verificare che la chiave Nome_Cognome_DataNascita dell’assegnatario non abbia associato un
                // Nominativo di chiamata in stato Attivo. Se c'è OMONIMA il sistema deve mostrare il seguente messaggio
                // "Esiste già un Nominativo di chiamata Attivo assegnato a persona omonima”
                else
                {
                    if (!radioamatoreDaGenerare.TipoAssegnatario.Contains("144"))
                    {
                        var radioamatoriOmonimi = await this.GetRadioamatoriByAnagraficaAsync(radioamatoreDaGenerare.Nome, radioamatoreDaGenerare.Cognome, radioamatoreDaGenerare.DataNascita.Value.ToString("yyyy-MM-dd")); // check data
                        if (radioamatoriOmonimi.Count > 0)
                        {
                            if (radioamatoriOmonimi.Any(r => r.StatoNominativo.ToLower().Contains("attivo")))
                            {
                                esito.Successo = false;
                                // ProseguiAbilitato rimane com'è
                                esito.Errori.Add("Esiste già un Nominativo di chiamata Attivo assegnato a persona omonima");
                            }
                        }
                    }
                }

                //Se Tipologia assegnatario = Radioamatore minorenne  allora il sistema deve controllare che Data di
                //nascita dell’assegnatario sia compresa tra 16 e 18 anni meno 1 giorno
                if (radioamatoreDaGenerare.TipoAssegnatario.ToLower().Contains("minorenne"))
                {
                    var dateFrom = DateTime.Now.AddYears(-18);
                    var dateTo = DateTime.Now.AddYears(-16);
                    if (!(dateFrom < radioamatoreDaGenerare.DataNascita && dateTo >= radioamatoreDaGenerare.DataNascita))
                    {
                        esito.Successo = false;
                        esito.ProseguiSuFEAbilitato = false;
                        esito.Errori.Add($"La data di nascita dell'assegnatario non è compresa tra 18 e 16 anni fa");
                    }
                }


            }
            else if (isManifestazione || isManifestazioneArt44)
            {
                // Controllo Sul Nominativo di chiamata Radioamatore
                var radioamatoriPerCF = await this._radioamatoreDal.GetRadioamatoriByCodiceFiscaleAsync(radioamatoreDaGenerare.CodiceFiscale);
                var radioamatoriPerNominativo = await this.GetRadioamatoriByCodiceNominantivoAsync(radioamatoreDaGenerare.NominativoDiChiamata);

                ControlloNominativoPerGenerazionePerChiamataPerManifestazioniOContest(esito, radioamatoriPerNominativo, radioamatoreDaGenerare.CodiceFiscale);


                //Controllo sul nominativo Manifestazione
                var radioamatoriAttiviInManifestazioni = radioamatoriPerCF
                        .Where(r => r.TIPO_NOMINATIVO.Contains("MANIFESTAZIONE") && r.STATO_NOMINATIVO.Contains("ATTIVO"))
                        .ToList();

                if (radioamatoriAttiviInManifestazioni.Count > 0)
                {

                    foreach (var radioamatore in radioamatoriAttiviInManifestazioni)
                    {
                        var manifestazioniPerRad = await this._tipoManifestazioneDal.GetManifestazioniByRadioamatoreId(radioamatore.ID_RADIOAMATORE);

                        //controllo che non sia presente per una manifestazione in questo periodo
                        if (manifestazioniPerRad.Any(m =>
                            (radioamatoreDaGenerare.Manifestazione.DataInizio <= m.DATA_INIZIO && m.DATA_INIZIO <= radioamatoreDaGenerare.Manifestazione.DataFine)
                            ||
                            (radioamatoreDaGenerare.Manifestazione.DataInizio <= m.DATA_FINE && m.DATA_FINE <= radioamatoreDaGenerare.Manifestazione.DataFine)
                            ))
                        {
                            esito.Successo = false;
                            esito.ProseguiSuFEAbilitato = false;
                            esito.Errori.Add($"Attenzione: è già presente un Nominativo, per una manifestazione, associato all’assegnatario con CF " +
                                $"{radioamatoreDaGenerare.CodiceFiscale} nello stesso periodo");
                            break;
                        }

                    }

                }

                // controllo formato e disponibilità nominativo
                if (!string.IsNullOrWhiteSpace(radioamatoreDaGenerare.Manifestazione.NominativoManifestazione))
                {
                    var siglaRegione = radioamatoreDaGenerare.Manifestazione.RegioneUbicazione.sigla.ToUpper();
                    var siglaProvinciaBasilicata = radioamatoreDaGenerare.Manifestazione.SiglaProvinciaBasilicata.ToUpper();
                    var prefissoSpec = await _prefissiSpecialiDal.GetPrefissoNominativoSpecialiBySiglaRegioneAsync(siglaRegione != "BAS" ? siglaRegione : siglaProvinciaBasilicata);

                    var strArray = radioamatoreDaGenerare.Manifestazione.NominativoManifestazione;

                    //if (radioamatoreDaGenerare.Manifestazione.TipoManifestazione.ToLower().Contains("associazione")) //Associazione
                    //{

                    //    if (strArray.Length != 5
                    //        || strArray[0] != 'I'
                    //        || strArray[1] != 'Q'
                    //        || !"ABCDEFGHIJKLMNOPQRSTUVWXYZ".Contains(strArray[3])
                    //        || !"ABCDEFGHIJKLMNOPQRSTUVWXYZ".Contains(strArray[4])
                    //        || !(strArray[2] == Char.Parse(prefissoSpec.CODICE_PREFISSO)))
                    //    {
                    //        esito.Successo = false;
                    //        esito.CodiceNominativo = strArray;
                    //        esito.ProseguiSuFEAbilitato = false;
                    //        esito.Errori.Add($"Il codice nominativo {strArray} non rispetta la sintassi richiesta");
                    //    }

                    //}
                    //else 
                    if (radioamatoreDaGenerare.Manifestazione.TipoManifestazione.ToLower().Contains("marconiana"))//Marconiana
                    {
                        if (strArray.Length != 6
                         || strArray[0] != 'I'
                         || strArray[1] != 'Y'
                         || !"ABCDEFGHIJKLMNOPQRSTUVWXYZ".Contains(strArray[3])
                         || !"ABCDEFGHIJKLMNOPQRSTUVWXYZ".Contains(strArray[4])
                         || !"ABCDEFGHIJKLMNOPQRSTUVWXYZ".Contains(strArray[5])
                         || !(strArray[2] == Char.Parse(prefissoSpec.CODICE_PREFISSO)))
                        {
                            esito.Successo = false;
                            esito.CodiceNominativo = strArray;
                            esito.ProseguiSuFEAbilitato = false;
                            esito.Errori.Add($"Il codice nominativo {strArray} non rispetta la sintassi richiesta");
                        }
                    }
                    else if (radioamatoreDaGenerare.Manifestazione.TipoManifestazione.ToLower().Contains("altro"))//Altro
                    {
                        if (strArray.Length != 7
                         || strArray[0] != 'I'
                         || !"RI".Contains(strArray[1])
                         || !"ABCDEFGHIJKLMNOPQRSTUVWXYZ".Contains(strArray[3])
                         || !"ABCDEFGHIJKLMNOPQRSTUVWXYZ".Contains(strArray[4])
                         || !"ABCDEFGHIJKLMNOPQRSTUVWXYZ".Contains(strArray[5])
                         || !"ABCDEFGHIJKLMNOPQRSTUVWXYZ".Contains(strArray[6])
                         || !(strArray[2] == Char.Parse(prefissoSpec.CODICE_PREFISSO)))
                        {
                            esito.Successo = false;
                            esito.CodiceNominativo = strArray;
                            esito.ProseguiSuFEAbilitato = false;
                            esito.Errori.Add($"Il codice nominativo {strArray} non rispetta la sintassi richiesta");
                        }

                    }

                    // controllo disponibilità
                    var nominativiUguali = await this.GetRadioamatoriByCodiceNominantivoAsync(radioamatoreDaGenerare.Manifestazione.NominativoManifestazione);
                    var nominativiManifestazioniUgualiAttiviOIstruttoria = nominativiUguali
                                                    .Where(r => (r.StatoNominativo == "ATTIVO" || r.StatoNominativo == "IN_ISTRUTTORIA")
                                                                        && r.TipoNominativo == "MANIFESTAZIONE").ToList();

                    if (nominativiManifestazioniUgualiAttiviOIstruttoria.Count > 0)
                    {
                        foreach (var nominativo in nominativiManifestazioniUgualiAttiviOIstruttoria)
                        {
                            var manifestazioniPerNominativo = await this._tipoManifestazioneDal.GetManifestazioniByRadioamatoreId(nominativo.IdRadioamatore);

                            if (manifestazioniPerNominativo.Any(m =>
                                (radioamatoreDaGenerare.Manifestazione.DataInizio <= m.DATA_INIZIO && m.DATA_INIZIO <= radioamatoreDaGenerare.Manifestazione.DataFine)
                                ||
                                (radioamatoreDaGenerare.Manifestazione.DataInizio <= m.DATA_FINE && m.DATA_FINE <= radioamatoreDaGenerare.Manifestazione.DataFine)
                               ))
                            {
                                esito.Successo = false;
                                esito.CodiceNominativo = strArray;
                                esito.ProseguiSuFEAbilitato = false;
                                esito.StatoNominativo = nominativo.StatoNominativo;
                                esito.Errori.Add($"Attenzione: il nominativo proposto non risulta disponibile per il periodo selezionato.");
                                break;
                            }

                        }
                    }
                }

            }
            else if (isContest)
            {
                // Controllo Sul Nominativo di chiamata Radioamatore
                var radioamatoriPerCF = await this._radioamatoreDal.GetRadioamatoriByCodiceFiscaleAsync(radioamatoreDaGenerare.CodiceFiscale);
                var radioamatoriPerNominativo = await this.GetRadioamatoriByCodiceNominantivoAsync(radioamatoreDaGenerare.NominativoDiChiamata);

                ControlloNominativoPerGenerazionePerChiamataPerManifestazioniOContest(esito, radioamatoriPerNominativo, radioamatoreDaGenerare.CodiceFiscale);

                // Controllo sui contest per cui si fa richiesta
                foreach (var contest in radioamatoreDaGenerare.Contest.ListaContest)
                {
                    var radioamatoriAttiviInContest = radioamatoriPerCF
                        .Where(r => r.TIPO_NOMINATIVO.Contains("CONTEST") && r.STATO_NOMINATIVO.Contains("ATTIVO"))
                        .ToList();

                    // Serve per recuperare le anagrafiche dei contest in cui è presente la data
                    // valutare se farlo con dati provenienti da FE
                    var listaIdRads = radioamatoriAttiviInContest.Select(r => r.ID_RADIOAMATORE).ToList();
                    var contestsPerRadioamatore = await this._contestDal.GetContesByListRadioamatoritAsync(listaIdRads);
                    var listaCodiciContest = contestsPerRadioamatore.Select(c => c.CODICE_ANAGRAFICA_CONTEST).ToList();
                    var anagraficheContest = await this._contestDal.GetAnagraficaContestAsync();
                    List<AnagraficaContest> listaAnagraficaContest = new List<AnagraficaContest>();

                    foreach (var codice in listaCodiciContest)
                    {
                        var anagrafica = anagraficheContest.FirstOrDefault(an => an.CODICE == codice);
                        listaAnagraficaContest.Add(anagrafica);
                    }


                    if (radioamatoriAttiviInContest.Count > 0)
                    {

                        if (listaAnagraficaContest.Any(c => c.CODICE.Equals(contest.Codice)))
                        {
                            esito.Successo = false;
                            esito.ProseguiSuFEAbilitato = false;
                            esito.Errori.Add($"Attenzione: è già presente un Nominativo per i contest selezionati associato all’assegnatario indicato");
                            break;
                        }

                    }



                }

                // Controllo sul nominativo proposto per contest se presente
                // controllo morfologia eventuale nominativo proposto
                if (!string.IsNullOrWhiteSpace(radioamatoreDaGenerare.Contest.NominativoContest))
                {
                    var siglaRegione = radioamatoreDaGenerare.Regione.sigla.ToUpper();
                    var siglaProvincia = radioamatoreDaGenerare.Provincia.sigla.ToUpper();

                    var prefissoSpec = await _prefissiSpecialiDal.GetPrefissoNominativoSpecialiBySiglaRegioneAsync("BAS" != siglaRegione ? siglaRegione : siglaProvincia);

                    var strArray = radioamatoreDaGenerare.Contest.NominativoContest;

                    // controllo morfologia
                    if (strArray.Length != 4
                        || strArray[0] != 'I'
                        || !"PBOI".Contains(strArray[1])
                        || !"ABCDEFGHIJKLMNOPQRSTUVWXYZ".Contains(strArray[3])
                        || !(strArray[2] == Char.Parse(prefissoSpec.CODICE_PREFISSO)))
                    {
                        esito.Successo = false;
                        esito.CodiceNominativo = strArray;
                        esito.ProseguiSuFEAbilitato = false;
                        esito.Errori.Add($"Il codice nominativo {strArray} non rispetta la sintassi richiesta");
                    }


                    // controllo disponibilità
                    var nominativiUguali = await this.GetRadioamatoriByCodiceNominantivoAsync(radioamatoreDaGenerare.Contest.NominativoContest);
                    var nominativiContestUgualiAttiviOIstruttoria = nominativiUguali
                                                    .Where(r => (r.StatoNominativo == "ATTIVO" || r.StatoNominativo == "IN_ISTRUTTORIA")
                                                                        && r.TipoNominativo == "CONTEST").ToList();

                    if (nominativiContestUgualiAttiviOIstruttoria.Any())
                    {
                        esito.Successo = false;
                        esito.CodiceNominativo = strArray;
                        esito.ProseguiSuFEAbilitato = false;
                        esito.Errori.Add($"Il codice nominativo {strArray} non è disponibile poiché già assegnato per un contest");

                    }

                }


            }

            return esito;
        }

        public async Task<EsitoValidazioneDto> ControllaNominativoPerIstruttoriaAsync(RadioamatoreDaGenerareDto radioamatoreDaGenerare, bool isStandard, bool isContest, bool isManifestazione, bool isManifestazioneArt44)
        {
            var esito = new EsitoValidazioneDto()
            {
                Successo = true,
                ProseguiSuFEAbilitato = true
            };

            if (isStandard)
            {

                var radioamatoriPerCF = await this._radioamatoreDal.GetRadioamatoriByCodiceFiscaleAsync(radioamatoreDaGenerare.CodiceFiscale);

                List<Radioamatore> radioamatoriPerAssegnatario = new List<Radioamatore>();
                if (radioamatoreDaGenerare.DataNascita.HasValue)
                {
                    var radioamatoriPerAnagrafica = await this._radioamatoreDal.GetRadioamatoriByAnagraficaAsync(radioamatoreDaGenerare.Nome, radioamatoreDaGenerare.Cognome, radioamatoreDaGenerare.DataNascita.Value.ToString("yyyy-MM-dd", CultureInfo.InvariantCulture));
                    radioamatoriPerAssegnatario = radioamatoriPerCF.Union(radioamatoriPerAnagrafica).ToList();
                }
                else
                {
                    radioamatoriPerAssegnatario = radioamatoriPerCF;
                }

                // controllo che che Assegnatario non abbia già associato un Nominativo di chiamata in stato Attivo
                var nominativiPresenti = radioamatoriPerAssegnatario.Where(c => c.TIPO_NOMINATIVO.Contains("STANDARD") && c.STATO_NOMINATIVO.Contains("ATTIVO")).ToList();

                if (nominativiPresenti.Count > 0)
                {
                    esito.Successo = false;
                    esito.ProseguiSuFEAbilitato = false;
                    esito.Errori.Add($"Esiste già un Nominativo di chiamata in stato ATTIVO per l’assegnatario indicato.");
                }


            }
            else if (isManifestazione || isManifestazioneArt44)
            {
                // Controllo Sul Nominativo di chiamata Radioamatore
                var radioamatoriPerCodiceNominativo = await this._radioamatoreDal.GetRadioamatoriByCodiceNominantivoAsync(radioamatoreDaGenerare.Manifestazione.NominativoManifestazione);


                //Controllo sul nominativo Manifestazione
                var radioamatoriAttiviInManifestazioni = radioamatoriPerCodiceNominativo
                        .Where(r => r.TIPO_NOMINATIVO.Contains("MANIFESTAZIONE") && r.STATO_NOMINATIVO.Contains("ATTIVO"))
                        .ToList();

                if (radioamatoriAttiviInManifestazioni.Count > 0)
                {
                    foreach (var radioamatore in radioamatoriAttiviInManifestazioni)
                    {
                        var manifestazioniPerRad = await this._tipoManifestazioneDal.GetManifestazioniByRadioamatoreId(radioamatore.ID_RADIOAMATORE);

                        //controllo che non sia presente per una manifestazione in questo periodo
                        if (manifestazioniPerRad.Any(m =>
                            (radioamatoreDaGenerare.Manifestazione.DataInizio <= m.DATA_INIZIO && m.DATA_INIZIO <= radioamatoreDaGenerare.Manifestazione.DataFine)
                            ||
                            (radioamatoreDaGenerare.Manifestazione.DataInizio <= m.DATA_FINE && m.DATA_FINE <= radioamatoreDaGenerare.Manifestazione.DataFine)
                            ))
                        {
                            esito.Successo = false;
                            esito.ProseguiSuFEAbilitato = false;
                            esito.Errori.Add($"Attenzione: il nominativo {radioamatoreDaGenerare.Manifestazione.NominativoManifestazione} è già utilizzato in una manifestazione nello stesso periodo");
                            break;
                        }

                    }

                }

            }
            else if (isContest)
            {

                //verifico che il nominativo non sia già attivo su un contest per il quale si chiede l'attivazione
                var nominativoGiaAttivo = await _contestDal.EsisteNominativoAttivoSuContest(radioamatoreDaGenerare.CodiceProtocollo, radioamatoreDaGenerare.Contest.ListaContest.Select(s => s.Codice).ToList());

                if (nominativoGiaAttivo)
                {
                    esito.Successo = false;
                    esito.ProseguiSuFEAbilitato = false;
                    esito.Errori.Add($"Attenzione: è già presente un Nominativo per i contest selezionati associato all’assegnatario indicato");
                }
            }

            return esito;
        }

        public async Task<CodiceNominativoDto> GeneraNominativoGeaAsync(
            string tipoApplicazione,
            RadioamatoreDaGenerareDto inputRad,
            bool isStandard,
            bool isContest,
            bool isManifestazione,
            bool isManifestazione44)
        {
            var now = DateTimeHelper.GetZoneDateTimeNow(this._conf["TimeZoneId"]);
            var nowStr = DateTimeHelper.FormattaNow(this._conf["TimeZoneId"]);

            Radioamatore radioamatore = await MappingNoJObject.MapIncomingToDBRadioamatore
                (inputRad, isStandard, isContest, isManifestazione, isManifestazione44, _zonaMappingDal, _ispettoratoDal, "ATTIVO");

            radioamatore.DATA_AGG = now;
            radioamatore.DATA_CREAZIONE = now;
            radioamatore.UTENTE = tipoApplicazione;
            radioamatore.DATA_RILASCIO = now;
            radioamatore.TIPOLOGIA_ASSEGNATARIO = inputRad.TipoAssegnatario;

            //Anche se si chiama così non è sempre front end, è il protocollo con cui viene salvato il nominativo
            radioamatore.PROTOCOLLO_PRESENTAZIONE_FE = inputRad.CodiceProtocollo;
            radioamatore.DATA_PROTOCOLLO_PRESENTAZIONE_FE = inputRad.DataProtocollo;

            if (isStandard)
            {
                // Logica particolare per associazioni
                string associazione = "Associazioni o sezioni delle associazioni dei radioamatori legalmente costituite di cui all’art. 144, comma 1, lettera d) del CdC";
                if (inputRad.TipoAssegnatario.Trim().ToLower().Contains(associazione.Trim().ToLower()))
                {
                    radioamatore.CODICE_NOMINATIVO = (await this.GeneraCodiceNominativoAssociazioneAsync(radioamatore.SIGLA_REGIONE, radioamatore.PROVINCIA));
                    radioamatore.ID_RADIOAMATORE = await this._radioamatoreDal.GeneraNominativoStandardAsync(radioamatore);

                }
                else
                {
                    // STEP 1 - prenotazione del suffisso in funzione della zona nella sezione assegnatario
                    Suffisso suffissoPrenotato = (await this._suffissiDal.PrenotaSuffissoAsync(radioamatore.TIPO_ZONA?.Trim() ?? string.Empty));
                    radioamatore.CODICE_NOMINATIVO = $"{radioamatore.CODICE_ZONA_STRING}{suffissoPrenotato.SUFFISSO}";
                    radioamatore.ID_SUFFISSO = suffissoPrenotato.ID_SUFFISSO;

                    // STEP 2 - creazione record radioamatore
                    try
                    {
                        radioamatore.ID_RADIOAMATORE = await this._radioamatoreDal.GeneraNominativoStandardAsync(radioamatore);
                    }
                    catch
                    {
                        if (suffissoPrenotato != null)
                        {
                            await this._suffissiDal.RollbackPrenotazioneSuffissoAsync(suffissoPrenotato.ID_SUFFISSO);
                        }
                        throw;
                    }
                }

            }
            else if (isManifestazione || isManifestazione44)
            {
                Manifestazione manifestazione = await MappingNoJObject.MapIncomingToDBManifestazione
                    (inputRad.Manifestazione, _tipoManifestazioneDal, _ispettoratoDal);

                // Se il nominativo per manifestazione non è stato proposto allora il sistema dovrà generare un Nominativo in stato "Generato"
                if (string.IsNullOrWhiteSpace(manifestazione.NOMINATIVO_PROPOSTO))
                {
                    // SIGLA_REGIONE è la sigla della provincia per la basilicata
                    radioamatore.CODICE_NOMINATIVO = (await this.GeneraCodiceNominativoManifestazioneAsync(manifestazione.ID_TIPOMANIFESTAZIONE, manifestazione.SIGLA_REGIONE));
                }
                else
                {
                    radioamatore.CODICE_NOMINATIVO = manifestazione.NOMINATIVO_PROPOSTO;
                }

                radioamatore.ID_RADIOAMATORE = await this._radioamatoreDal.GeneraNominativoManifestazioneAsync(radioamatore, manifestazione);

            }
            else if (isContest)
            {
                string codiceNominativoProposto;
                List<Contest> elencoContest = MappingNoJObject.MapIncomingToDBContest(inputRad.Contest, out codiceNominativoProposto);
                if (string.IsNullOrWhiteSpace(codiceNominativoProposto))
                {
                    radioamatore.CODICE_NOMINATIVO = (await this.GeneraCodiceNominativoContestAsync(radioamatore.SIGLA_REGIONE, radioamatore.PROVINCIA));
                }
                else
                {
                    radioamatore.CODICE_NOMINATIVO = codiceNominativoProposto;
                }

                radioamatore.ID_RADIOAMATORE = await this._radioamatoreDal.GeneraNominativoContestAsync(radioamatore, elencoContest);
            }
            else
                throw new Exception();



            return new CodiceNominativoDto()
            {
                IdRadioamatore = radioamatore.ID_RADIOAMATORE,
                CodiceNominativoGenerato = radioamatore.CODICE_NOMINATIVO,
                DataNominativoGenerato = nowStr
            };
        }

        public async Task<List<ManifestazioneDto>> GetManifestazioniByRadioamatoriAsync(int idRadiamatore)
        {


            return this._mapper.Map<List<ManifestazioneDto>>(await this._tipoManifestazioneDal.GetManifestazioniByRadioamatoreId(idRadiamatore));

        }

        public async Task<string> GetCodiceRegioneByRegione(string regione, string provincia)
        {
            var denominazione = regione.EqualsIgnoreCaseAndTrim("Basilicata") ? $"provincia di {provincia}" : regione;
            return (await _regioneDal.GetRegioneByDenominazioneAsync(denominazione))?.SIGLA;
        }

        public async Task<int> InserisciNominativoStandardAsync(RadioamatoreDaGenerareDto radioamatoreDaGenerare)
        {
            Radioamatore radioamatore = await MappingNoJObject.MapIncomingToDBRadioamatore(radioamatoreDaGenerare, true, false, false, false, _zonaMappingDal, _ispettoratoDal, "ATTIVO");
            radioamatore.CODICE_NOMINATIVO = radioamatoreDaGenerare.CodiceNominativo;
            radioamatore.TIPOLOGIA_ASSEGNATARIO = radioamatoreDaGenerare.TipoAssegnatario;
            return await _radioamatoreDal.GeneraNominativoStandardAsync(radioamatore);
        }

        public async Task<int> SetProtocolloUscitaNominativoAsync(ProtocolloUscitaDto protocolloUscitaDto)
        {
            var radioamatore = await _radioamatoreDal.GetRadioamatoreByCodiceDomandaFEAsync(protocolloUscitaDto.CodiceDomandaFE);

            return await _radioamatoreDal.SetProtocolloUscitaAsync(radioamatore.ID_RADIOAMATORE, 
                                                                    DateTimeHelper.ParseDataInput(protocolloUscitaDto.DataProtocolloInUscita), 
                                                                    protocolloUscitaDto.CodiceProtocolloInUscita, 
                                                                    DateTimeHelper.ParseDataInput(protocolloUscitaDto.DataRilascioNominativo));
        }

        #endregion

        #region Patenti

        public async Task<List<PatenteDto>> GetPatentiByFilterAsync(PatenteFilterDto filtro)
        {
            var entities = await _patenteDal.GetPatentiByFilterAsync(filtro);
            return this._mapper.Map<List<PatenteDto>>(entities);
        }

        public async Task<List<PatenteDto>> GetPatentiByIspettoratoConGestioneSubentriAsync(PatenteFilterDto filtro)
        {
            var entities = await _patenteDal.GetPatentiByIspettoratoConGestioneSubentriAsync(filtro);
            return this._mapper.Map<List<PatenteDto>>(entities);
        }

        public async Task<string> InsertPatenteAsync(string idRisorsa, PatenteDto patente, RequestOrigin requestOrigin)
        {
            patente.CodicePatente = await GeneraCodicePatente(patente.CodiceRegioneResidenza);

            var statoPatente = (requestOrigin == RequestOrigin.GEA) ? Enums.StatoPatente.Attiva : Enums.StatoPatente.RichiestaAmmissione;
            patente.StatoPatente = new StatoPatenteDto { Id = (int)statoPatente, Descrizione = statoPatente.GetAttributeDescription() };
            patente.IdStatoPatente = (int)statoPatente;

            patente.Ispettorato = _mapper.Map<IspettoratoDto>(await _ispettoratoDal.GetIspettoratoBySiglaRegioneAsync(patente.CodiceRegioneResidenza));

            var documentoPatente = patente.Documenti?.FirstOrDefault(doc => doc.TipoDocumento.Equals(TipoDocumentoPatente.PATENTE.ToString(), StringComparison.InvariantCultureIgnoreCase));
            if (documentoPatente != null && !string.IsNullOrWhiteSpace(documentoPatente.FileBase64))
            {
                patente.RiferimentoDocumentoPatente = await UploadDocumentoRAD2(idRisorsa, patente.Ispettorato, documentoPatente, patente.CodiceProtocolloInUscita, patente.DataProtocolloInUscita, patente.DataRilascio);
            }

            var certificatoHarec = patente.Documenti?.FirstOrDefault(doc => doc.TipoDocumento.Equals(TipoDocumentoPatente.HAREC.ToString(), StringComparison.InvariantCultureIgnoreCase));
            if (certificatoHarec != null && !string.IsNullOrWhiteSpace(certificatoHarec.FileBase64))
            {
                patente.RiferimentoDocumentoHarec = await UploadDocumentoRAD2(idRisorsa, patente.Ispettorato, certificatoHarec);
            }

            return await _patenteDal.InsertPatenteAsync(_mapper.Map<Patente>(patente));
        }

        public async Task<int> UpdatePatenteAsync(PatenteDto patente)
        {
            return await _patenteDal.UpdatePatenteAsync(_mapper.Map<Patente>(patente));
        }

        public async Task<string> GestisciCambioStatoPatenteAsync(PatenteDto patenteDto, Enums.StatoPatente statoPatente)
        {
            return await _patenteDal.UpdateStatoPatenteByCodiceAsync((int)statoPatente, patenteDto.CodicePatente);
        }

        public async Task<List<PatenteDto>> GetPatentiByCfAsync(string codiceFiscale)
        {
            var entity = await _patenteDal.GetPatentiByCfAsync(codiceFiscale);
            return _mapper.Map<List<PatenteDto>>(entity);
        }

        public async Task<int> SetProtocolloPatenteAsync(string codiceDomandaFE, DateTime dataProtocollo, string protocollo)
        {
            return await _patenteDal.SetProtocolloAsync(codiceDomandaFE, dataProtocollo, protocollo);
        }

        public async Task<string> SetProtocolloUscitaPatenteAsync(string idRisorsa, ProtocolloUscitaDto protocolloUscitaDto)
        {
            var patente = await GetPatenteByCodiceAsync(protocolloUscitaDto.CodicePatente);

            var documentoPatente = protocolloUscitaDto.Documenti.FirstOrDefault(doc => doc.TipoDocumento.Equals(TipoDocumentoPatente.PATENTE.ToString(), StringComparison.InvariantCultureIgnoreCase));
            if (documentoPatente != null && !string.IsNullOrWhiteSpace(documentoPatente.FileBase64))
            {
                patente.RiferimentoDocumentoPatente = await UploadDocumentoRAD2(idRisorsa, patente.Ispettorato, documentoPatente, protocolloUscitaDto.CodiceProtocolloInUscita, protocolloUscitaDto.DataProtocolloInUscita, protocolloUscitaDto.DataRilascioPatente);
            }

            var certificatoHarec = protocolloUscitaDto.Documenti.FirstOrDefault(doc => doc.TipoDocumento.Equals(TipoDocumentoPatente.HAREC.ToString(), StringComparison.InvariantCultureIgnoreCase));
            if (certificatoHarec != null && !string.IsNullOrWhiteSpace(certificatoHarec.FileBase64))
            {
                patente.RiferimentoDocumentoHarec = await UploadDocumentoRAD2(idRisorsa, patente.Ispettorato, certificatoHarec);
            }

            return await _patenteDal.SetProtocolloUscitaAsync(patente.CodicePatente, DateTimeHelper.ParseDataInput(protocolloUscitaDto.DataProtocolloInUscita), protocolloUscitaDto.CodiceProtocolloInUscita, DateTimeHelper.ParseDataInput(protocolloUscitaDto.DataRilascioPatente), patente.RiferimentoDocumentoPatente, patente.RiferimentoDocumentoHarec);
        }

        public async Task<List<ScadenzaNominativoDto>> SetScadenzaNominativiAsync(bool isReadOnly)
        {
            var result = new List<ScadenzaNominativoDto>();

            var nominativiScaduti = await _radioamatoreDal.GetNominativiDaSettareScadutiAsync();

            if (nominativiScaduti.Any())
            {
                if (!isReadOnly)
                {
                    _ = await _radioamatoreDal.SetNominativiScadutiByIdAsync(nominativiScaduti);
                }

                result = _mapper.Map<List<ScadenzaNominativoDto>>(nominativiScaduti);
                result.ForEach(n =>
                {
                    n.StatoNominativo = EnumStatiRadioamatori.SCADUTO.ToString();
                    n.Updated = !isReadOnly;
                });
            }

            return result;
        }

        public async Task<PatenteDto> GetPatenteByIdAsync(int idPatente)
        {
            var entity = await _patenteDal.GetPatenteByIdAsync(idPatente);
            return _mapper.Map<PatenteDto>(entity);
        }

        public async Task<PatenteDto> GetPatenteByCodiceAsync(string codicePatente)
        {
            var entity = await _patenteDal.GetPatenteByCodiceAsync(codicePatente);
            return _mapper.Map<PatenteDto>(entity);
        }

        public async Task<List<PatenteDto>> GetPatenteByNumeroCartaceaAsync(string numeroPatenteCartacea)
        {
            var entity = await _patenteDal.GetPatenteByNumeroCartaceaAsync(numeroPatenteCartacea);
            return _mapper.Map< List<PatenteDto>>(entity);
        }

        public async Task<bool> ExistsPatenteByCodiceAsync(string codicePatente)
        {
            return (await GetPatenteByCodiceAsync(codicePatente)) != null;
        }

        public async Task<PatenteDto> UploadDocumentoPatenteAsync(string idRisorsa, PatenteDto dto)
        {
            var patente = await GetPatenteByIdAsync(dto.Id);

            var documentoPatente = dto.Documenti?.FirstOrDefault(doc => doc.TipoDocumento.Equals(TipoDocumentoPatente.PATENTE.ToString(), StringComparison.InvariantCultureIgnoreCase));
            if (documentoPatente != null && !string.IsNullOrWhiteSpace(documentoPatente.FileBase64))
            {
                patente.RiferimentoDocumentoPatente = await UploadDocumentoRAD2(idRisorsa, patente.Ispettorato, documentoPatente, patente.CodiceProtocolloInUscita, patente.DataProtocolloInUscita, patente.DataRilascio);
            }

            var certificatoHarec = dto.Documenti?.FirstOrDefault(doc => doc.TipoDocumento.Equals(TipoDocumentoPatente.HAREC.ToString(), StringComparison.InvariantCultureIgnoreCase));
            if (certificatoHarec != null && !string.IsNullOrWhiteSpace(certificatoHarec.FileBase64))
            {
                patente.RiferimentoDocumentoHarec = await UploadDocumentoRAD2(idRisorsa, patente.Ispettorato, certificatoHarec);
            }

            _ = await _patenteDal.UpdateRiferimentiDocumenti(dto.Id, patente.RiferimentoDocumentoPatente, patente.RiferimentoDocumentoHarec);

            return patente;
        }

        #endregion

        #region Autorizzazioni

        public async Task<AutorizzazioneDto> GetAutorizzazioneByIdAsync(int idAutorizzazione)
        {
            var entity = await _autorizzazioneDal.GetAutorizzazioniByIdAsync(idAutorizzazione);
            return _mapper.Map<AutorizzazioneDto>(entity);
        }

        public async Task<List<AutorizzazioneDto>> GetAutorizzazioniByCodiceFiscaleAsync(string codiceFiscale)
        {
            var entities = await this._autorizzazioneDal.GetAutorizzazioniByCodiceFiscaleAsync(codiceFiscale);
            return _mapper.Map<List<AutorizzazioneDto>>(entities); ;
        }

        public async Task<AutorizzazioneDto> GetAutorizzazioneByNumeroAsync(string numeroAutorizzazione)
        {
            var entity = await _autorizzazioneDal.GetAutorizzazioneByNumeroAsync(numeroAutorizzazione);
            return _mapper.Map<AutorizzazioneDto>(entity);
        }

        public async Task<List<AutorizzazioneDto>> GetAutorizzazioneByFilterAsync(AutorizzazioneFilterDto filtro)
        {
            var entities = await _autorizzazioneDal.GetAutorizzazioniByFilterAsync(filtro);
            return this._mapper.Map<List<AutorizzazioneDto>>(entities);
        }

        public async Task<List<AutorizzazioneDto>> GetAutorizzazioniByIspettoratoConGestioneSubentriAsync(AutorizzazioneFilterDtoBase filtro, string idIspettorato)
        {
            var entities = await _autorizzazioneDal.GetAutorizzazioniByIspettoratoConGestioneSubentriAsync(filtro, idIspettorato);
            return this._mapper.Map<List<AutorizzazioneDto>>(entities);
        }

        public async Task<List<AutorizzazioneSRDto>> GetAutorizzazioneSRByFilterAsync(AutorizzazioneSRFilterDto filtro)
        {
            var entities = await _autorizzazioneSRDal.GetAutorizzazioniSRByFilterAsync(filtro);
            return this._mapper.Map<List<AutorizzazioneSRDto>>(entities);
        }

        public async Task<List<AutorizzazioneSRDto>> GetAutorizzazioniSRByIspettoratoConGestioneSubentriAsync(AutorizzazioneSRFilterDtoBase filtro, string idSpettorato)
        {
            var entities = await _autorizzazioneSRDal.GetAutorizzazioniSRByIspettoratoConGestioneSubentriAsync(filtro, idSpettorato);
            return this._mapper.Map<List<AutorizzazioneSRDto>>(entities);
        }

        public async Task<AutorizzazioneSRDto> GetAutorizzazioneSRByIdAsync(int idAutorizzazione)
        {
            var entity = await _autorizzazioneSRDal.GetAutorizzazioniSRByIdAsync(idAutorizzazione);
            return _mapper.Map<AutorizzazioneSRDto>(entity);
        }

        public async Task<bool> ExistsAutorizzazioneByNumeroAsync(string numeroAutorizzazione)
        {
            return (await GetAutorizzazioneByNumeroAsync(numeroAutorizzazione)) != null;
        }

        public async Task<int> GestisciCambioStatoAutorizzazioneAsync(AutorizzazioneDto autorizzazioneDto, EnumStatoAutorizzazione statoAutorizzazione)
        {
            if (statoAutorizzazione == EnumStatoAutorizzazione.Attiva)
            {
                var autorizzazione = await _autorizzazioneDal.GetAutorizzazioniByIdAsync(autorizzazioneDto.Id);
                if (autorizzazione.RINNOVO ?? false && autorizzazione.ID_AUTORIZZAZIONE_RINNOVATA.HasValue)
                {
                    _ = await _autorizzazioneDal.UpdateStatoAutorizzazioneByIdAsync((short)EnumStatoAutorizzazione.Disattivata, autorizzazione.ID_AUTORIZZAZIONE_RINNOVATA.Value);
                }
            }

            return await _autorizzazioneDal.UpdateStatoAutorizzazioneByIdAsync((short)statoAutorizzazione, autorizzazioneDto.Id);
        }

        public async Task<ResponseInsertAutorizzazioneDto> InsertAutorizzazioneAsync(string idRisorsa, AutorizzazioneDto autorizzazioneDto, RequestOrigin requestOrigin)
        {
            var isRinnovo = (bool)autorizzazioneDto.IsRinnovo;

            var statoAutorizzazione = (requestOrigin == RequestOrigin.GEA) ?
                                                            EnumStatoAutorizzazione.Attiva : (!isRinnovo ? EnumStatoAutorizzazione.InIstruttoria : EnumStatoAutorizzazione.Rinnovo);

            autorizzazioneDto.IdStatoAutorizzazione = (short)statoAutorizzazione;
            autorizzazioneDto.StatoAutorizzazione = new StatoAutorizzazioneDto { Id = (int)statoAutorizzazione, Descrizione = statoAutorizzazione.GetAttributeDescription() };
            autorizzazioneDto.Utente = requestOrigin == RequestOrigin.GEA ? autorizzazioneDto.Utente : "RDA";

            if (requestOrigin == RequestOrigin.GEA)
            {
                var documentoAutorizzazione = autorizzazioneDto.Documenti?.FirstOrDefault(doc => doc.TipoDocumento.EqualsIgnoreCaseAndTrim(TipoDocumentoAutorizzazione.AUTORIZZAZIONE_GENERALE.ToString()));
                var ispettorati = await GetAnagraficaIspettoratiAsync();
                autorizzazioneDto.AnagraficaIspettorato = ispettorati.FirstOrDefault(i => i.IdIspettorato.EqualsIgnoreCaseAndTrim(autorizzazioneDto.IdIspettorato));
                if (documentoAutorizzazione != null && !string.IsNullOrWhiteSpace(documentoAutorizzazione.FileBase64))
                {
                    autorizzazioneDto.RiferimentoDocumentoAutorizzazione = await UploadDocumentoRAD3(idRisorsa, autorizzazioneDto.AnagraficaIspettorato, documentoAutorizzazione, autorizzazioneDto.CodiceProtocolloInUscita, autorizzazioneDto.DataProtocolloInUscita, autorizzazioneDto.DataRilascio, true);
                }
            }

            if (isRinnovo)
            {
                var autorizzazioneDaRinnovare = (await GetAutorizzazioneByFilterAsync(new AutorizzazioneFilterDto { IdStatoAutorizzazione = (int)EnumStatoAutorizzazione.InScadenza, CodiceFiscale = autorizzazioneDto.CodiceFiscale, IdTipologiaAutorizzazione = autorizzazioneDto.IdTipologiaAutorizzazione })).LastOrDefault();
                if (autorizzazioneDaRinnovare != null)
                {
                    autorizzazioneDto.IdAutorizzazioneRinnovata = autorizzazioneDaRinnovare.Id;
                    autorizzazioneDto.NumeroAutorizzazione = autorizzazioneDaRinnovare.NumeroAutorizzazione;
                }
            }

            if (!isRinnovo || string.IsNullOrWhiteSpace(autorizzazioneDto.NumeroAutorizzazione))
            {
                autorizzazioneDto.NumeroAutorizzazione = await GeneraNumeroAutorizzazione();
            }

            var nominativo = (await GetRadioamatoriByCodiceNominantivoAsync(autorizzazioneDto.CodiceNominativo)).FirstOrDefault();

            if (nominativo != null)
                autorizzazioneDto.IdRadioamatore = nominativo.IdRadioamatore;


            return new ResponseInsertAutorizzazioneDto()
            {
                IdAutorizzazione = await _autorizzazioneDal.InsertAutorizzazioneAsync(_mapper.Map<Autorizzazione>(autorizzazioneDto)),
                NumeroAutorizzazione = autorizzazioneDto.NumeroAutorizzazione
            };

        }

        public async Task<int> SetProtocolloAutorizzazioneAsync(string codiceDomandaFE, DateTime dataProtocollo, string protocollo)
        {
            return await _autorizzazioneDal.SetProtocolloAsync(codiceDomandaFE, dataProtocollo, protocollo);
        }

        public async Task<int> SetProtocolloUscitaAutorizzazioneAsync(string idRisorsa, ProtocolloUscitaDto protocolloUscitaDto)
        {
            var autorizzazione = await GetAutorizzazioneByIdAsync(protocolloUscitaDto.IdAutorizzazione.Value);

            var documentoAutorizzazione = protocolloUscitaDto.Documenti?.FirstOrDefault(doc => doc.TipoDocumento.EqualsIgnoreCaseAndTrim(TipoDocumentoAutorizzazione.AUTORIZZAZIONE_GENERALE.ToString()));
            if (documentoAutorizzazione != null && !string.IsNullOrWhiteSpace(documentoAutorizzazione.FileBase64))
            {
                autorizzazione.RiferimentoDocumentoAutorizzazione = await UploadDocumentoRAD3(idRisorsa, autorizzazione.AnagraficaIspettorato, documentoAutorizzazione, protocolloUscitaDto.CodiceProtocolloInUscita, protocolloUscitaDto.DataProtocolloInUscita, protocolloUscitaDto.DataRilascioAutorizzazione);
            }

            return await _autorizzazioneDal.SetProtocolloUscitaAsync(autorizzazione.Id, DateTimeHelper.ParseDataInput(protocolloUscitaDto.DataProtocolloInUscita), protocolloUscitaDto.CodiceProtocolloInUscita, DateTimeHelper.ParseDataInput(protocolloUscitaDto.DataRilascioAutorizzazione), autorizzazione.RiferimentoDocumentoAutorizzazione);
        }

        public async Task<List<ScadenzaAutorizzazioneDto>> SetScadenzaAutorizzazioniAsync(bool isReadOnly)
        {
            var result = new List<ScadenzaAutorizzazioneDto>();

            var autorizzazioniInScadenza = await _autorizzazioneDal.GetAutorizzazioniDaSettareInScadenzaAsync();
            var autorizzazioniScadute = await _autorizzazioneDal.GetAutorizzazioniDaSettareScaduteAsync();

            if (autorizzazioniInScadenza.Any())
            {
                if (!isReadOnly)
                {
                    _ = await _autorizzazioneDal.UpdateStatoAutorizzazioniByIdAsync((short)EnumStatoAutorizzazione.InScadenza, autorizzazioniInScadenza.Select(a => a.ID_AUTORIZZAZIONE).ToList());
                }

                var dto = _mapper.Map<List<ScadenzaAutorizzazioneDto>>(autorizzazioniInScadenza);
                dto.ForEach(a =>
                {
                    a.StatoAutorizzazione = EnumStatoAutorizzazione.InScadenza.GetAttributeDescription();
                    a.Updated = !isReadOnly;
                });
                result.AddRange(dto);
            }

            if (autorizzazioniScadute.Any())
            {
                if (!isReadOnly)
                {
                    _ = await _autorizzazioneDal.UpdateStatoAutorizzazioniByIdAsync((short)EnumStatoAutorizzazione.Scaduta, autorizzazioniScadute.Select(a => a.ID_AUTORIZZAZIONE).ToList());
                }

                var dto = _mapper.Map<List<ScadenzaAutorizzazioneDto>>(autorizzazioniScadute);
                dto.ForEach(a =>
                {
                    a.StatoAutorizzazione = EnumStatoAutorizzazione.Scaduta.GetAttributeDescription();
                    a.Updated = !isReadOnly;
                });
                result.AddRange(dto);
            }

            return result;
        }

        public async Task<int> UpdateAutorizzazioneAsync(AutorizzazioneDto autorizzazione)
        {
            return await _autorizzazioneDal.UpdateAutorizzazioneAsync(_mapper.Map<Autorizzazione>(autorizzazione));
        }

        public async Task<int> UpdateVisibilitaAttestatoAsync(int idAutorizzazione, bool isAttestatoVisibileFE)
        {
            return await _autorizzazioneDal.UpdateVisibilitaAttestatoByIdAsync(idAutorizzazione, isAttestatoVisibileFE);
        }

        public async Task<int> UpdateIdRadioamatoreAsync(int idRadioamatore, int idAutorizzazione)
        {
            return await _autorizzazioneDal.UpdateIdRadioamatoreByIdAsync(idRadioamatore, idAutorizzazione);
        }

        public async Task<int> UploadDocumentoAutorizzazioneAsync(string idRisorsa, AutorizzazioneDto dto)
        {
            var autorizzazione = await GetAutorizzazioneByIdAsync(dto.Id);

            var documentoAutorizzazione = dto.Documenti?.FirstOrDefault(doc => doc.TipoDocumento.EqualsIgnoreCaseAndTrim(TipoDocumentoAutorizzazione.AUTORIZZAZIONE_GENERALE.ToString()));
            if (documentoAutorizzazione != null && !string.IsNullOrWhiteSpace(documentoAutorizzazione.FileBase64))
            {
                if (!string.IsNullOrWhiteSpace(autorizzazione.RiferimentoDocumentoAutorizzazione))
                {
                    _ = await DeleteAllegatoByBlobNameAsync(CODICE_MISURA_RAD3, autorizzazione.RiferimentoDocumentoAutorizzazione);
                }

                autorizzazione.RiferimentoDocumentoAutorizzazione = await UploadDocumentoRAD3(idRisorsa, autorizzazione.AnagraficaIspettorato, documentoAutorizzazione, dto.CodiceProtocolloInUscita, dto.DataProtocolloInUscita, dto.DataRilascio, true);
            }

            return await _autorizzazioneDal.UpdateRiferimentoDocumentoByIdAsync(dto.Id, autorizzazione.RiferimentoDocumentoAutorizzazione);
        }

        public async Task<List<RadioamatoreSRDto>> GetRadioamatoriAndAutorizzazioniSRByFilterAsync(RadioamatoreSRFilterDto filtro)
        {
            var entities = await _autorizzazioneSRDal.GetRadioamatoriSRAndAutorizzazioniByFilterAsync(filtro.Cognome,
                                                                                        filtro.Nome,
                                                                                        filtro.Denominazione,
                                                                                        filtro.CodiceFiscale,
                                                                                        filtro.DataDiNascita,
                                                                                        filtro.Regione,
                                                                                        filtro.Provincia,
                                                                                        filtro.IdIspettorato,
                                                                                        filtro.StatiNominativo,
                                                                                        filtro.DataRilascio,
                                                                                        filtro.Nominativo);
            return this._mapper.Map<List<RadioamatoreSRDto>>(entities);
        }

        public async Task<List<RadioamatoreSRDto>> GetRadiomatoriSRByFilter(RadioamatoreSRFilterDto filtro)
        {
            var entities = await _autorizzazioneSRDal.GetRadiomatoriSRByFilter(filtro);
            return this._mapper.Map<List<RadioamatoreSRDto>>(entities);
        }

        public async Task<List<RadioamatoreSRDto>> GetRadiomatoriSRByIspettoratoFilter(RadioamatoreSRFilterDtoBase filtro, string idIspettorato)
        {
            var entities = await _autorizzazioneSRDal.GetRadiomatoriSRByIspettoratoFilter(filtro, idIspettorato);
            return this._mapper.Map<List<RadioamatoreSRDto>>(entities);
        }


        #endregion

        #region Autorizzazioni SR

        public async Task<SuffissoSRDto> GetSuffissoByPrefissoAndSuffissoAsync(string prefisso, string suffisso)
        {
            var entity = await _autorizzazioneSRDal.GetSuffissoByPrefissoAndSuffissoAsync(prefisso, suffisso);
            return _mapper.Map<SuffissoSRDto>(entity);
        }


        public async Task<ResponseInsertAutorizzazioneDto> InsertAutorizzazioneSRCompletaAsync(AutorizzazioneSRCompletaDto autorizzazioneCompleta, Operation operation, RequestOrigin origin)
        {
            var stazioneEntity = _mapper.Map<StazioneRipetitrice>(autorizzazioneCompleta.StazioneRipetitrice);
            var autorizzazioneDto = _mapper.Map<AutorizzazioneSRDto>(autorizzazioneCompleta.Autorizzazione);
            var radioamatoreTrovato = false;

            //Se viene da GEA è in stato "attivo", altrimenti è "in istrutttoria" se è una nuova autorizzazione o "rinnovo in istruttoria" se è un rinnovo
            autorizzazioneDto.IdStatoAutorizzazione = origin == RequestOrigin.GEA ? (int)EnumStatoAutorizzazione.Attiva : autorizzazioneDto.Rinnovo == true ? (int)EnumStatoAutorizzazione.Rinnovo : (int)EnumStatoAutorizzazione.InIstruttoria;
            autorizzazioneDto.Utente = origin == RequestOrigin.GEA ? autorizzazioneDto.Utente : "RDS";

            if (!string.IsNullOrEmpty(autorizzazioneCompleta.Radioamatore.CodiceNominativo))
            {
                var radioamatoreEsistente = await _autorizzazioneSRDal.GetRadioamatoreSRByCodiceNominativoAsync(autorizzazioneCompleta.Radioamatore.CodiceNominativo);
                if (radioamatoreEsistente != null && radioamatoreEsistente.STATO_NOMINATIVO.EqualsIgnoreCaseAndTrim(EnumStatiRadioamatori.ATTIVO.ToString()))
                {
                    autorizzazioneDto.IdRadioamatoreSR = radioamatoreEsistente.ID_RADIOAMATORE_SR;
                    radioamatoreTrovato = true;
                }
            }

            if (!radioamatoreTrovato)
            {
                autorizzazioneCompleta.Radioamatore.StatoNominativo = (origin == RequestOrigin.GEA || autorizzazioneCompleta.Autorizzazione.Rinnovo == true) ? 
                                                                        EnumStatiRadioamatori.ATTIVO.ToString() : EnumStatiRadioamatori.IN_ISTRUTTORIA.ToString();
                if (origin != RequestOrigin.GEA)
                {
                    autorizzazioneCompleta.Radioamatore.CodiceDomanda = autorizzazioneCompleta.Autorizzazione.CodiceDomanda;
                }

                var codiceRegione = stazioneEntity.CODICE_REGIONE.Equals("BAS") ? stazioneEntity.CODICE_PROVINCIA : stazioneEntity.CODICE_REGIONE;
                autorizzazioneCompleta.Radioamatore.StatoNominativo = (autorizzazioneCompleta.Autorizzazione.Rinnovo ?? false) ? EnumStatoNominativo.ATTIVO.ToString() : EnumStatoNominativo.IN_ISTRUTTORIA.ToString();
                var nuovoRadioamatore = await GeneraNominativoSR(autorizzazioneCompleta.Radioamatore, codiceRegione, string.IsNullOrWhiteSpace(autorizzazioneCompleta.Radioamatore.CodiceNominativo));
                autorizzazioneDto.IdRadioamatoreSR = nuovoRadioamatore.ID_RADIOAMATORE_SR;
            }

            var idStazione = await _autorizzazioneSRDal.InsertStazioneRipetitriceAsync(stazioneEntity);
            autorizzazioneDto.IdStazioneRipetitrice = idStazione;

            var response = await InsertAutorizzazioneSRAsync(autorizzazioneDto);
            return response;
        }

        public async Task<RadioamatoreSR> GeneraNominativoSR(RadioamatoreSRDto dto, string codiceRegione, bool generaNominativo = true)
        {
            var radioamatoreEntity = _mapper.Map<RadioamatoreSR>(dto);

            lock (LOCK)
            {
                string nominativo;
                SuffissoSR suffissoSR = new SuffissoSR();
                if (generaNominativo)
                {
                    var prefisso = _autorizzazioneSRDal.GetPrefissoByCodiceRegioneAsync(codiceRegione).GetAwaiter().GetResult();
                    suffissoSR = _autorizzazioneSRDal.GetSuffissoByPrefissoAsync(prefisso).GetAwaiter().GetResult();
                    nominativo = $"{prefisso}{suffissoSR.SUFFISSO}";
                }
                else
                {
                    suffissoSR = _autorizzazioneSRDal.GetSuffissoByPrefissoAndSuffissoAsync(dto.CodiceNominativo.Substring(0, 3), dto.CodiceNominativo.Substring(3)).GetAwaiter().GetResult();
                    nominativo = dto.CodiceNominativo;
                }
                radioamatoreEntity.CODICE_NOMINATIVO = nominativo;
                radioamatoreEntity.ID_SUFFISSO_SR = suffissoSR?.ID_SUFFISSO_SR;
                var result = _autorizzazioneSRDal.InsertNominativoSRAsync(radioamatoreEntity, suffissoSR?.ID_SUFFISSO_SR).GetAwaiter().GetResult();
                return result;
            }
        }

        public async Task<int> InsertStazioneRipetitriceAsync(StazioneRipetitriceDto dto)
        {
            return await _autorizzazioneSRDal.InsertStazioneRipetitriceAsync(_mapper.Map<StazioneRipetitrice>(dto));
        }

        public async Task<ResponseInsertAutorizzazioneDto> InsertAutorizzazioneSRAsync(AutorizzazioneSRDto dto)
        {
            if ((dto.Rinnovo ?? false) && dto.IdRadioamatoreSR.HasValue)
            {
                var radioamatoreSR = await _autorizzazioneSRDal.GetRadioamatoriSRByIdAsync(dto.IdRadioamatoreSR.Value);

                if (radioamatoreSR != null)
                {
                    var filter = new AutorizzazioneSRFilterDto()
                    {
                        IdStatoAutorizzazione = (int)EnumStatoAutorizzazione.InScadenza,
                        CodiceFiscale = dto.CodiceFiscale,
                        IdTipologiaAutorizzazione = dto.IdTipologiaAutorizzazione,
                        NominativoStazione = radioamatoreSR.CODICE_NOMINATIVO
                    };

                    var autorizzazioneDaRinnovare = (await GetAutorizzazioneSRByFilterAsync(filter)).LastOrDefault();
                    if (autorizzazioneDaRinnovare != null)
                    {
                        dto.IdAutorizzazioneRinnovata = autorizzazioneDaRinnovare.Id;
                        dto.NumeroAutorizzazione = autorizzazioneDaRinnovare.NumeroAutorizzazione;
                    }
                }
            }

            dto.NumeroAutorizzazione = (string.IsNullOrWhiteSpace(dto.NumeroAutorizzazione)) ? await GeneraNumeroAutorizzazioneSR() : dto.NumeroAutorizzazione;

            var response = new ResponseInsertAutorizzazioneDto()
            {
                IdAutorizzazione = await _autorizzazioneSRDal.InsertAutorizzazioneSRAsync(_mapper.Map<AutorizzazioneSR>(dto)),
                NumeroAutorizzazione = dto.NumeroAutorizzazione
            };
            return response;
        }

        public async Task<ResponseInsertAutorizzazioneDto> InsertAutorizzazioneSRAndStazioneAsync(AutorizzazioneSRDto dto)
        {
            var idStazione = await InsertStazioneRipetitriceAsync(dto.StazioneRipetritrice);

            dto.IdStazioneRipetitrice = idStazione;
            dto.IdStatoAutorizzazione = (int)EnumStatoAutorizzazione.Attiva;
            dto.IdTipologiaAutorizzazione = (int)EnumTipoAutorizzazione.StazioneRipetitrice;

            return await InsertAutorizzazioneSRAsync(dto);
        }

        public async Task<List<string>> GetNominativiAutorizzazioniSRInScadenzaByCodiceFiscale(string codiceFiscale)
        {
            return await _autorizzazioneSRDal.GetNominativiAutorizzazioniSRInScadenzaByCodiceFiscale(codiceFiscale);
        }

        public async Task<int> SetProtocolloAutorizzazioneSRAsync(string codiceDomandaFE, DateTime dataProtocollo, string protocollo)
        {
            return await _autorizzazioneSRDal.SetProtocolloAsync(codiceDomandaFE, dataProtocollo, protocollo);
        }

        public async Task<int> SetProtocolloUscitaAutorizzazioneSRAsync(ProtocolloUscitaDto protocolloUscitaDto)
        {
            var autorizzazione = await GetAutorizzazioneSRByIdAsync(protocolloUscitaDto.IdAutorizzazione.Value);

            return await _autorizzazioneSRDal.SetProtocolloUscitaAsync(autorizzazione.Id.Value, DateTimeHelper.ParseDataInput(protocolloUscitaDto.DataProtocolloInUscita), protocolloUscitaDto.CodiceProtocolloInUscita, DateTimeHelper.ParseDataInput(protocolloUscitaDto.DataRilascioAutorizzazione));
        }

        public async Task<List<ScadenzaAutorizzazioneSRDto>> SetScadenzaAutorizzazioniSRAsync(bool isReadOnly)
        {
            var result = new List<ScadenzaAutorizzazioneSRDto>();

            var autorizzazioniInScadenza = await _autorizzazioneSRDal.GetAutorizzazioniSRDaSettareInScadenzaAsync();
            var autorizzazioniScadute = await _autorizzazioneSRDal.GetAutorizzazioniSRDaSettareScaduteAsync();

            if (autorizzazioniInScadenza.Any())
            {
                if (!isReadOnly)
                {
                    _ = await _autorizzazioneSRDal.UpdateStatoAutorizzazioniSRByIdAsync((short)EnumStatoAutorizzazione.InScadenza, autorizzazioniInScadenza.Select(a => a.ID_AUTORIZZAZIONE_SR.Value).ToList());
                }

                var dto = _mapper.Map<List<ScadenzaAutorizzazioneSRDto>>(autorizzazioniInScadenza);
                dto.ForEach(a =>
                {
                    a.StatoAutorizzazione = EnumStatoAutorizzazione.InScadenza.GetAttributeDescription();
                    a.Updated = !isReadOnly;
                });
                result.AddRange(dto);
            }

            if (autorizzazioniScadute.Any())
            {
                if (!isReadOnly)
                {
                    _ = await _autorizzazioneSRDal.UpdateStatoAutorizzazioniSRByIdAsync((short)EnumStatoAutorizzazione.Scaduta, autorizzazioniScadute.Select(a => a.ID_AUTORIZZAZIONE_SR.Value).ToList());
                }

                var dto = _mapper.Map<List<ScadenzaAutorizzazioneSRDto>>(autorizzazioniScadute);
                dto.ForEach(a =>
                {
                    a.StatoAutorizzazione = EnumStatoAutorizzazione.Scaduta.GetAttributeDescription();
                    a.Updated = !isReadOnly;
                });
                result.AddRange(dto);
            }

            return result;
        }

        public async Task<List<ScadenzaNominativoSRDto>> SetScadenzaNominativiSRAsync(bool isReadOnly)
        {
            var result = new List<ScadenzaNominativoSRDto>();

            var nominativiScaduti = await _autorizzazioneSRDal.GetNominativiSRDaSettareScadutiAsync();

            if (nominativiScaduti.Any())
            {
                if (!isReadOnly)
                {
                    _ = await _autorizzazioneSRDal.SetNominativiSRScadutiByIdAsync(nominativiScaduti);
                }

                result = _mapper.Map<List<ScadenzaNominativoSRDto>>(nominativiScaduti);
                result.ForEach(n =>
                {
                    n.StatoNominativo = EnumStatiRadioamatori.SCADUTO.ToString();
                    n.Updated = !isReadOnly;
                });
            }

            return result;
        }

        public async Task<int> GestisciCambioStatoAutorizzazioneSRAsync(AutorizzazioneSRDto autorizzazioneSrDto, EnumStatoAutorizzazione statoAutorizzazione)
        {
            if (statoAutorizzazione == EnumStatoAutorizzazione.Cancellata)
            {
                var autorizzazione = await _autorizzazioneSRDal.GetAutorizzazioniSRByIdAsync(autorizzazioneSrDto.Id.Value);
                if (autorizzazione.ID_RADIOAMATORE_SR.HasValue && autorizzazione.ID_RADIOAMATORE_SR.Value != default)
                {
                    _ = _autorizzazioneSRDal.UpdateStatoNominativoSRByIdAsync(EnumStatoNominativo.ANNULLATO.ToString(), autorizzazione.ID_RADIOAMATORE_SR.Value);
                }
            }

            return await _autorizzazioneSRDal.UpdateStatoAutorizzazioneSRByIdAsync((short)statoAutorizzazione, autorizzazioneSrDto.Id.Value);
        }

        public async Task<int> GestisciCambioStatoNominativoSRAsync(RadioamatoreSRDto radioamatoreSrDto, EnumStatoNominativo statoNominativo)
        {
            return await _autorizzazioneSRDal.UpdateStatoNominativoSRByIdAsync(statoNominativo.ToString(), radioamatoreSrDto.IdRadioamatore);
        }


        public async Task<RadioamatoreSRDto> GetRadioamatoreSRByIdAsync(int idRadioamatore)
        {
            return this._mapper.Map<RadioamatoreSRDto>((await this._autorizzazioneSRDal.GetRadioamatoriSRByIdAsync(idRadioamatore)));
        }

        public async Task<int> UpdateIdRadioamatoreSRAsync(int idRadioamatore, int idAutorizzazione)
        {
            return await _autorizzazioneSRDal.UpdateIdRadioamatoreByIdAsync(idRadioamatore, idAutorizzazione);
        }


        #endregion

        #region Iscrizione elenco SWL

        public async Task<IscrizioneSWLDto> GetIscrizioneSWLByIdAsync(int idIscrizioneSWL)
        {
            var entity = await _iscrizioneSWLDal.GetIscrizioneSWLByIdAsync(idIscrizioneSWL);
            return _mapper.Map<IscrizioneSWLDto>(entity);
        }

        public async Task<List<IscrizioneSWLDto>> GetIscrizioniSWLByFilterAsync(IscrizioneSWLFilterDto filtro)
        {
            var entities = await _iscrizioneSWLDal.GetIscrizioniSWLByFilterAsync(filtro);
            return _mapper.Map<List<IscrizioneSWLDto>>(entities);
        }

        public async Task<List<IscrizioneSWLDto>> GetIscrizioniSWLByIspettoratoConGestioneSubentriAsync(IscrizioneSWLFilterDtoBase filtro, string idIspettorato)
        {
            var entities = await _iscrizioneSWLDal.GetIscrizioniSWLByIspettoratoConGestioneSubentriAsync(filtro, idIspettorato);
            return _mapper.Map<List<IscrizioneSWLDto>>(entities);
        }

        public async Task<int> InsertIscrizioneSWLAsync(string idRisorsa, IscrizioneSWLDto iscrizione, RequestOrigin requestOrigin)
        {
            var statoIscrizioneSWL = EnumStatoIscrizioneSWL.InIstruttoria;

            iscrizione.StatoIscrizioneSWL = new StatoIscrizioneSWLDto { Id = (int)statoIscrizioneSWL, Descrizione = statoIscrizioneSWL.GetAttributeDescription() };
            iscrizione.IdStatoIscrizioneSWL = (int)statoIscrizioneSWL;
            iscrizione.IdAnagraficaIspettorato = (await _ispettoratoDal.GetIspettoratoBySiglaRegioneAsync(iscrizione.CodiceRegioneResidenza)).ID_ANAGRAFICA_ISPETTORATO;
            iscrizione.Utente = requestOrigin == RequestOrigin.GEA ? iscrizione.Utente : "RDI";

            return await _iscrizioneSWLDal.InsertIscrizioneSWLAsync(_mapper.Map<IscrizioneSWL>(iscrizione));
        }

        public async Task<int> SetProtocolloIscrizioneSWLAsync(string codiceDomandaFE, DateTime dataProtocollo, string protocollo, string numeroIscrizioneSWL)
        {
            return await _iscrizioneSWLDal.SetProtocolloAsync(codiceDomandaFE,
                                                                dataProtocollo,
                                                                protocollo,
                                                                numeroIscrizioneSWL);
        }

        public async Task<int> SetProtocolloUscitaIscrizioneSWLAsync(string idRisorsa, ProtocolloUscitaDto protocolloUscitaDto)
        {
            var iscrizione = await GetIscrizioneSWLByIdAsync(protocolloUscitaDto.IdIscrizioneSWL.Value);

            var attestatoAscolto = protocolloUscitaDto.Documenti?.FirstOrDefault(doc => doc.TipoDocumento.EqualsIgnoreCaseAndTrim(TipoDocumentoIscrizioneSWL.ATTESTATO_ASCOLTO.ToString()));
            if (attestatoAscolto != null && !string.IsNullOrWhiteSpace(attestatoAscolto.FileBase64))
            {
                iscrizione.RiferimentoDocumentoIscrizione_SWL = await UploadDocumentoRAD5(idRisorsa, iscrizione.Ispettorato, attestatoAscolto, protocolloUscitaDto.CodiceProtocolloInUscita, protocolloUscitaDto.DataProtocolloInUscita, protocolloUscitaDto.DataRilascioIscrizioneSWL, true);
            }

            return await _iscrizioneSWLDal.SetProtocolloUscitaAsync(iscrizione.Id.Value, DateTimeHelper.ParseDataInput(protocolloUscitaDto.DataProtocolloInUscita), protocolloUscitaDto.CodiceProtocolloInUscita, DateTimeHelper.ParseDataInput(protocolloUscitaDto.DataProtocolloInUscita), iscrizione.RiferimentoDocumentoIscrizione_SWL);
        }

        public async Task<int> GestisciCambioStatoIscrizioneSWLAsync(IscrizioneSWLDto iscrizioneDto, EnumStatoIscrizioneSWL statoIscrizione)
        {
            return await _iscrizioneSWLDal.UpdateStatoIscrizioneSWLByCodiceAsync((int)statoIscrizione, iscrizioneDto.Id.Value);
        }

        #endregion

        #region Metodi privati

        //Nominativi
        private static void ControlloNominativoPerGenerazionePerChiamataPerManifestazioniOContest(EsitoValidazioneDto esito, List<RadioamatoreDto> radioamatoriPerNominativo, string codiceFiscale)
        {

            //if (radioamatoriPerCF.Count > 0)
            //{

            // controllo che nominativo sia associato solo al CF in questione                    
            if (radioamatoriPerNominativo.Count > 0)
            {
                // controllo che nominativo sia in stato attivo
                var nominativo = radioamatoriPerNominativo.First();
                if (!nominativo.StatoNominativo.ToLower().Contains("attivo"))
                {
                    esito.Successo = false;
                    esito.ProseguiSuFEAbilitato = false;
                    esito.CodiceNominativo = nominativo.CodiceNominativo;
                    esito.StatoNominativo = nominativo.StatoNominativo;
                    esito.Errori.Add($"Il nominativo di chiamata indicato non è attivo. Non è possibile procedere con la richiesta.");
                }
                else if (nominativo.CodiceFiscale != codiceFiscale)
                {
                    esito.Successo = false;
                    esito.ProseguiSuFEAbilitato = false;
                    esito.CodiceNominativo = nominativo.CodiceNominativo;
                    esito.StatoNominativo = nominativo.StatoNominativo;
                    esito.Errori.Add($"Il nominativo di chiamata indicato non è associato al codice fiscale del firmatario. Non è possibile procedere con la richiesta.");
                }
            }
            else
            {
                esito.Successo = false;
                esito.ProseguiSuFEAbilitato = false;
                esito.Errori.Add($"Il nominativo di chiamata indicato non è associato al codice fiscale del firmatario. Non è possibile procedere con la richiesta.");
            }

            //}
            //else
            //{
            //    esito.Successo = false;
            //    esito.ProseguiSuFEAbilitato = false;
            //    esito.Errori.Add($"Il codice fiscale {codiceFiscale} non corrisponde a nessun record");

            //}
        }

        private async Task<string> GeneraCodiceNominativoContestAsync(string siglaRegione, string siglaProvincia)
        {
            // controllare se non esiste già il codiceNominativoContest sulla banca dati, altrimenti è necessario generare un nuovo codice
            List<Radioamatore> check;
            int maxTentativi = 30;
            string codiceNominativoContest;
            do
            {
                // Per gestire il fatto che la regione Basillicata non ha un prefisso speciale poiché ne esiste uno per la provincia di matera e uno per la provincia di potenza
                PrefissoSpeciale prefissoSpeciale = await this._prefissiSpecialiDal.GetPrefissoNominativoSpecialiBySiglaRegioneAsync("BAS" != siglaRegione ? siglaRegione : siglaProvincia);
                if (prefissoSpeciale == null)
                {
                    throw new Exception($"Prefisso speciale non trovato per la sigla regione {siglaRegione}");
                }

                codiceNominativoContest = $"I{RandomSecondoCarattereNominativoContest()}{prefissoSpeciale.CODICE_PREFISSO}{RandomLettera()}";

                check = await this._radioamatoreDal.GetRadioamatoriByCodiceNominantivoAsync(codiceNominativoContest);

                maxTentativi--;
            }
            while (check != null && check.Any() && maxTentativi > 0);

            if (maxTentativi <= 0)
            {
                throw new Exception("Numero massimo di tentativi raggiunto per generare un codice nomiativo contest;");
            }

            return codiceNominativoContest;
        }

        private async Task<string> GeneraCodiceNominativoAssociazioneAsync(string siglaRegione, string siglaProvincia)
        {
            // controllare se non esiste già il codiceNominativoContest sulla banca dati, altrimenti è necessario generare un nuovo codice
            List<Radioamatore> check;
            int maxTentativi = 30;
            string codiceNominativoStandardAssociazione;
            do
            {
                // Per gestire il fatto che la regione Basillicata non ha un prefisso speciale poiché ne esiste uno per la provincia di matera e uno per la provincia di potenza
                PrefissoSpeciale prefissoSpeciale = await this._prefissiSpecialiDal.GetPrefissoNominativoSpecialiBySiglaRegioneAsync("BAS" != siglaRegione ? siglaRegione : siglaProvincia);
                if (prefissoSpeciale == null)
                {
                    throw new Exception($"Prefisso speciale non trovato per la sigla regione {siglaRegione}");
                }

                codiceNominativoStandardAssociazione = $"IQ{prefissoSpeciale.CODICE_PREFISSO}{RandomLettera()}{RandomLettera()}{RandomLettera()}";

                check = await this._radioamatoreDal.GetRadioamatoriByCodiceNominantivoAsync(codiceNominativoStandardAssociazione);

                maxTentativi--;
            }
            while (check != null && check.Any() && maxTentativi > 0);

            if (maxTentativi <= 0)
            {
                throw new Exception("Numero massimo di tentativi raggiunto per generare un codice nomiativo;");
            }

            return codiceNominativoStandardAssociazione;
        }

        private async Task<string> GeneraCodiceNominativoManifestazioneAsync(int idTipoManifestazione, string siglaRegione)
        {
            // controllare se non esiste già il codiceNominativoContest sulla banca dati, altrimenti è necessario generare un nuovo codice
            List<Radioamatore> check;
            int maxTentativi = 30;
            string codiceNominativoManifestazione;
            do
            {
                string prefissoParte1 = idTipoManifestazione == 1 // Associazione
                    ? "IQ"
                    : idTipoManifestazione == 2 // Marconiana
                        ? "IY"
                        : idTipoManifestazione == 3 // Altro
                            ? RandomPrefissoManifestazioneTipoAltro()
                            : throw new Exception($"idTipomanifestazione {idTipoManifestazione} non riconoscouto");

                var prefissoSpeciale = await this._prefissiSpecialiDal.GetPrefissoNominativoSpecialiBySiglaRegioneAsync(siglaRegione);
                if (prefissoSpeciale == null)
                {
                    throw new Exception($"Prefisso speciale non trovato per la sigla regione {siglaRegione}");
                }

                string suffisso = idTipoManifestazione == 1 // Associazione
                    ? $"{RandomLettera()}{RandomLettera()}"
                    : idTipoManifestazione == 2 // Marconiana
                        ? $"{RandomLettera()}{RandomLettera()}{RandomLettera()}"
                        : idTipoManifestazione == 3 // Altro
                            ? $"{RandomLettera()}{RandomLettera()}{RandomLettera()}{RandomLettera()}"
                            : throw new Exception($"idTipomanifestazione {idTipoManifestazione} non riconoscouto");

                codiceNominativoManifestazione = $"{prefissoParte1}{prefissoSpeciale.CODICE_PREFISSO}{suffisso}";

                check = await this._radioamatoreDal.GetRadioamatoriByCodiceNominantivoAsync(codiceNominativoManifestazione);

                maxTentativi--;
            }
            while (check != null && check.Any() && maxTentativi > 0);

            if (maxTentativi <= 0)
            {
                throw new Exception("Numero massimo di tentativi raggiunto per generare un codice nomiativo contest;");
            }

            return codiceNominativoManifestazione;
        }

        private static string RandomSecondoCarattereNominativoContest()
        {
            const string chars = "PBOI";
            var carattere = new string(Enumerable.Repeat(chars, 1)
                .Select(s => s[new Random(Guid.NewGuid().GetHashCode()).Next(s.Length)]).ToArray());

            return carattere;
        }

        private static string RandomPrefissoManifestazioneTipoAltro()
        {
            return new string[2] { "IR", "II" }[new Random(Guid.NewGuid().GetHashCode()).Next(0, 1)];
        }

        private static char RandomLettera()
        {
            string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
            return chars.ToArray()[new Random(Guid.NewGuid().GetHashCode()).Next(0, chars.Length - 1)];
        }

        //Patenti
        private async Task<string> GeneraCodicePatente(string siglaRegione)
        {
            var regione = await _regioneDal.GetRegioneBySiglaAsync(siglaRegione);
            var seq = (await _patenteDal.GetNextSequenceValue()).ToString();
            var anno = DateTime.Now.ToString("yy");
            return $"PAT-RD_{regione.CODICE_ISTAT}{anno}_{seq}";
        }

        private async Task<string> UploadDocumentoRAD2(string idRisorsa, IspettoratoDto ispettorato, DocumentoDto documento, string codiceProtocolloInUscita = null, string dataProtocolloInUscita = null, string dataRilascioPatente = null)
        {
            var tipoDocumentoPatente = Enum.Parse<TipoDocumentoPatente>(documento.TipoDocumento);
            TipologiaAllegatoBackend tipologiaAllegatoBackend = tipoDocumentoPatente == TipoDocumentoPatente.PATENTE ? TipologiaAllegatoBackend.Patente : TipologiaAllegatoBackend.CertificatoHarec;

            IDictionary<string, string> inputDto = new Dictionary<string, string>()
            {
                { "idTipologiaDocumento", ((int)tipologiaAllegatoBackend).ToString() },
                { "tipologiaDocumento", tipologiaAllegatoBackend.GetAttributeDescription()},
                { "cleanFileName", documento.FileName.PulisciNomeFile()},
                { "estensione", documento.Estensione },
                { "fileStringBase64", documento.FileBase64},
                { "idIspettorato", ispettorato.IdIspettorato },
                { "denominazioneIspettorato", ispettorato.DenominazioneIspettorato}
            };

            if (tipoDocumentoPatente == TipoDocumentoPatente.PATENTE)
            {
                inputDto.Add("dataRilascio", dataRilascioPatente);
                inputDto.Add("codiceProtocolloInUscita", codiceProtocolloInUscita);
                inputDto.Add("dataProtocolloInUscita", dataProtocolloInUscita);
            }

            return await SalvaAllegatoAsync(idRisorsa, "RAD2", TIPO_APPLICAZIONE_RDP, POST_PRESENTAZIONE, inputDto);
        }

        //Autorizzazioni
        private async Task<string> GeneraNumeroAutorizzazione()
        {
            //Esempio: AG_2023_1
            var seq = (await _autorizzazioneDal.GetNextSequenceValue()).ToString();
            var anno = DateTime.Now.ToString("yyyy");
            return $"AG_{seq}_{anno}";
        }

        private async Task<string> UploadDocumentoRAD3(string idRisorsa, IspettoratoDto ispettorato, DocumentoDto documento, string codiceProtocolloInUscita = null, string dataProtocolloInUscita = null, string dataRilascioAutorizzazione = null, bool insertFromBE = false)
        {
            var tipologiaAllegatoBackend = TipologiaAllegatoBackend.AutorizzazioneGenerale;

            IDictionary<string, string> inputDto = new Dictionary<string, string>()
            {
                { "idTipologiaDocumento", ((int)tipologiaAllegatoBackend).ToString() },
                { "tipologiaDocumento", tipologiaAllegatoBackend.GetAttributeDescription()},
                { "cleanFileName", documento.FileName.PulisciNomeFile()},
                { "estensione", documento.Estensione },
                { "fileStringBase64", documento.FileBase64},
                { "idIspettorato", ispettorato.IdIspettorato },
                { "denominazioneIspettorato", ispettorato.DenominazioneIspettorato},
                { "dataRilascio", dataRilascioAutorizzazione},
                { "codiceProtocolloInUscita", codiceProtocolloInUscita},
                { "dataProtocolloInUscita", dataProtocolloInUscita},
                { "autorizzazioneInseritaDaBE", insertFromBE.ToString()}
            };

            return await SalvaAllegatoAsync(idRisorsa, CODICE_MISURA_RAD3, TIPO_APPLICAZIONE_RDA, POST_PRESENTAZIONE, inputDto);
        }

        private async Task<string> GeneraNumeroAutorizzazioneSR()
        {
            var seq = (await _autorizzazioneSRDal.GetNextSequenceValue()).ToString();
            var anno = DateTime.Now.ToString("yyyy");
            return $"AP_{anno}_{seq}";
        }

        //Iscrizioni
        private async Task<string> UploadDocumentoRAD5(string idRisorsa, IspettoratoDto ispettorato, DocumentoDto documento, string codiceProtocolloInUscita = null, string dataProtocolloInUscita = null, string dataRilascioIscrizioneSWL = null, bool insertFromBE = false)
        {
            var tipologiaAllegatoBackend = TipologiaAllegatoBackend.AutorizzazioneGenerale;

            IDictionary<string, string> inputDto = new Dictionary<string, string>()
            {
                { "idTipologiaDocumento", ((int)tipologiaAllegatoBackend).ToString() },
                { "tipologiaDocumento", tipologiaAllegatoBackend.GetAttributeDescription()},
                { "cleanFileName", documento.FileName.PulisciNomeFile()},
                { "estensione", documento.Estensione },
                { "fileStringBase64", documento.FileBase64},
                { "idIspettorato", ispettorato.IdIspettorato },
                { "denominazioneIspettorato", ispettorato.DenominazioneIspettorato},
                { "dataRilascio", dataRilascioIscrizioneSWL},
                { "codiceProtocolloInUscita", codiceProtocolloInUscita},
                { "dataProtocolloInUscita", dataProtocolloInUscita},
                { "iscrizioneInseritaDaBE", insertFromBE.ToString()}
            };

            return await SalvaAllegatoAsync(idRisorsa, CODICE_MISURA_RAD5, TIPO_APPLICAZIONE_RDI, POST_PRESENTAZIONE, inputDto);
        }

        //Allegati
        public async Task<AllegatoBlobDownloadDto> DownloadAllegatoByBlobName(string codiceMisura, string blobName)
        {
            var jObject = await GetAllegatoByBlobNameAsync(codiceMisura, blobName);

            AllegatoBlobDownloadDto allegatoDownloadDTO = new AllegatoBlobDownloadDto()
            {
                Id = jObject.GetValueIgnoreCase<string>("id"),
                FileName = $"{(jObject.GetValueIgnoreCase<string>("cleanFileName")).TrimEnd('.')}.{(jObject.GetValueIgnoreCase<string>("estensione")).TrimStart('.')}",
                StringBase64 = jObject.GetValueIgnoreCase<string>("fileStringBase64"),
                Extension = jObject.GetValueIgnoreCase<string>("estensione")
            };

            return allegatoDownloadDTO;
        }

        private async Task<string> SalvaAllegatoAsync(string idRisorsa, string codiceMisura, string tipoApplicazione, string tipoSoggetto, IDictionary<string, string> inputDto)
        {
            idRisorsa = idRisorsa.Replace('/', '_');

            var response = await Policy
                            .Handle<Exception>((e) =>
                            {
                                if (e is ApiException && ((ApiException)e).StatusCode == System.Net.HttpStatusCode.BadRequest)
                                {
                                    Console.WriteLine("Exit Retry for bad content returned");
                                    return false;
                                }
                                if (e is ApiException && ((ApiException)e).StatusCode == System.Net.HttpStatusCode.Unauthorized)
                                {
                                    Console.WriteLine("Retry after 401 received - generating new token");
                                }
                                return true;

                            })
                            .RetryAsync(NUMBER_OF_RETRIES, async (exception, retryCount) => await Task.Delay(retryCount * 500))
                            .ExecuteAsync(async () => await RestServiceWithNewtonsoftJson.For<IAPIBlobStorage>(
                                                        this._httpClientFactory.HttpClientV1(_conf["ApiBlobStorage"].ToString(), TIMEOUT_SEC))
                                                        .SalvaAllegato(idRisorsa, tipoApplicazione, tipoSoggetto, codiceMisura, inputDto)
                                        ).ConfigureAwait(false);

            return response.SelectToken("fullPath").ToString();
        }

        private async Task<JObject> GetAllegatoByBlobNameAsync(string codiceMisura, string blobName)
        {
            JObject result = null;
            result = await Policy
                            .Handle<Exception>((e) =>
                            {
                                if (e is ApiException && ((ApiException)e).StatusCode == System.Net.HttpStatusCode.BadRequest)
                                {
                                    Console.WriteLine("Exit Retry for bad content returned");
                                    return false;
                                }
                                if (e is ApiException && ((ApiException)e).StatusCode == System.Net.HttpStatusCode.NotFound)
                                {
                                    Console.WriteLine("Exit as file not found");
                                    return false;
                                }
                                if (e is ApiException && ((ApiException)e).StatusCode == System.Net.HttpStatusCode.Unauthorized)
                                {
                                    Console.WriteLine("Retry after 401 received - generating new token");
                                }
                                return true;

                            })
                            .RetryAsync(NUMBER_OF_RETRIES, async (exception, retryCount) => await Task.Delay(retryCount * 500))
                            .ExecuteAsync(async () => await RestServiceWithNewtonsoftJson.For<IAPIBlobStorage>(
                                                        this._httpClientFactory.HttpClientV1(_conf["ApiBlobStorage"].ToString(), TIMEOUT_SEC))
                                                        .GetAllegatoByBlobName(codiceMisura, blobName)
                                        ).ConfigureAwait(false);

            return result;
        }

        private async Task<JObject> DeleteAllegatoByBlobNameAsync(string codiceMisura, string blobName)
        {
            JObject result = null;
            result = await Policy
                            .Handle<Exception>((e) =>
                            {
                                if (e is ApiException && ((ApiException)e).StatusCode == System.Net.HttpStatusCode.BadRequest)
                                {
                                    Console.WriteLine("Exit Retry for bad content returned");
                                    return false;
                                }
                                if (e is ApiException && ((ApiException)e).StatusCode == System.Net.HttpStatusCode.NotFound)
                                {
                                    Console.WriteLine("Exit as file not found");
                                    return false;
                                }
                                if (e is ApiException && ((ApiException)e).StatusCode == System.Net.HttpStatusCode.Unauthorized)
                                {
                                    Console.WriteLine("Retry after 401 received - generating new token");
                                }
                                return true;

                            })
                            .RetryAsync(NUMBER_OF_RETRIES, async (exception, retryCount) => await Task.Delay(retryCount * 500))
                            .ExecuteAsync(async () => await RestServiceWithNewtonsoftJson.For<IAPIBlobStorage>(
                                                        this._httpClientFactory.HttpClientV1(_conf["ApiBlobStorage"].ToString(), TIMEOUT_SEC))
                                                        .DeleteAllegatoByBlobName(codiceMisura, blobName)
                                        ).ConfigureAwait(false);

            return result;
        }

        #endregion

    }
}
