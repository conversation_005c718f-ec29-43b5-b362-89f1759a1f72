using FluentValidation;
using Inv.Framework.Api.Portable.Logger.Configurations;
using Invitalia.Misure.FiltriAPI;
using Invitalia.Misure.Utilities.WebApi.OAuthTokenOtherApi;
using Invitalia.Misure.Utilities.WebApi.Refit.XRM;
using Invitalia.Misure.Utilities.WebApi.Swagger;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Diagnostics;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using MicroSV_Radioamatori.API.Filters;
using MicroSV_Radioamatori.BL.Interfaces;
using MicroSV_Radioamatori.BL.Services;
using MicroSV_Radioamatori.DAL.Interfaces;
using MicroSV_Radioamatori.DAL.Services;
using Newtonsoft.Json.Converters;
using System;
using System.Globalization;
using System.Net;

namespace MicroSV_Radioamatori
{
    public class Startup
    {
        public Startup(IConfiguration configuration)
        {
            Configuration = configuration;
        }

        public IConfiguration Configuration { get; }

        // This method gets called by the runtime. Use this method to add services to the container.
        public void ConfigureServices(IServiceCollection services)
        {
            services.AddControllers()
                .AddNewtonsoftJson(options =>
                {
                    options.SerializerSettings.Converters.Add(new StringEnumConverter());
                    options.SerializerSettings.DateTimeZoneHandling = Newtonsoft.Json.DateTimeZoneHandling.Unspecified;
                    options.SerializerSettings.DateFormatString = "dd/MM/yyyy HH:mm:ss";
                });


            services.AddHttpContextAccessor(); // Permite Using HttpContext Outside An MVC Controller    
            services.AddHealthChecks();
            services.RegisterServicesForSwagger(this.Configuration, "API.Radioamatori", "v1", "API.Radioamatori.xml");

            var loggerConfig = new ConfigureLogger(this.Configuration);
            loggerConfig.RegisterServices(services);

            ValidatorOptions.Global.LanguageManager.Culture = new CultureInfo("it");

            services.AddCors(opt => opt.AddPolicy("AllowAny",
                 builder => builder.AllowAnyOrigin()
                 .AllowAnyMethod()
                 .AllowAnyHeader()
             ));

            services.AddScoped<ApiContext, ApiContext>();
            services.AddSingleton<OAuthTokenOtherApi, OAuthTokenOtherApi>();
            services.AddSingleton<HttpClientFactory, HttpClientFactory>();
            services.AddScoped<TraceControllerActionsFilter>();
            services.AddScoped<IPrefissiSpecialiDal, PrefissiSpecialiDal>();
            services.AddScoped<IIspettoratoDal, IspettoratoDal>();
            services.AddScoped<IZonaMappingDal, ZonaMappingDal>();
            services.AddScoped<IRadioamatoriDal, RadioamatoriDal>();
            services.AddScoped<IContestDal, ContestDal>();
            services.AddScoped<IManifestazioneDal, ManifestazioniDal>();
            services.AddScoped<ISuffissiDal, SuffissiDal>();
            services.AddScoped<IRegioneDal, RegioneDal>();
            services.AddScoped<IRadiamatoreBl, RadioamatoreBl>();
            services.AddScoped<IPatenteDal, PatenteDal>();
            services.AddScoped<IAutorizzazioneDal, AutorizzazioneDal>();
            services.AddScoped<CheckAccessAttachmentFilter, CheckAccessAttachmentFilter>();
            services.AddScoped<IAutorizzazioneSRDal, AutorizzazioneSRDal>();
            services.AddScoped<IIscrizioniSWLDal, IscrizioniSWLDal>();
            services.AddApplicationInsightsTelemetry();

            services.AddAutoMapper(typeof(Startup));
        }

        // This method gets called by the runtime. Use this method to configure the HTTP request pipeline.
        public void Configure(IApplicationBuilder app, IWebHostEnvironment env, ApiContext apiContext, Inv.Framework.Api.Portable.Logger.Interfaces.ILogger logger)
        {
            if (env.IsDevelopment())
            {
                app.UseDeveloperExceptionPage();
                #region Error handling  
                app.UseExceptionHandler(x =>
                {
                    x.Run(async context =>
                    {
                        context.Response.StatusCode = (int)HttpStatusCode.InternalServerError;
                        context.Response.ContentType = "application/json";
                        var ex = context.Features.Get<IExceptionHandlerFeature>();

                        if (ex != null)
                        {
                            var errorjson = new ErrorModel(ex.Error).ToJson();
                            var username = "";
                            var userip = "";
                            apiContext.SetApiContextFromHeaders(context.Request.Headers);
                            System.Guid traceid = System.Guid.NewGuid();
                            ReadUserContext(apiContext, ref username, ref traceid, ref userip);
                            await logger.LogErrore(
                                traceid,
                                username, ex.Error.Message, context.Request.HttpContext.Connection.LocalIpAddress.MapToIPv4().ToString(), userip, context.Request.Path.Value, new { errorjson }
                                );

                            context.Response.ContentType = "application/json";
                            await context.Response.WriteAsync(errorjson).ConfigureAwait(false);

                        }
                    });
                });
                #endregion
            }
            else
            {
                #region Error handling  
                app.UseExceptionHandler(x =>
                {
                    x.Run(async context =>
                    {
                        var ex = context.Features.Get<IExceptionHandlerFeature>();
                        if (ex != null)
                        {
                            var errorjson = new ErrorModel(ex.Error).ToJson();

                            var username = "";
                            var userip = "";
                            apiContext.SetApiContextFromHeaders(context.Request.Headers);
                            System.Guid traceid = System.Guid.NewGuid();
                            ReadUserContext(apiContext, ref username, ref traceid, ref userip);
                            await logger.LogErrore(
                                traceid,
                                username,
                                ex.Error.Message,
                                context.Request.HttpContext.Connection.LocalIpAddress.MapToIPv4().ToString(),
                                userip,
                                context.Request.Path.Value,
                                new { errorjson }
                                );

                        }
                        context.Response.StatusCode = (int)HttpStatusCode.InternalServerError;
                        context.Response.ContentType = "application/json";
                        await context.Response.WriteAsync(string.Empty).ConfigureAwait(false);
                    });
                });
                #endregion
            }

            app.UseHealthChecks("/health");
            app.UseSwagger();
            app.ConfigureAppForSwagger(this.Configuration, "API.Radioamatori", true);

            app.UseHttpsRedirection();

            app.UseRouting();

            app.UseCors("AllowAny");

            app.UseEndpoints(endpoints =>
            {
                endpoints.MapControllers();
            });
        }

        private void ReadUserContext(ApiContext userContext, ref string username, ref Guid traceid, ref string userip)
        {
            if (userContext.UtenteCompilatore != null)
            {
                username = userContext.UtenteCompilatore.Username;
                traceid = userContext.UtenteCompilatore.TraceId;
                userip = userContext.UtenteCompilatore.IpClient;
            }
            else
            {
                username = "EMPTY";
                traceid = Guid.NewGuid();
                userip = "127.0.0.1";
            }
        }
    }
}
