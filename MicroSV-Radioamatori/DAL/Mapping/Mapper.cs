﻿using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System.Collections.Generic;


namespace MicroSV_Radioamatori.DAL.Mapping
{
    public static class Mapper
    {
        public static T EntityFromJObject<T>(JObject json)
        {
            return JsonConvert.DeserializeObject<T>(json.ToString());
        }

        public static IList<T> EntityFromJObjectList<T>(JArray items)
        {
            return JsonConvert.DeserializeObject<IList<T>>(items.ToString());
        }

        public static IList<T> EntityFromJObjectList<T>(IList<JObject> items)
        {
            return JsonConvert.DeserializeObject<IList<T>>(JsonConvert.SerializeObject(items));
        }
    }
}
