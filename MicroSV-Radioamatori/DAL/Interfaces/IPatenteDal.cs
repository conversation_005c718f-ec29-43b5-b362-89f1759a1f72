﻿using MicroSV_Radioamatori.BL.DTOs;
using MicroSV_Radioamatori.DAL.Entities;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace MicroSV_Radioamatori.DAL.Interfaces
{
    public interface IPatenteDal
    {
        Task<List<Patente>> GetPatentiByFilterAsync(PatenteFilterDto parametri);
        Task<List<Patente>> GetPatentiByIspettoratoConGestioneSubentriAsync(PatenteFilterDto parametri);
        Task<Patente> GetPatenteByIdAsync(int idPatente);
        Task<Patente> GetPatenteByCodiceAsync(string codicePatente);
        Task<List<Patente>> GetPatenteByNumeroCartaceaAsync(string numeroPatenteCartacea);
        Task<List<Patente>> GetPatentiByCfAsync(string codiceFiscale);
        Task<string> InsertPatenteAsync(Patente patente);
        Task<int> UpdatePatenteAsync(Patente patente);
        Task<string> UpdateStatoPatenteByCodiceAsync(int idStatoPatente, string codicePatente);
        Task<long> GetNextSequenceValue();
        Task<int> SetProtocolloAsync(string codiceDomandaFE, DateTime dataProtocollo, string numeroProtocollo);
        Task<string> SetProtocolloUscitaAsync(string codicePatente, DateTime dataProtocolloUscita, string codiceProtocolloUscita, DateTime dataRilascioPatente, string riferimentoPatente, string riferimentoHarec);
        Task<int> UpdateRiferimentiDocumenti(int idPatente, string riferimentoDocumentoPatente, string riferimentoCertificatoHAREC);

    }
}
