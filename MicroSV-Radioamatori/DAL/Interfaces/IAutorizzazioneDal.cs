﻿using MicroSV_Radioamatori.BL.DTOs;
using MicroSV_Radioamatori.DAL.Entities;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace MicroSV_Radioamatori.DAL.Interfaces
{
    public interface IAutorizzazioneDal
    {

        Task<List<Autorizzazione>> GetAutorizzazioniByFilterAsync(AutorizzazioneFilterDto parametri);
        Task<List<Autorizzazione>> GetAutorizzazioniByIspettoratoConGestioneSubentriAsync(AutorizzazioneFilterDtoBase parametri, string idIspettorato);
        Task<Autorizzazione> GetAutorizzazioniByIdAsync(int idAutorizzazione);
        Task<List<Autorizzazione>> GetAutorizzazioniByCodiceFiscaleAsync(string codiceFiscale);
        Task<Autorizzazione> GetAutorizzazioneByNumeroAsync(string numeroAutorizzazione);
        Task<List<Autorizzazione>> GetAutorizzazioniDaSettareInScadenzaAsync();
        Task<List<Autorizzazione>> GetAutorizzazioniDaSettareScaduteAsync();
        Task<int> InsertAutorizzazioneAsync(Autorizzazione autorizzazione);
        Task<int> UpdateAutorizzazioneAsync(Autorizzazione autorizzazione);
        Task<int> UpdateStatoAutorizzazioneByIdAsync(short idStatoAutorizzazione, int idAutorizzazione);
        Task<int> UpdateStatoAutorizzazioniByIdAsync(short idStatoAutorizzazione, List<int> listaIdAutorizzazione);
        Task<int> UpdateVisibilitaAttestatoByIdAsync(int idAutorizzazione, bool isAttestatoVisibile);
        Task<int> UpdateIdRadioamatoreByIdAsync(int idRadioamatore, int idAutorizzazione);
        Task<int> UpdateRiferimentoDocumentoByIdAsync(int idAutorizzazione, string riferimentoDocumento);
        Task<long> GetNextSequenceValue();
        Task<int> SetProtocolloAsync(string codiceDomandaFE, DateTime dataProtocollo, string numeroProtocollo);
        Task<int> SetProtocolloUscitaAsync(int numeroAutorizzazione, DateTime dataProtocolloUscita, string codiceProtocolloUscita, DateTime dataRilascioAutorizzazione, string riferimentoAutorizzazione);
    }

}
