﻿using MicroSV_Radioamatori.DAL.Entities;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace MicroSV_Radioamatori.DAL.Interfaces
{
    public interface IIspettoratoDal
    {
        Task<List<AnagraficaIspettorato>> GetAnagraficaIspettoratiAsync(bool? visualizzaAncheDismessi = null);
        Task<IspettoratoRegione> GetIspettoratoBySiglaRegioneAsync(string siglaRegione);
    }
}
