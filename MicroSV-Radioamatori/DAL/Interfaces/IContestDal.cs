﻿using MicroSV_Radioamatori.DAL.Entities;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace MicroSV_Radioamatori.DAL.Interfaces
{
    public interface IContestDal
    {

        Task<bool> EsisteNominativoAttivoSuContest(string codiceDomandaFE, List<string> contest);
        Task<List<AnagraficaContest>> GetAnagraficaContestAsync();        
        Task<List<AnagraficaContest>> GetContestbyRadioamatoreAsync(string idradioamatore);
        Task<List<Contest>> GetContesByListRadioamatoritAsync(List<int> elencoIdRadioamatori);
    }
}
