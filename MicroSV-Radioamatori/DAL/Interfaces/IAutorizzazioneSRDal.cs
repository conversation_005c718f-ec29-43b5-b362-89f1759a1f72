﻿using MicroSV_Radioamatori.BL.DTOs;
using MicroSV_Radioamatori.DAL.Entities;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace MicroSV_Radioamatori.DAL.Interfaces
{
    public interface IAutorizzazioneSRDal
    {
        Task<string> GetPrefissoByCodiceRegioneAsync(string codiceRegione);
        Task<SuffissoSR> GetSuffissoByPrefissoAsync(string prefisso);
        Task<SuffissoSR> GetSuffissoByPrefissoAndSuffissoAsync(string prefisso, string suffisso);
        Task<RadioamatoreSR> InsertNominativoSRAsync(RadioamatoreSR dto, int? idSuffisso);
        Task<int> InsertStazioneRipetitriceAsync(StazioneRipetitrice entity);
        Task<int> InsertAutorizzazioneSRAsync(AutorizzazioneSR dto);
        Task<long> GetNextSequenceValue();
        Task<RadioamatoreSR> GetRadioamatoreSRByCodiceNominativoAsync(string codiceNominativo);
        Task<List<AutorizzazioneSR>> GetAutorizzazioniSRByFilterAsync(AutorizzazioneSRFilterDto parametri);
        Task<List<AutorizzazioneSR>> GetAutorizzazioniSRByIspettoratoConGestioneSubentriAsync(AutorizzazioneSRFilterDtoBase parametri, string idSpettorato);
        Task<AutorizzazioneSR> GetAutorizzazioniSRByIdAsync(int idAutorizzazione);
        Task<RadioamatoreSR> GetRadioamatoriSRByIdAsync(int idRadioamatore);
        Task<List<RadioamatoreSR>> GetRadioamatoriSRAndAutorizzazioniByFilterAsync(string cognome, string nome, string denominazione, string codiceFiscale, DateTime? dataDiNascita, string regione, string provincia, string idIspettorato, List<string> statiNominativo, DateTime? dataRilascio, string nominativo);
        Task<List<RadioamatoreSR>> GetRadiomatoriSRByFilter(RadioamatoreSRFilterDto parametri);
        Task<List<RadioamatoreSR>> GetRadiomatoriSRByIspettoratoFilter(RadioamatoreSRFilterDtoBase parametri, string idIspettorato);
        Task<List<string>> GetNominativiAutorizzazioniSRInScadenzaByCodiceFiscale(string codiceFiscale);
        Task<int> SetProtocolloAsync(string codiceDomandaFE, DateTime dataProtocollo, string numeroProtocollo);
        Task<List<AutorizzazioneSR>> GetAutorizzazioniSRDaSettareInScadenzaAsync();
        Task<List<AutorizzazioneSR>> GetAutorizzazioniSRDaSettareScaduteAsync();
        Task<int> UpdateStatoAutorizzazioniSRByIdAsync(short idStatoAutorizzazione, List<int> listaIdAutorizzazioniSR);
        Task<List<RadioamatoreSR>> GetNominativiSRDaSettareScadutiAsync();
        Task<int> SetNominativiSRScadutiByIdAsync(List<RadioamatoreSR> listaIdRadioamatoriSR);
        Task<int> UpdateStatoNominativoSRByIdAsync(string statoNominativo, int idRadioamatore);
        Task<int> UpdateStatoAutorizzazioneSRByIdAsync(short idStatoAutorizzazione, int idAutorizzazione);
        Task<int> UpdateIdRadioamatoreByIdAsync(int idRadioamatore, int idAutorizzazione);
        Task<int> SetProtocolloUscitaAsync(int idAutorizzazione,
                                                    DateTime dataProtocolloUscita,
                                                    string codiceProtocolloUscita,
                                                    DateTime dataRilascioAutorizzazione);
    }
}
