﻿using System.Collections.Generic;
using System.Threading.Tasks;
using System;
using MicroSV_Radioamatori.DAL.Entities;
using MicroSV_Radioamatori.BL.DTOs;

namespace MicroSV_Radioamatori.DAL.Interfaces
{
    public interface IIscrizioniSWLDal
    {        
        Task<List<IscrizioneSWL>> GetIscrizioniSWLByFilterAsync(IscrizioneSWLFilterDto parametri);
        Task<List<IscrizioneSWL>> GetIscrizioniSWLByIspettoratoConGestioneSubentriAsync(IscrizioneSWLFilterDtoBase parametri, string idIspettorato);
        Task<IscrizioneSWL> GetIscrizioneSWLByIdAsync(int idIscrizioneSWL);
        Task<int> InsertIscrizioneSWLAsync(IscrizioneSWL iscrizioneSWL);
        Task<int> UpdateStatoIscrizioneSWLByCodiceAsync(int idStatoIscrizioneSWL, int idIscrizioneSWL);
        Task<long> GetNextSequenceValue();
        Task<int> SetProtocolloAsync(string codiceDomandaFE, DateTime dataProtocollo, string numeroProtocollo, string numeroIscrizioneSWL);
        Task<int> SetProtocolloUscitaAsync(int idIscrizioneSWL, DateTime dataProtocolloUscita, string codiceProtocolloUscita, DateTime dataRilascio, string riferimentoDocumento);
    }
}
