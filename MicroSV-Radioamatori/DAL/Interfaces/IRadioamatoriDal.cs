﻿using MicroSV_Radioamatori.BL.DTOs;
using MicroSV_Radioamatori.DAL.Entities;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace MicroSV_Radioamatori.DAL.Interfaces
{
    public interface IRadioamatoriDal
    {
        Task<List<Radioamatore>> GetRadioamatoriByCodiceFiscaleAsync(string codiceFiscale, bool filtraStati = true);
        Task<List<Radioamatore>> GetRadioamatoriByCodiceNominantivoAsync(string nominativo);
        Task<Radioamatore> GetRadioamatoreByCodiceDomandaFEAsync(string codiceDomandaFE);
        Task<List<Radioamatore>> GetRadioamatoriByCodiceContestAsync(string codiceContest);
        Task<Radioamatore> GetRadioamatoreByIdAsync(int idRadioamatore);
        Task<List<Radioamatore>> GetRadioamatoriByAnagraficaAsync(string nome, string cognome, string datanascita);
        Task<List<Radioamatore>> GetRadioamatoriAsync();
        Task<List<Radioamatore>> GetRadioamatoriByParametriAsync(ParametriRicercaRadioamatoreDto parametri);
        Task<List<Radioamatore>> GetRadioamatoriByIspettoratoConGestioneSubentriAsync(ParametriRicercaRadioamatoreDto parametri);
        Task<List<Radioamatore>> GetNominativiDaSettareScadutiAsync();
        Task AnnullaNominativoAsync(string codiceDomandaFE);
        Task AnnullaNominativoByIdAsync(int idRadioamatore);
        Task SetProtocolloMisePresentazioneAsync(string codiceDomandaFE, DateTime dataProtocollo, string numeroProtocollo);
        Task<int> GeneraNominativoStandardAsync(Radioamatore radioamatore);
        Task<int> GeneraNominativoManifestazioneAsync(Radioamatore radioamatore, Manifestazione manifestazione);
        Task<int> GeneraNominativoContestAsync(Radioamatore radioamatore, List<Contest> elencoContest);
        Task UpdateCodiceFiscaleCodiceProtocolloDataRilascioAsync(int idRadioamatore, string codiceFiscale, DateTime? dataRilascio);
        Task UpdateDataRilascioAsync(int idRadioamatore, DateTime? dataRilascio);
        Task UpdateCodiceFiscaleAsync(int idRadioamatore, string codiceFiscale);
        Task<int> SetNominativiScadutiByIdAsync(List<Radioamatore> listaIdRadioamatore);
        Task<int> SetProtocolloUscitaAsync(int idRadioamatore,
                                            DateTime dataProtocolloUscita,
                                            string codiceProtocolloUscita,
                                            DateTime dataRilascioNominativo);
    }
}
