﻿using Dapper;
using Microsoft.Extensions.Configuration;
using MicroSV_Radioamatori.DAL.Common;
using MicroSV_Radioamatori.DAL.Entities;
using MicroSV_Radioamatori.DAL.Interfaces;
using System;
using System.Text;
using System.Threading.Tasks;

namespace MicroSV_Radioamatori.DAL.Services
{
    public class SuffissiDal : BaseDal, ISuffissiDal
    {
        public SuffissiDal(IConfiguration conf) : base(conf)
        {
        }

        public async Task<Suffisso> PrenotaSuffissoAsync(string tipo)
        {
            var query1 = new StringBuilder();
            query1.Append($" SELECT TOP 1 * FROM {NomeTabella.Suffissi}");
            query1.Append($" WHERE {nameof(Suffisso.TIPO)} = @tipo");
            query1.Append($" AND {nameof(Suffisso.DISPONIBILE)} = @disponibile");
            query1.Append($" ORDER BY {nameof(Suffisso.SUFFISSO)} ASC");

            var query2 = new StringBuilder();
            query2.Append($" UPDATE {NomeTabella.Suffissi} ");
            query2.Append($" SET {NomeTabella.Suffissi}.{nameof(Suffisso.DISPONIBILE)} = @disponibile ");
            query2.Append($" FROM {NomeTabella.Suffissi} ");
            query2.Append($" WHERE {nameof(Suffisso.ID_SUFFISSO)} = @idSuffisso ");

            using (var connection = CreateConnection())
            {
                connection.Open();
                using (var tran = connection.BeginTransaction())
                {
                    try
                    {
                        var result = (await connection.QueryFirstOrDefaultAsync<Suffisso>(
                            query1.ToString(),
                            new { tipo = tipo, disponibile = 1 },
                            tran));

                        int id = result.ID_SUFFISSO;

                        var ret = await connection.ExecuteAsync(query2.ToString(),
                            new
                            {
                                disponibile = false,
                                idSuffisso = id
                            },
                            tran);

                        tran.Commit();

                        return result;
                    }
                    catch (Exception ex)
                    {
                        tran.Rollback();
                        throw;
                    }
                }
            }

        }

        public async Task RollbackPrenotazioneSuffissoAsync(int idSuffisso)
        {
            var query = new StringBuilder();
            query.Append($" UPDATE {NomeTabella.Suffissi} ");
            query.Append($" SET {NomeTabella.Suffissi}.{nameof(Suffisso.DISPONIBILE)} = @disponibile ");
            query.Append($" FROM {NomeTabella.Suffissi} ");
            query.Append($" WHERE {nameof(Suffisso.ID_SUFFISSO)} = @idSuffisso ");

            using (var connection = CreateConnection())
            {
                var ret = await connection.ExecuteAsync(query.ToString(),
                            new
                            {
                                disponibile = true,
                                idSuffisso = idSuffisso
                            });
            }
        }

    }
}