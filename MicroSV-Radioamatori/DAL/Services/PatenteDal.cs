﻿using Dapper;
using Microsoft.Extensions.Configuration;
using MicroSV_Radioamatori.BL.DTOs;
using MicroSV_Radioamatori.BL.Enums;
using MicroSV_Radioamatori.BL.ValueObject;
using MicroSV_Radioamatori.DAL.Common;
using MicroSV_Radioamatori.DAL.Entities;
using MicroSV_Radioamatori.DAL.Interfaces;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using StatoPatente = MicroSV_Radioamatori.DAL.Entities.StatoPatente;


namespace MicroSV_Radioamatori.DAL.Services
{
    public class PatenteDal : BaseDal, IPatenteDal
    {
        public PatenteDal(IConfiguration conf) : base(conf)
        {

        }

        public async Task<Patente> GetPatenteByIdAsync(int idPatente)
        {
            var query = new StringBuilder();
            query.Append($" SELECT p.*, s.*, i.* FROM {NomeTabella.Patenti} AS p");
            query.Append($" JOIN {NomeTabella.StatiPatente} AS s");
            query.Append($"     ON p.{nameof(Patente.ID_STATO_PATENTE)} = s.{nameof(StatoPatente.ID_STATO_PATENTE)}");
            query.Append($" JOIN {NomeTabella.AnagraficaIspettorati} AS i");
            query.Append($"     ON p.{nameof(Patente.ID_ANAGRAFICA_ISPETTORATO)} = i.{nameof(AnagraficaIspettorato.ID)}");
            query.Append($" WHERE p.{nameof(Patente.ID_PATENTE)} = @Id");



            using (var connection = CreateConnection())
            {
                return connection.Query<Patente, StatoPatente, AnagraficaIspettorato, Patente>(query.ToString(),
                    (p, s, i) =>
                    {
                        p.STATO_PATENTE = s;
                        p.ANAGRAFICA_ISPETTORATO = i;
                        return p;
                    },
                    splitOn: $"{nameof(StatoPatente.ID_STATO_PATENTE)}, {nameof(AnagraficaIspettorato.ID)}",
                    param: new
                    {
                        id = idPatente
                    }
                    ).AsQueryable().FirstOrDefault();
            }
        }

        public async Task<Patente> GetPatenteByCodiceAsync(string codicePatente)
        {
            var query = new StringBuilder();
            query.Append($" SELECT p.*, s.*, i.* FROM {NomeTabella.Patenti} AS p");
            query.Append($" JOIN {NomeTabella.StatiPatente} AS s");
            query.Append($"     ON p.{nameof(Patente.ID_STATO_PATENTE)} = s.{nameof(StatoPatente.ID_STATO_PATENTE)}");
            query.Append($" JOIN {NomeTabella.AnagraficaIspettorati} AS i");
            query.Append($"     ON p.{nameof(Patente.ID_ANAGRAFICA_ISPETTORATO)} = i.{nameof(AnagraficaIspettorato.ID)}");
            query.Append($" WHERE p.{nameof(Patente.CODICE_PATENTE)} = @CodicePatente");



            using (var connection = CreateConnection())
            {
                return connection.Query<Patente, StatoPatente, AnagraficaIspettorato, Patente>(query.ToString(),
                    (p, s, i) =>
                    {
                        p.STATO_PATENTE = s;
                        p.ANAGRAFICA_ISPETTORATO = i;
                        return p;
                    },
                    splitOn: $"{nameof(StatoPatente.ID_STATO_PATENTE)}, {nameof(AnagraficaIspettorato.ID)}",
                    param: new
                    {
                        CodicePatente = codicePatente
                    }
                    ).AsQueryable().FirstOrDefault();
            }
        }

        public async Task<List<Patente>> GetPatenteByNumeroCartaceaAsync(string numeroPatenteCartacea)
        {
            var query = new StringBuilder();
            query.Append($" SELECT p.*, s.*, i.* FROM {NomeTabella.Patenti} AS p");
            query.Append($" JOIN {NomeTabella.StatiPatente} AS s");
            query.Append($"     ON p.{nameof(Patente.ID_STATO_PATENTE)} = s.{nameof(StatoPatente.ID_STATO_PATENTE)}");
            query.Append($" JOIN {NomeTabella.AnagraficaIspettorati} AS i");
            query.Append($"     ON p.{nameof(Patente.ID_ANAGRAFICA_ISPETTORATO)} = i.{nameof(AnagraficaIspettorato.ID)}");
            query.Append($" WHERE p.{nameof(Patente.NUMERO_PATENTE_CARTACEA)} = @NumeroPatenteCartacea");



            using (var connection = CreateConnection())
            {
                return connection.Query<Patente, StatoPatente, AnagraficaIspettorato, Patente>(query.ToString(),
                    (p, s, i) =>
                    {
                        p.STATO_PATENTE = s;
                        p.ANAGRAFICA_ISPETTORATO = i;
                        return p;
                    },
                    splitOn: $"{nameof(StatoPatente.ID_STATO_PATENTE)}, {nameof(AnagraficaIspettorato.ID)}",
                    param: new
                    {
                        NumeroPatenteCartacea = numeroPatenteCartacea
                    }
                    ).AsQueryable().ToList();
            }
        }

        public async Task<List<Patente>> GetPatentiByCfAsync(string codiceFiscale)
        {
            var query = new StringBuilder();
            query.Append($" SELECT p.*, s.*, i.* FROM {NomeTabella.Patenti} AS p");
            query.Append($" JOIN {NomeTabella.StatiPatente} AS s");
            query.Append($"     ON p.{nameof(Patente.ID_STATO_PATENTE)} = s.{nameof(StatoPatente.ID_STATO_PATENTE)}");
            query.Append($" JOIN {NomeTabella.AnagraficaIspettorati} AS i");
            query.Append($"     ON p.{nameof(Patente.ID_ANAGRAFICA_ISPETTORATO)} = i.{nameof(AnagraficaIspettorato.ID)}");
            query.Append($" WHERE UPPER(p.{nameof(Patente.CODICE_FISCALE)}) = UPPER(@CodiceFiscale)");

            using (var connection = CreateConnection())
            {
                return connection.Query<Patente, StatoPatente, AnagraficaIspettorato, Patente>(query.ToString(),
                    (p, s, i) =>
                    {
                        p.STATO_PATENTE = s;
                        p.ANAGRAFICA_ISPETTORATO = i;
                        return p;
                    },
                    splitOn: $"{nameof(StatoPatente.ID_STATO_PATENTE)}, {nameof(AnagraficaIspettorato.ID)}",
                    param: new
                    {
                        CodiceFiscale = codiceFiscale
                    }
                    ).AsQueryable().ToList();
            }
        }

        public async Task<List<Patente>> GetPatentiByFilterAsync(PatenteFilterDto parametri)
        {
            var query = PreparaParametriRicercaPatentiQuery(parametri);

            if (!string.IsNullOrWhiteSpace(parametri.IdIspettorato))
                query.Item1.Append($" AND ( @IdIspettorato IS NULL OR p.{nameof(Patente.ID_ANAGRAFICA_ISPETTORATO)} LIKE '%' + @IdIspettorato + '%') "); // Come prenderlo?

            query.Item2.Add("IdIspettorato", parametri.IdIspettorato);

            using (var connection = CreateConnection())
            {
                return (await connection.QueryAsync<Patente, StatoPatente, AnagraficaIspettorato, Patente>(query.Item1.ToString(),
                        (p, s, i) =>
                        {
                            p.STATO_PATENTE = s;
                            p.ANAGRAFICA_ISPETTORATO = i;
                            return p;
                        },
                        splitOn: $"{nameof(StatoPatente.ID_STATO_PATENTE)}, {nameof(AnagraficaIspettorato.ID)}",
                        param: query.Item2
                        )).AsQueryable().ToList();
            }
        }

        public async Task<List<Patente>> GetPatentiByIspettoratoConGestioneSubentriAsync(PatenteFilterDto parametri)
        {
            var query = PreparaParametriRicercaPatentiQuery(parametri);

            if (!string.IsNullOrWhiteSpace(parametri.IdIspettorato))
                query.Item1.Append($" AND ( @IdIspettorato IS NULL OR i.{nameof(AnagraficaIspettorato.ID)} = @IdIspettorato OR i.{nameof(AnagraficaIspettorato.ID_SUBENTRO)} = @IdIspettorato ) "); // Come prenderlo?

            query.Item2.Add("IdIspettorato", parametri.IdIspettorato);

            using (var connection = CreateConnection())
            {
                return (await connection.QueryAsync<Patente, StatoPatente, AnagraficaIspettorato, Patente>(query.Item1.ToString(),
                        (p, s, i) =>
                        {
                            p.STATO_PATENTE = s;
                            p.ANAGRAFICA_ISPETTORATO = i;
                            return p;
                        },
                        splitOn: $"{nameof(StatoPatente.ID_STATO_PATENTE)}, {nameof(AnagraficaIspettorato.ID)}",
                        param: query.Item2
                        )).AsQueryable().ToList();
            }
        }

        public async Task<string> InsertPatenteAsync(Patente patente)
        {
            var query = new StringBuilder();
            query.Append($" INSERT INTO[dbo].[TB_PATENTI]");
            query.Append($" ([CODICE_PATENTE]");
            query.Append($",[COGNOME]");
            query.Append($",[NOME]");
            query.Append($",[CODICE_FISCALE]");
            query.Append($",[SESSO]");
            query.Append($",[LUOGO_NASCITA]");
            query.Append($",[DATA_NASCITA]");
            query.Append($",[REGIONE_RESIDENZA]");
            query.Append($",[PROVINCIA_RESIDENZA]");
            query.Append($",[COMUNE_RESIDENZA]");
            query.Append($",[INDIRIZZO_RESIDENZA]");
            query.Append($",[CAP_RESIDENZA]");
            query.Append($",[ID_ANAGRAFICA_ISPETTORATO]");
            query.Append($",[PEC]");
            query.Append($",[EMAIL]");
            query.Append($",[TELEFONO]");
            query.Append($",[RIFERIMENTO_DOCUMENTO_PATENTE]");
            query.Append($",[RIFERIMENTO_DOCUMENTO_HAREC]");
            query.Append($",[DATA_RILASCIO]");
            query.Append($",[CODICE_PROTOCOLLO_ENTRATA]");
            query.Append($",[DATA_PROTOCOLLO_ENTRATA]");
            query.Append($",[CODICE_PROTOCOLLO_USCITA]");
            query.Append($",[DATA_PROTOCOLLO_USCITA]");
            query.Append($",[CODICE_DOMANDA]");
            query.Append($",[NUMERO_PATENTE_CARTACEA]");
            query.Append($",[ID_STATO_PATENTE]");
            query.Append($",[UTENTE]");
            query.Append($",[CODICE_LUOGO_NASCITA]");
            query.Append($",[CODICE_REGIONE_RESIDENZA]");
            query.Append($",[CODICE_PROVINCIA_RESIDENZA]");
            query.Append($",[CODICE_COMUNE_RESIDENZA]");
            query.Append(") OUTPUT Inserted.ID_PATENTE VALUES ");

            query.Append($"(@CodicePatente, ");
            query.Append($"@Cognome, ");
            query.Append($"@Nome, ");
            query.Append($"@CodiceFiscale, ");
            query.Append($"@Sesso, ");
            query.Append($"@LuogoNascita, ");
            query.Append($"@DataNascita, ");
            query.Append($"@Regione, ");
            query.Append($"@Provincia, ");
            query.Append($"@Comune, ");
            query.Append($"@Indirizzo, ");
            query.Append($"@Cap, ");
            query.Append($"@IdIspettorato, ");
            query.Append($"@Pec, ");
            query.Append($"@Email, ");
            query.Append($"@Telefono, ");
            query.Append($"@RiferimentoDocPatente, ");
            query.Append($"@RiferimentoDocHarec, ");
            query.Append($"@DataRilascio, ");
            query.Append($"@CodiceProtocolloEntrata, ");
            query.Append($"@DataProtocolloEntrata, ");
            query.Append($"@CodiceProtocolloUscita, ");
            query.Append($"@DataProtocolloUscita, ");
            query.Append($"@CodiceDomanda, ");
            query.Append($"@NumeroPatenteCartacea, ");
            query.Append($"@IdStato, ");
            query.Append($"@Utente, ");
            query.Append($"@CodiceLuogoNascita, ");
            query.Append($"@CodiceRegione, ");
            query.Append($"@CodiceProvincia, ");
            query.Append($"@CodiceComune)");

            var queryTriggerOutput = new StringBuilder();

            queryTriggerOutput.Append($" INSERT INTO [dbo].[TB_PATENTI_TRIGGER_OUTPUT]");
            queryTriggerOutput.Append($" ([ID_PATENTE]");
            queryTriggerOutput.Append($",[CODICE_PATENTE]");
            queryTriggerOutput.Append($",[COGNOME]");
            queryTriggerOutput.Append($",[NOME]");
            queryTriggerOutput.Append($",[CODICE_FISCALE]");
            queryTriggerOutput.Append($",[SESSO]");
            queryTriggerOutput.Append($",[LUOGO_NASCITA]");
            queryTriggerOutput.Append($",[DATA_NASCITA]");
            queryTriggerOutput.Append($",[REGIONE_RESIDENZA]");
            queryTriggerOutput.Append($",[PROVINCIA_RESIDENZA]");
            queryTriggerOutput.Append($",[COMUNE_RESIDENZA]");
            queryTriggerOutput.Append($",[INDIRIZZO_RESIDENZA]");
            queryTriggerOutput.Append($",[CAP_RESIDENZA]");
            queryTriggerOutput.Append($",[ID_ANAGRAFICA_ISPETTORATO]");
            queryTriggerOutput.Append($",[PEC]");
            queryTriggerOutput.Append($",[EMAIL]");
            queryTriggerOutput.Append($",[TELEFONO]");
            queryTriggerOutput.Append($",[RIFERIMENTO_DOCUMENTO_PATENTE]");
            queryTriggerOutput.Append($",[RIFERIMENTO_DOCUMENTO_HAREC]");
            queryTriggerOutput.Append($",[DATA_RILASCIO]");
            queryTriggerOutput.Append($",[CODICE_PROTOCOLLO_ENTRATA]");
            queryTriggerOutput.Append($",[DATA_PROTOCOLLO_ENTRATA]");
            queryTriggerOutput.Append($",[CODICE_PROTOCOLLO_USCITA]");
            queryTriggerOutput.Append($",[DATA_PROTOCOLLO_USCITA]");
            queryTriggerOutput.Append($",[CODICE_DOMANDA]");
            queryTriggerOutput.Append($",[NUMERO_PATENTE_CARTACEA]");
            queryTriggerOutput.Append($",[ID_STATO_PATENTE]");
            queryTriggerOutput.Append($",[UTENTE]");
            queryTriggerOutput.Append($",[CODICE_LUOGO_NASCITA]");
            queryTriggerOutput.Append($",[CODICE_REGIONE_RESIDENZA]");
            queryTriggerOutput.Append($",[CODICE_PROVINCIA_RESIDENZA]");
            queryTriggerOutput.Append($",[CODICE_COMUNE_RESIDENZA]");
            queryTriggerOutput.Append($",[TIMESTAMP_MODIFICA]");
            queryTriggerOutput.Append(") VALUES ");

            queryTriggerOutput.Append($"(@IdPatente, ");
            queryTriggerOutput.Append($"@CodicePatente, ");
            queryTriggerOutput.Append($"@Cognome, ");
            queryTriggerOutput.Append($"@Nome, ");
            queryTriggerOutput.Append($"@CodiceFiscale, ");
            queryTriggerOutput.Append($"@Sesso, ");
            queryTriggerOutput.Append($"@LuogoNascita, ");
            queryTriggerOutput.Append($"@DataNascita, ");
            queryTriggerOutput.Append($"@Regione, ");
            queryTriggerOutput.Append($"@Provincia, ");
            queryTriggerOutput.Append($"@Comune, ");
            queryTriggerOutput.Append($"@Indirizzo, ");
            queryTriggerOutput.Append($"@Cap, ");
            queryTriggerOutput.Append($"@IdIspettorato, ");
            queryTriggerOutput.Append($"@Pec, ");
            queryTriggerOutput.Append($"@Email, ");
            queryTriggerOutput.Append($"@Telefono, ");
            queryTriggerOutput.Append($"@RiferimentoDocPatente, ");
            queryTriggerOutput.Append($"@RiferimentoDocHarec, ");
            queryTriggerOutput.Append($"@DataRilascio, ");
            queryTriggerOutput.Append($"@CodiceProtocolloEntrata, ");
            queryTriggerOutput.Append($"@DataProtocolloEntrata, ");
            queryTriggerOutput.Append($"@CodiceProtocolloUscita, ");
            queryTriggerOutput.Append($"@DataProtocolloUscita, ");
            queryTriggerOutput.Append($"@CodiceDomanda, ");
            queryTriggerOutput.Append($"@NumeroPatenteCartacea, ");
            queryTriggerOutput.Append($"@IdStato, ");
            queryTriggerOutput.Append($"@Utente, ");
            queryTriggerOutput.Append($"@CodiceLuogoNascita, ");
            queryTriggerOutput.Append($"@CodiceRegione, ");
            queryTriggerOutput.Append($"@CodiceProvincia, ");
            queryTriggerOutput.Append($"@CodiceComune, ");
            queryTriggerOutput.Append($"@Timestamp)");


            using (var connection = CreateConnection())
            {
                connection.Open();
                using (var tran = connection.BeginTransaction())
                {
                    try
                    {
                        int idPatente = await connection.ExecuteScalarAsync<int>(
                        query.ToString(),
                        new
                        {
                            CodicePatente = patente.CODICE_PATENTE,
                            Cognome = patente.COGNOME,
                            Nome = patente.NOME,
                            CodiceFiscale = patente.CODICE_FISCALE,
                            Sesso = patente.SESSO,
                            LuogoNascita = patente.LUOGO_NASCITA,
                            DataNascita = patente.DATA_NASCITA,
                            Regione = patente.REGIONE_RESIDENZA,
                            Provincia = patente.PROVINCIA_RESIDENZA,
                            Comune = patente.COMUNE_RESIDENZA,
                            Indirizzo = patente.INDIRIZZO_RESIDENZA,
                            Cap = patente.CAP_RESIDENZA,
                            IdIspettorato = patente.ID_ANAGRAFICA_ISPETTORATO,
                            Pec = patente.PEC,
                            Email = patente.EMAIL,
                            Telefono = patente.TELEFONO,
                            RiferimentoDocPatente = patente.RIFERIMENTO_DOCUMENTO_PATENTE,
                            RiferimentoDocHarec = patente.RIFERIMENTO_DOCUMENTO_HAREC,
                            DataRilascio = patente.DATA_RILASCIO,
                            CodiceProtocolloEntrata = patente.CODICE_PROTOCOLLO_ENTRATA,
                            DataProtocolloEntrata = patente.DATA_PROTOCOLLO_ENTRATA,
                            CodiceProtocolloUscita = patente.CODICE_PROTOCOLLO_USCITA,
                            DataProtocolloUscita = patente.DATA_PROTOCOLLO_USCITA,
                            CodiceDomanda = patente.CODICE_DOMANDA,
                            NumeroPatenteCartacea = patente.NUMERO_PATENTE_CARTACEA,
                            IdStato = patente.ID_STATO_PATENTE,
                            Utente = patente.UTENTE,
                            CodiceLuogoNascita = patente.CODICE_LUOGO_NASCITA,
                            CodiceRegione = patente.CODICE_REGIONE_RESIDENZA,
                            CodiceProvincia = patente.CODICE_PROVINCIA_RESIDENZA,
                            CodiceComune = patente.CODICE_COMUNE_RESIDENZA
                        }, tran);

                        _ = await connection.ExecuteAsync(
                            queryTriggerOutput.ToString(),
                            new
                            {
                                IdPatente = idPatente,
                                CodicePatente = patente.CODICE_PATENTE,
                                Cognome = patente.COGNOME,
                                Nome = patente.NOME,
                                CodiceFiscale = patente.CODICE_FISCALE,
                                Sesso = patente.SESSO,
                                LuogoNascita = patente.LUOGO_NASCITA,
                                DataNascita = patente.DATA_NASCITA,
                                Regione = patente.REGIONE_RESIDENZA,
                                Provincia = patente.PROVINCIA_RESIDENZA,
                                Comune = patente.COMUNE_RESIDENZA,
                                Indirizzo = patente.INDIRIZZO_RESIDENZA,
                                Cap = patente.CAP_RESIDENZA,
                                IdIspettorato = patente.ID_ANAGRAFICA_ISPETTORATO,
                                Pec = patente.PEC,
                                Email = patente.EMAIL,
                                Telefono = patente.TELEFONO,
                                RiferimentoDocPatente = patente.RIFERIMENTO_DOCUMENTO_PATENTE,
                                RiferimentoDocHarec = patente.RIFERIMENTO_DOCUMENTO_HAREC,
                                DataRilascio = patente.DATA_RILASCIO,
                                CodiceProtocolloEntrata = patente.CODICE_PROTOCOLLO_ENTRATA,
                                DataProtocolloEntrata = patente.DATA_PROTOCOLLO_ENTRATA,
                                CodiceProtocolloUscita = patente.CODICE_PROTOCOLLO_USCITA,
                                DataProtocolloUscita = patente.DATA_PROTOCOLLO_USCITA,
                                CodiceDomanda = patente.CODICE_DOMANDA,
                                NumeroPatenteCartacea = patente.NUMERO_PATENTE_CARTACEA,
                                IdStato = patente.ID_STATO_PATENTE,
                                Utente = patente.UTENTE,
                                CodiceLuogoNascita = patente.CODICE_LUOGO_NASCITA,
                                CodiceRegione = patente.CODICE_REGIONE_RESIDENZA,
                                CodiceProvincia = patente.CODICE_PROVINCIA_RESIDENZA,
                                CodiceComune = patente.CODICE_COMUNE_RESIDENZA,
                                Timestamp = DateTime.Now
                            }, tran);
                        tran.Commit();
                        return patente.CODICE_PATENTE;
                    }
                    catch (Exception ex)
                    {
                        tran.Rollback();
                        throw;
                    }
                }
            }
        }

        public async Task<int> UpdatePatenteAsync(Patente patente)
        {
            var query = new StringBuilder();
            query.Append($" UPDATE [dbo].[TB_PATENTI] SET ");
            query.Append($" [CODICE_PATENTE] = @CodicePatente");
            query.Append($",[COGNOME] = @Cognome");
            query.Append($",[NOME] = @Nome");
            query.Append($",[CODICE_FISCALE] = @CodiceFiscale");
            query.Append($",[SESSO] = @Sesso");
            query.Append($",[LUOGO_NASCITA] = @LuogoNascita");
            query.Append($",[DATA_NASCITA] = @DataNascita");
            query.Append($",[REGIONE_RESIDENZA] = @Regione");
            query.Append($",[PROVINCIA_RESIDENZA] = @Provincia");
            query.Append($",[COMUNE_RESIDENZA] = @Comune");
            query.Append($",[INDIRIZZO_RESIDENZA] = @Indirizzo");
            query.Append($",[CAP_RESIDENZA] = @Cap");
            query.Append($",[ID_ANAGRAFICA_ISPETTORATO] = @IdIspettorato");
            query.Append($",[PEC] = @Pec");
            query.Append($",[EMAIL] = @Email");
            query.Append($",[TELEFONO] = @Telefono");
            query.Append($",[RIFERIMENTO_DOCUMENTO_PATENTE] = @RiferimentoDocPatente");
            query.Append($",[RIFERIMENTO_DOCUMENTO_HAREC] = @RiferimentoDocHarec");
            query.Append($",[DATA_RILASCIO] = @DataRilascio");
            query.Append($",[CODICE_PROTOCOLLO_ENTRATA] = @CodiceProtocolloEntrata");
            query.Append($",[DATA_PROTOCOLLO_ENTRATA] = @DataProtocolloEntrata");
            query.Append($",[CODICE_PROTOCOLLO_USCITA] = @CodiceProtocolloUscita");
            query.Append($",[DATA_PROTOCOLLO_USCITA] = @DataProtocolloUscita");
            query.Append($",[CODICE_DOMANDA] = @CodiceDomanda");
            query.Append($",[NUMERO_PATENTE_CARTACEA] = @NumeroPatenteCartacea");
            query.Append($",[ID_STATO_PATENTE] = @IdStato");
            query.Append($",[UTENTE] = @Utente");
            query.Append($",[CODICE_LUOGO_NASCITA] = @CodiceLuogoNascita");
            query.Append($",[CODICE_REGIONE_RESIDENZA] = @CodiceRegione");
            query.Append($",[CODICE_PROVINCIA_RESIDENZA] = @CodiceProvincia");
            query.Append($",[CODICE_COMUNE_RESIDENZA] = @CodiceComune");
            query.Append($" WHERE ID_PATENTE = @IdPatente");

            using (var connection = CreateConnection())
            {
                connection.Open();

                int result = await connection.ExecuteAsync(
                    query.ToString(),
                    new
                    {
                        IdPatente = patente.ID_PATENTE,
                        CodicePatente = patente.CODICE_PATENTE,
                        Cognome = patente.COGNOME,
                        Nome = patente.NOME,
                        CodiceFiscale = patente.CODICE_FISCALE,
                        Sesso = patente.SESSO,
                        LuogoNascita = patente.LUOGO_NASCITA,
                        DataNascita = patente.DATA_NASCITA,
                        Regione = patente.REGIONE_RESIDENZA,
                        Provincia = patente.PROVINCIA_RESIDENZA,
                        Comune = patente.COMUNE_RESIDENZA,
                        Indirizzo = patente.INDIRIZZO_RESIDENZA,
                        Cap = patente.CAP_RESIDENZA,
                        IdIspettorato = patente.ID_ANAGRAFICA_ISPETTORATO,
                        Pec = patente.PEC,
                        Email = patente.EMAIL,
                        Telefono = patente.TELEFONO,
                        RiferimentoDocPatente = patente.RIFERIMENTO_DOCUMENTO_PATENTE,
                        RiferimentoDocHarec = patente.RIFERIMENTO_DOCUMENTO_HAREC,
                        DataRilascio = patente.DATA_RILASCIO,
                        CodiceProtocolloEntrata = patente.CODICE_PROTOCOLLO_ENTRATA,
                        DataProtocolloEntrata = patente.DATA_PROTOCOLLO_ENTRATA,
                        CodiceProtocolloUscita = patente.CODICE_PROTOCOLLO_USCITA,
                        DataProtocolloUscita = patente.DATA_PROTOCOLLO_USCITA,
                        CodiceDomanda = patente.CODICE_DOMANDA,
                        NumeroPatenteCartacea = patente.NUMERO_PATENTE_CARTACEA,
                        IdStato = patente.ID_STATO_PATENTE,
                        Utente = patente.UTENTE,
                        CodiceLuogoNascita = patente.CODICE_LUOGO_NASCITA,
                        CodiceRegione = patente.CODICE_REGIONE_RESIDENZA,
                        CodiceProvincia = patente.CODICE_PROVINCIA_RESIDENZA,
                        CodiceComune = patente.CODICE_COMUNE_RESIDENZA
                    });
                return result > 0 ? patente.ID_PATENTE : 0;
            }
        }

        public async Task<string> UpdateStatoPatenteByCodiceAsync(int idStatoPatente, string codicePatente)
        {
            var query = new StringBuilder();
            query.Append($" UPDATE [dbo].[TB_PATENTI] SET ");
            query.Append($" [ID_STATO_PATENTE] = @IdStato");
            query.Append($" WHERE [CODICE_PATENTE] = @CodicePatente");

            using (var connection = CreateConnection())
            {
                connection.Open();

                int result = await connection.ExecuteAsync(
                    query.ToString(),
                    new
                    {
                        IdStato = idStatoPatente,
                        CodicePatente = codicePatente
                    });
                return result > 0 ? codicePatente : string.Empty;
            }
        }

        public async Task<long> GetNextSequenceValue()
        {
            string query = $"SELECT NEXT VALUE FOR {NomeSequence.CodicePatente}";
            using (var connection = CreateConnection())
            {
                connection.Open();
                long result = await connection.ExecuteScalarAsync<long>(query);
                return result;
            }
        }

        public async Task<int> SetProtocolloAsync(string codiceDomandaFE, DateTime dataProtocollo, string numeroProtocollo)
        {
            var query = new StringBuilder();
            query.Append($" UPDATE {NomeTabella.Patenti} ");
            query.Append($" SET [CODICE_PROTOCOLLO_ENTRATA] = @numeroProtocollo ");
            query.Append($" , [DATA_PROTOCOLLO_ENTRATA] = @dataProtocollo ");
            query.Append($" WHERE [CODICE_DOMANDA] = @codiceDomandaFE ");

            using (var connection = CreateConnection())
            {
                connection.Open();
                using (var tran = connection.BeginTransaction())
                {
                    try
                    {
                        var ret = await connection.ExecuteAsync(query.ToString(),
                            new
                            {
                                dataProtocollo = dataProtocollo,
                                codiceDomandaFE = codiceDomandaFE,
                                numeroProtocollo = numeroProtocollo
                            },
                            tran);

                        tran.Commit();

                        return ret;
                    }
                    catch (Exception ex)
                    {
                        tran.Rollback();
                        throw;
                    }
                }
            }
        }

        public async Task<string> SetProtocolloUscitaAsync(string codicePatente, DateTime dataProtocolloUscita, string codiceProtocolloUscita, DateTime dataRilascioPatente, string riferimentoPatente, string riferimentoHarec)
        {
            var query = new StringBuilder();
            query.Append($" UPDATE {NomeTabella.Patenti} ");
            query.Append($" SET [CODICE_PROTOCOLLO_USCITA] = @codiceProtocollo ");
            query.Append($" , [DATA_PROTOCOLLO_USCITA] = @dataProtocollo ");
            query.Append($" , [DATA_RILASCIO] = @dataRilascio ");
            query.Append($" , [RIFERIMENTO_DOCUMENTO_PATENTE] = @riferimentoPatente ");
            query.Append($" , [RIFERIMENTO_DOCUMENTO_HAREC] = @riferimentoHarec ");
            query.Append($" WHERE [CODICE_PATENTE] = @codicePatente ");

            using (var connection = CreateConnection())
            {
                connection.Open();
                using (var tran = connection.BeginTransaction())
                {
                    try
                    {
                        var ret = await connection.ExecuteAsync(query.ToString(),
                            new
                            {
                                codiceProtocollo = codiceProtocolloUscita,
                                dataProtocollo = dataProtocolloUscita,
                                dataRilascio = dataRilascioPatente,
                                riferimentoPatente = riferimentoPatente,
                                riferimentoHarec = riferimentoHarec,
                                codicePatente = codicePatente
                            },
                            tran);

                        tran.Commit();

                        return codicePatente;
                    }
                    catch (Exception ex)
                    {
                        tran.Rollback();
                        throw;
                    }
                }
            }
        }

        public async Task<int> UpdateRiferimentiDocumenti(int idPatente, string riferimentoDocumentoPatente, string riferimentoCertificatoHAREC)
        {
            var query = new StringBuilder();
            query.Append($" UPDATE {NomeTabella.Patenti} ");
            query.Append($" SET {nameof(Patente.RIFERIMENTO_DOCUMENTO_PATENTE)} = @riferimentoDocumentoPatente ");
            query.Append($" , {nameof(Patente.RIFERIMENTO_DOCUMENTO_HAREC)} = @riferimentoCertificatoHAREC ");
            query.Append($" WHERE {nameof(Patente.ID_PATENTE)} = @idPatente ");

            using (var connection = CreateConnection())
            {
                connection.Open();
                using (var tran = connection.BeginTransaction())
                {
                    try
                    {
                        var ret = await connection.ExecuteAsync(query.ToString(),
                            new
                            {
                                riferimentoDocumentoPatente,
                                riferimentoCertificatoHAREC,
                                idPatente
                            },
                            tran);

                        tran.Commit();

                        return ret;
                    }
                    catch (Exception ex)
                    {
                        tran.Rollback();
                        throw;
                    }
                }
            }
        }

        #region Metodi privati

        private (StringBuilder, Dictionary<string, object>) PreparaParametriRicercaPatentiQuery(PatenteFilterDto parametri)
        {
            // query di ricerca
            var query = new StringBuilder();
            query.Append($" SELECT p.*, s.*, i.* FROM {NomeTabella.Patenti} AS p");
            query.Append($" JOIN {NomeTabella.StatiPatente} AS s");
            query.Append($"     ON p.{nameof(Patente.ID_STATO_PATENTE)} = s.{nameof(Entities.StatoPatente.ID_STATO_PATENTE)}");
            query.Append($" JOIN {NomeTabella.AnagraficaIspettorati} AS i");
            query.Append($"     ON p.{nameof(Patente.ID_ANAGRAFICA_ISPETTORATO)} = i.{nameof(AnagraficaIspettorato.ID)}");
            query.Append($" WHERE ( @Cognome IS NULL OR p.{nameof(Patente.COGNOME)} LIKE '%' +  @Cognome + '%')");
            query.Append($" AND ( @Nome IS NULL OR p.{nameof(Patente.NOME)} LIKE '%' + @Nome + '%') ");
            query.Append($" AND ( @CodiceFiscale IS NULL OR p.{nameof(Patente.CODICE_FISCALE)} LIKE '%' +  @CodiceFiscale + '%') ");
            query.Append($" AND ( p.{nameof(Patente.DATA_NASCITA)} = @Data_Nascita OR @Data_Nascita IS NULL) ");
            query.Append($" AND ( @Regione IS NULL OR p.{nameof(Patente.REGIONE_RESIDENZA)} LIKE '%' + @Regione + '%') ");
            query.Append($" AND ( @Provincia IS NULL OR p.{nameof(Patente.PROVINCIA_RESIDENZA)} LIKE '%' + @Provincia + '%') ");
            query.Append($" AND ( @CodicePatente IS NULL OR (p.{nameof(Patente.CODICE_PATENTE)} LIKE '%' + @CodicePatente + '%' OR p.{nameof(Patente.NUMERO_PATENTE_CARTACEA)} LIKE '%' + @CodicePatente + '%'))");
            query.Append($" AND ( p.{nameof(Patente.ID_STATO_PATENTE)} = @IdStato OR @IdStato IS NULL) ");
            query.Append($" AND ( p.{nameof(Patente.DATA_RILASCIO)} = @DataRilascio OR @DataRilascio IS NULL) ");

            // Parametri per la ricerca
            var _p = new Dictionary<string, object>();

            _p.Add("Cognome", parametri.Cognome);
            _p.Add("Nome", parametri.Nome);
            _p.Add("CodiceFiscale", parametri.CodiceFiscale);
            _p.Add("Data_Nascita", parametri.DataDiNascita);
            _p.Add("Regione", parametri.RegioneResidenza);
            _p.Add("Provincia", parametri.ProvinciaResidenza);
            _p.Add("CodicePatente", parametri.NumeroPatente);
            _p.Add("IdStato", parametri.IdStatoPatente);
            _p.Add("DataRilascio", parametri.DataRilascio);

            return (query, _p);
        }

        #endregion
    }
}
