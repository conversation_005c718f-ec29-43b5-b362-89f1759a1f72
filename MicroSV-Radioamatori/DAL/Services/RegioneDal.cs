﻿using Dapper;
using Microsoft.Extensions.Configuration;
using MicroSV_Radioamatori.DAL.Common;
using MicroSV_Radioamatori.DAL.Entities;
using MicroSV_Radioamatori.DAL.Interfaces;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MicroSV_Radioamatori.DAL.Services
{
    public class RegioneDal : BaseDal, IRegioneDal
    {
        public RegioneDal(IConfiguration conf) : base(conf)
        {
        }

        public async Task<List<Regione>> GetRegioniAsync()
        {
            var query = new StringBuilder();
            query.Append($" SELECT * FROM {NomeTabella.Regioni} ORDER BY {nameof(Regione.DENOMINAZIONE)}");

            using (var connection = CreateConnection())
            {
                return (await connection.QueryAsync<Regione>(query.ToString())).ToList();
            }
        }

        public async Task<Regione> GetRegioneBySiglaAsync(string siglaRegione)
        {
            var query = new StringBuilder();
            query.Append($" SELECT * FROM {NomeTabella.Regioni} WHERE SIGLA = @sigla ORDER BY {nameof(Regione.DENOMINAZIONE)}");

            using (var connection = CreateConnection())
            {
                return (await connection.QueryAsync<Regione>(query.ToString(), new { sigla = siglaRegione })).FirstOrDefault();
            }
        }

        public async Task<Regione> GetRegioneByDenominazioneAsync(string denominazione)
        {
            var query = new StringBuilder();
            query.Append($" SELECT * FROM {NomeTabella.Regioni} WHERE DENOMINAZIONE = @denominazione ORDER BY {nameof(Regione.DENOMINAZIONE)}");

            using (var connection = CreateConnection())
            {
                return (await connection.QueryAsync<Regione>(query.ToString(), new { denominazione })).FirstOrDefault();
            }
        }
    }
}
