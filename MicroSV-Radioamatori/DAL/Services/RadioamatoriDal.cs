﻿using Dapper;
using Inv.Fwk.Helper.DateTimeZone;
using Microsoft.Extensions.Configuration;
using MicroSV_Radioamatori.BL.Common;
using MicroSV_Radioamatori.BL.DTOs;
using MicroSV_Radioamatori.BL.Enums;
using MicroSV_Radioamatori.BL.ValueObject;
using MicroSV_Radioamatori.DAL.Common;
using MicroSV_Radioamatori.DAL.Entities;
using MicroSV_Radioamatori.DAL.Interfaces;
using System;
using System.Collections.Generic;
using System.Dynamic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MicroSV_Radioamatori.DAL.Services
{
    public class RadioamatoriDal : BaseDal, IRadioamatoriDal
    {
        public RadioamatoriDal(IConfiguration conf) : base(conf)
        {

        }

        public async Task<List<Radioamatore>> GetRadioamatoriByCodiceFiscaleAsync(string codicefiscale, bool filtraStati = true)
        {
            var query = new StringBuilder();
            query.Append($" SELECT r.*, z.*, s.*, m.*, m.{nameof(Manifestazione.ID_MANIFESTAZIONE)} AS ID_MANIFESTAZIONE FROM {NomeTabella.Radioamatori} AS r");
            query.Append($" JOIN {NomeTabella.CodiciZone} AS z");
            query.Append($"     ON r.{nameof(Radioamatore.ID_CODICE_ZONA)} = z.{nameof(CodiceZona.ID_CODICE_ZONA)}");
            query.Append($" LEFT OUTER JOIN {NomeTabella.Suffissi} AS s");
            query.Append($"     ON r.{nameof(Radioamatore.ID_SUFFISSO)} = s.{nameof(Suffisso.ID_SUFFISSO)}");
            query.Append($" LEFT OUTER JOIN {NomeTabella.Manifestazione} AS m");
            query.Append($"     ON r.{nameof(Radioamatore.ID_RADIOAMATORE)} = m.{nameof(Manifestazione.ID_RADIOAMATORE)}");
            query.Append($" WHERE r.{nameof(Radioamatore.CODICE_FISCALE)} = @CodiceFiscale ");

            if (filtraStati)
            {
                query.Append($" AND r.{nameof(Radioamatore.STATO_NOMINATIVO)} NOT IN @stati ");
            }

            using (var connection = CreateConnection())
            {
                return connection.Query<Radioamatore, CodiceZona, Suffisso, Manifestazione, Radioamatore>(query.ToString(),
                    (r, z, s, m) =>
                    {
                        r.SUFFISSO = s;
                        r.CODICE_ZONA = z;
                        r.MANIFESTAZIONE = m;
                        return r;
                    },
                    splitOn: $"{nameof(CodiceZona.ID_CODICE_ZONA)}, {nameof(Suffisso.ID_SUFFISSO)}, {nameof(Manifestazione.ID_RADIOAMATORE)}",
                    param: new
                    {
                        CodiceFiscale = codicefiscale,
                        stati = new List<string>
                        {                            
                            EnumStatiRadioamatori.ANNULLATO.ToString(),
                            EnumStatiRadioamatori.ARCHIVIATA.ToString()
                        }
                    }).AsQueryable().ToList();
            }
        }

        public async Task<List<Radioamatore>> GetRadioamatoriByCodiceNominantivoAsync(string codiceNominativo)
        {
            var query = new StringBuilder();
            query.Append($" SELECT r.*, z.*, s.*, m.* FROM {NomeTabella.Radioamatori} AS r ");
            query.Append($" JOIN {NomeTabella.CodiciZone} AS z");
            query.Append($"     ON r.{nameof(Radioamatore.ID_CODICE_ZONA)} = z.{nameof(CodiceZona.ID_CODICE_ZONA)}");
            query.Append($" LEFT OUTER JOIN {NomeTabella.Suffissi} AS s");
            query.Append($"     ON r.{nameof(Radioamatore.ID_SUFFISSO)} = s.{nameof(Suffisso.ID_SUFFISSO)}");
            query.Append($" LEFT OUTER JOIN {NomeTabella.Manifestazione} AS m");
            query.Append($"     ON r.{nameof(Radioamatore.ID_RADIOAMATORE)} = m.{nameof(Manifestazione.ID_RADIOAMATORE)}");
            query.Append($" WHERE r.{nameof(Radioamatore.CODICE_NOMINATIVO)} = @codiceNominativo ");
            query.Append($" AND r.{nameof(Radioamatore.STATO_NOMINATIVO)} NOT IN @stati ");

            using (var connection = CreateConnection())
            {
                return connection.Query<Radioamatore, CodiceZona, Suffisso, Manifestazione, Radioamatore>(query.ToString(),
                    (r, z, s, m) =>
                    {
                        r.SUFFISSO = s;
                        r.CODICE_ZONA = z;
                        r.MANIFESTAZIONE = m;
                        return r;
                    },
                    splitOn: $"{nameof(CodiceZona.ID_CODICE_ZONA)}, {nameof(Suffisso.ID_SUFFISSO)}, {nameof(Manifestazione.ID_RADIOAMATORE)}",
                    param: new
                    {
                        codiceNominativo = codiceNominativo,
                        stati = new List<string>
                        {
                            EnumStatiRadioamatori.ANNULLATO.ToString(),
                            EnumStatiRadioamatori.ARCHIVIATA.ToString()
                        }
                    }
                    ).AsQueryable().ToList();
            }
        }

        public async Task<Radioamatore> GetRadioamatoreByCodiceDomandaFEAsync(string codiceDomandaFE)
        {
            var query = new StringBuilder();
            query.Append($" SELECT r.*, z.*, s.*, m.* FROM {NomeTabella.Radioamatori} AS r ");
            query.Append($" JOIN {NomeTabella.CodiciZone} AS z");
            query.Append($"     ON r.{nameof(Radioamatore.ID_CODICE_ZONA)} = z.{nameof(CodiceZona.ID_CODICE_ZONA)}");
            query.Append($" LEFT OUTER JOIN {NomeTabella.Suffissi} AS s");
            query.Append($"     ON r.{nameof(Radioamatore.ID_SUFFISSO)} = s.{nameof(Suffisso.ID_SUFFISSO)}");
            query.Append($" LEFT OUTER JOIN {NomeTabella.Manifestazione} AS m");
            query.Append($"     ON r.{nameof(Radioamatore.ID_RADIOAMATORE)} = m.{nameof(Manifestazione.ID_RADIOAMATORE)}");
            query.Append($" WHERE r.{nameof(Radioamatore.CODICE_DOMANDA_FE)} = @codiceDomandaFE ");

            using (var connection = CreateConnection())
            {
                return (await connection.QueryAsync<Radioamatore, CodiceZona, Suffisso, Manifestazione, Radioamatore>(query.ToString(),
                            (r, z, s, m) =>
                            {
                                r.SUFFISSO = s;
                                r.CODICE_ZONA = z;
                                r.MANIFESTAZIONE = m;
                                return r;
                            },
                            splitOn: $"{nameof(CodiceZona.ID_CODICE_ZONA)}, {nameof(Suffisso.ID_SUFFISSO)}, {nameof(Manifestazione.ID_RADIOAMATORE)}",
                            param: new
                            {
                                codiceDomandaFE
                            })).FirstOrDefault();
            }
        }

        public async Task<List<Radioamatore>> GetRadioamatoriByCodiceContestAsync(string codiceContest)
        {
            var query = new StringBuilder();
            query.Append($" SELECT r.* FROM {NomeTabella.Radioamatori} AS r ");
            query.Append($" JOIN {NomeTabella.Contest} AS c");
            query.Append($"     ON r.{nameof(Radioamatore.ID_RADIOAMATORE)} = c.{nameof(Contest.ID_RADIOAMATORE)}");
            query.Append($" WHERE c.{nameof(Contest.CODICE_ANAGRAFICA_CONTEST)} = @codiceContest ");

            using (var connection = CreateConnection())
            {
                return (await connection.QueryAsync<Radioamatore>(query.ToString(),
                        param: new
                        {
                            codiceContest
                        }
                        )).ToList();
            }
        }

        public async Task<Radioamatore> GetRadioamatoreByIdAsync(int idRadioamatore)
        {
            var query = new StringBuilder();
            query.Append($" SELECT r.*, z.*, s.*, m.* FROM {NomeTabella.Radioamatori} AS r ");
            query.Append($" JOIN {NomeTabella.CodiciZone} AS z");
            query.Append($"     ON r.{nameof(Radioamatore.ID_CODICE_ZONA)} = z.{nameof(CodiceZona.ID_CODICE_ZONA)}");
            query.Append($" LEFT OUTER JOIN {NomeTabella.Suffissi} AS s");
            query.Append($"     ON r.{nameof(Radioamatore.ID_SUFFISSO)} = s.{nameof(Suffisso.ID_SUFFISSO)}");
            query.Append($" LEFT OUTER JOIN {NomeTabella.Manifestazione} AS m");
            query.Append($"     ON r.{nameof(Radioamatore.ID_RADIOAMATORE)} = m.{nameof(Manifestazione.ID_RADIOAMATORE)}");
            query.Append($" WHERE r.{nameof(Radioamatore.ID_RADIOAMATORE)} = @idRadioamatore ");

            using (var connection = CreateConnection())
            {
                return (await connection.QueryAsync<Radioamatore, CodiceZona, Suffisso, Manifestazione, Radioamatore>(query.ToString(),
                    (r, z, s, m) =>
                    {
                        r.SUFFISSO = s;
                        r.CODICE_ZONA = z;
                        r.MANIFESTAZIONE = m;
                        return r;
                    },
                    splitOn: $"{nameof(CodiceZona.ID_CODICE_ZONA)}, {nameof(Suffisso.ID_SUFFISSO)}, {nameof(Manifestazione.ID_RADIOAMATORE)}",
                    param: new
                    {
                        idRadioamatore
                    }
                    )).FirstOrDefault();
            }
        }

        public async Task<List<Radioamatore>> GetRadioamatoriByAnagraficaAsync(string nome, string cognome, string datanascita)
        {
            var query = new StringBuilder();
            query.Append($" SELECT * FROM {NomeTabella.Radioamatori} AS r");
            query.Append($" JOIN {NomeTabella.CodiciZone} AS z");
            query.Append($"     ON r.{nameof(Radioamatore.ID_CODICE_ZONA)} = z.{nameof(CodiceZona.ID_CODICE_ZONA)}");
            query.Append($" LEFT OUTER JOIN {NomeTabella.Suffissi} AS s");
            query.Append($"     ON r.{nameof(Radioamatore.ID_SUFFISSO)} = s.{nameof(Suffisso.ID_SUFFISSO)}");
            query.Append($" WHERE r.{nameof(Radioamatore.NOME)} = @Nome ");
            query.Append($" AND r.{nameof(Radioamatore.COGNOME)} = @Cognome ");
            query.Append($" AND r.{nameof(Radioamatore.DATA_NASCITA)} = @Data_Nascita ");
            query.Append($" AND r.{nameof(Radioamatore.STATO_NOMINATIVO)} NOT IN @stati ");

            using (var connection = CreateConnection())
            {
                return connection.Query<Radioamatore, CodiceZona, Suffisso, Radioamatore>(query.ToString(),
                    (r, z, s) =>
                    {
                        r.SUFFISSO = s;
                        r.CODICE_ZONA = z;
                        return r;
                    },
                    splitOn: $" {nameof(CodiceZona.ID_CODICE_ZONA)}, {nameof(Suffisso.ID_SUFFISSO)}",
                    param: new
                    {
                        Nome = nome,
                        Cognome = cognome,
                        Data_Nascita = datanascita,
                        stati = new List<string>
                        {
                            EnumStatiRadioamatori.ANNULLATO.ToString(),
                            EnumStatiRadioamatori.ARCHIVIATA.ToString()
                        }
                    }
                    ).AsQueryable().ToList();
            }
        }

        public async Task<List<Radioamatore>> GetRadioamatoriAsync()
        {
            var query = new StringBuilder();
            query.Append($" SELECT * FROM {NomeTabella.Radioamatori} AS r");
            query.Append($" JOIN {NomeTabella.CodiciZone} AS z");
            query.Append($"     ON r.{nameof(Radioamatore.ID_CODICE_ZONA)} = z.{nameof(CodiceZona.ID_CODICE_ZONA)}");
            query.Append($" LEFT OUTER JOIN {NomeTabella.Suffissi} AS s");
            query.Append($"     ON r.{nameof(Radioamatore.ID_SUFFISSO)} = s.{nameof(Suffisso.ID_SUFFISSO)}");
            query.Append($" WHERE r.{nameof(Radioamatore.STATO_NOMINATIVO)} NOT IN  @stati ");

            using (var connection = CreateConnection())
            {
                return connection.Query<Radioamatore, CodiceZona, Suffisso, Radioamatore>(query.ToString(),
                    (r, z, s) =>
                    {
                        r.SUFFISSO = s;
                        r.CODICE_ZONA = z;
                        return r;
                    },
                    splitOn: $" {nameof(CodiceZona.ID_CODICE_ZONA)}, {nameof(Suffisso.ID_SUFFISSO)}",
                    param: new
                    {
                        stati = new List<string>
                        {
                            EnumStatiRadioamatori.ANNULLATO.ToString(),
                            EnumStatiRadioamatori.ARCHIVIATA.ToString()
                        }
                    }
                    ).AsQueryable().ToList();
            }

        }

        public async Task<List<Radioamatore>> GetRadioamatoriByParametriAsync(ParametriRicercaRadioamatoreDto parametri)
        {
            var query = PreparaParametriRicercaRadioamatoreQuery(parametri);

            if (!string.IsNullOrWhiteSpace(parametri.IdIspettorato))
                query.Item1.Append($" AND i.{nameof(AnagraficaIspettorato.DENOMINAZIONE)} LIKE @Ispettorato ");

            query.Item2.Add("Ispettorato", $"%{parametri.IdIspettorato}%");

            using (var connection = CreateConnection())
            {
                var ret = connection.Query<Radioamatore, AnagraficaIspettorato, CodiceZona, Suffisso, Manifestazione, Radioamatore>(query.Item1.ToString(),
                    (r, a, z, s, m) =>
                    {
                        r.SUFFISSO = s;
                        r.CODICE_ZONA = z;
                        r.MANIFESTAZIONE = m;
                        return r;
                    },
                    splitOn: $"{nameof(AnagraficaIspettorato.ID)}, {nameof(CodiceZona.ID_CODICE_ZONA)}, {nameof(Suffisso.ID_SUFFISSO)}, {nameof(Manifestazione.ID_RADIOAMATORE)}",
                    param: query.Item2
                    ).AsQueryable().ToList();

                return ret;
            }

        }        

        public async Task<List<Radioamatore>> GetRadioamatoriByIspettoratoConGestioneSubentriAsync(ParametriRicercaRadioamatoreDto parametri)
        {
            var query = PreparaParametriRicercaRadioamatoreQuery(parametri);

            if (!string.IsNullOrWhiteSpace(parametri.IdIspettorato))
                query.Item1.Append($" AND ( i.{nameof(AnagraficaIspettorato.ID)} = @Ispettorato OR i.{nameof(AnagraficaIspettorato.ID_SUBENTRO)} = @Ispettorato ) "); 

            query.Item2.Add("Ispettorato", parametri.IdIspettorato);

            using (var connection = CreateConnection())
            {
                var ret = connection.Query<Radioamatore, AnagraficaIspettorato, CodiceZona, Suffisso, Manifestazione, Radioamatore>(query.Item1.ToString(),
                    (r, a, z, s, m) =>
                    {
                        r.SUFFISSO = s;
                        r.CODICE_ZONA = z;
                        r.MANIFESTAZIONE = m;
                        return r;
                    },
                    splitOn: $"{nameof(AnagraficaIspettorato.ID)}, {nameof(CodiceZona.ID_CODICE_ZONA)}, {nameof(Suffisso.ID_SUFFISSO)}, {nameof(Manifestazione.ID_RADIOAMATORE)}",
                    param: query.Item2
                    ).AsQueryable().ToList();

                return ret;
            }

        }

        public async Task<List<Radioamatore>> GetNominativiDaSettareScadutiAsync()
        {
            var query = new StringBuilder();
            query.Append($" SELECT r.* ");
            query.Append($" FROM {NomeTabella.Radioamatori} AS r ");
            query.Append($" WHERE CAST(GETDATE() AS DATE) > CAST(r.{nameof(Radioamatore.DATA_SCADENZA)} AS DATE) ");
            query.Append($" AND r.{nameof(Radioamatore.TIPO_NOMINATIVO)} = @TipoNominativo");
            query.Append($" AND r.{nameof(Radioamatore.STATO_NOMINATIVO)} = @StatoNominativo");

            using (var connection = CreateConnection())
            {
                return (await connection.QueryAsync<Radioamatore>(query.ToString(),
                        param: new
                        {
                            TipoNominativo = TipoNominativo.STANDARD.ToString(),
                            StatoNominativo = EnumStatiRadioamatori.ATTIVO.ToString()
                        }
                        )).AsQueryable().ToList();
            }
        }

        public async Task AnnullaNominativoAsync(string codiceDomandaFE)
        {
            // In caso di annullamento della nominativo allora si riporta disponibile il codice nominativo
            Radioamatore VerificaesistenzaNominativo;
            var query = new StringBuilder();
            query.Append($" SELECT * FROM  {NomeTabella.Radioamatori} ");
            query.Append($" WHERE {nameof(Radioamatore.STATO_NOMINATIVO)} NOT IN @statiEsclusi ");
            query.Append($" AND  {nameof(Radioamatore.CODICE_DOMANDA_FE)} = @codiceDomandaFE ");

            using (var connection = CreateConnection())
            {
                VerificaesistenzaNominativo = (await connection.QueryAsync<Radioamatore>(query.ToString(),
                   new
                   {
                       codiceDomandaFE = codiceDomandaFE,
                       statiEsclusi = new List<string>
                               {
                                    EnumStatiRadioamatori.ANNULLATO.ToString(),
                                    EnumStatiRadioamatori.ARCHIVIATA.ToString()
                               }
                   })).FirstOrDefault();
            }

            Console.WriteLine("VerificaesistenzaNominativo");
            if (VerificaesistenzaNominativo != null)
            {
                var query1 = new StringBuilder();
                query1.Append($" UPDATE {NomeTabella.Suffissi} ");
                query1.Append($" SET {NomeTabella.Suffissi}.{nameof(Suffisso.DISPONIBILE)} = @disponibile ");
                query1.Append($" FROM {NomeTabella.Radioamatori} AS R, {NomeTabella.Suffissi} AS S ");
                query1.Append($" WHERE R.{nameof(Radioamatore.ID_SUFFISSO)} = S.{nameof(Suffisso.ID_SUFFISSO)} ");
                query1.Append($" AND R.{nameof(Radioamatore.STATO_NOMINATIVO)} NOT IN @statiEsclusi ");
                query1.Append($" AND R.{nameof(Radioamatore.CODICE_DOMANDA_FE)} = @codiceDomandaFE ");

                var query2 = new StringBuilder();
                query2.Append($" UPDATE {NomeTabella.Radioamatori} ");
                query2.Append($" SET {NomeTabella.Radioamatori}.{nameof(Radioamatore.STATO_NOMINATIVO)} = @stato ");
                query2.Append($" , {NomeTabella.Radioamatori}.{nameof(Radioamatore.DATA_AGG)} = @dataAggiornamento ");
                query2.Append($" WHERE {nameof(Radioamatore.STATO_NOMINATIVO)} NOT IN @statiEsclusi ");
                query2.Append($" AND {nameof(Radioamatore.CODICE_DOMANDA_FE)} = @codiceDomandaFE ");

                using (var connection = CreateConnection())
                {
                    connection.Open();
                    using (var tran = connection.BeginTransaction())
                    {
                        try
                        {
                            var ret = await connection.ExecuteAsync(query1.ToString(),
                                new
                                {
                                    disponibile = true,
                                    codiceDomandaFE = codiceDomandaFE,
                                    statiEsclusi = new List<string>
                                    {
                                    EnumStatiRadioamatori.ANNULLATO.ToString(),
                                    EnumStatiRadioamatori.ARCHIVIATA.ToString()
                                    }
                                },
                                tran);

                            if (ret > 1)
                            {
                                throw new Exception($"L'annullamento del codice nominativo della domanda {codiceDomandaFE} impatta più record {NomeTabella.Suffissi} - rollback eseguito");
                            }

                            ret = await connection.ExecuteAsync(query2.ToString(),
                                new
                                {
                                    stato = EnumStatiRadioamatori.ANNULLATO.ToString().ToUpper(),
                                    dataAggiornamento = DateTimeHelper.GetZoneDateTimeNow(this._conf["TimeZoneId"]),
                                    codiceDomandaFE = codiceDomandaFE,
                                    statiEsclusi = new List<string>
                                    {
                                    EnumStatiRadioamatori.ANNULLATO.ToString(),
                                    EnumStatiRadioamatori.ARCHIVIATA.ToString()
                                    }
                                },
                                tran);

                            if (ret > 2)
                            {
                                // Per via del trigger sulla tabella Radioamatori l'azione di update ritorna un numero doppio delle row impattate
                                throw new Exception($"L'annullamento del codice nominativo della domanda {codiceDomandaFE} impatta più record {NomeTabella.Radioamatori} - rollback eseguito");
                            }

                            // if it was successful, commit the transaction
                            tran.Commit();
                        }
                        catch (Exception ex)
                        {
                            // roll the transaction back
                            tran.Rollback();

                            // handle the error however you need to.
                            throw;
                        }
                    }
                }
            }
        }

        public async Task AnnullaNominativoByIdAsync(int idRadioamatore)
        {
            var radioamatore = await GetRadioamatoreByIdAsync(idRadioamatore);

            if (radioamatore != null)
            {
                var querySuffisso = new StringBuilder();
                querySuffisso.Append($" UPDATE {NomeTabella.Suffissi} ");
                querySuffisso.Append($" SET {NomeTabella.Suffissi}.{nameof(Suffisso.DISPONIBILE)} = 1 ");
                querySuffisso.Append($" WHERE {nameof(Suffisso.ID_SUFFISSO)} = @idSuffisso ");

                var queryRadioamatore = new StringBuilder();
                queryRadioamatore.Append($" UPDATE {NomeTabella.Radioamatori} ");
                queryRadioamatore.Append($" SET {NomeTabella.Radioamatori}.{nameof(Radioamatore.STATO_NOMINATIVO)} = @stato ");
                queryRadioamatore.Append($" , {NomeTabella.Radioamatori}.{nameof(Radioamatore.DATA_AGG)} = @dataAggiornamento ");
                queryRadioamatore.Append($" WHERE {nameof(Radioamatore.ID_RADIOAMATORE)} = @idRadioamatore ");

                using (var connection = CreateConnection())
                {
                    connection.Open();
                    using (var tran = connection.BeginTransaction())
                    {
                        try
                        {
                            if (radioamatore.ID_SUFFISSO.HasValue)
                            {
                                var resultSuffisso = await connection.ExecuteAsync(querySuffisso.ToString(),
                                    new
                                    {
                                        idSuffisso = radioamatore.ID_SUFFISSO
                                    },
                                    tran);

                                if (resultSuffisso > 1)
                                {
                                    throw new Exception($"L'annullamento del codice nominativo avente idRadioamatore {idRadioamatore} impatta più record {NomeTabella.Suffissi} - rollback eseguito");
                                }
                            }

                            _ = await connection.ExecuteAsync(queryRadioamatore.ToString(),
                                new
                                {
                                    stato = EnumStatiRadioamatori.ANNULLATO.ToString().ToUpper(),
                                    dataAggiornamento = DateTimeHelper.GetZoneDateTimeNow(this._conf["TimeZoneId"]),
                                    idRadioamatore
                                },
                                tran);

                            // if it was successful, commit the transaction
                            tran.Commit();
                        }
                        catch (Exception)
                        {
                            // roll the transaction back
                            tran.Rollback();

                            // handle the error however you need to.
                            throw;
                        }
                    }
                }
            }


        }

        public async Task SetProtocolloMisePresentazioneAsync(string codiceDomandaFE, DateTime dataProtocollo, string numeroProtocollo)
        {

            var query1 = new StringBuilder();
            query1.Append($" UPDATE {NomeTabella.Radioamatori} ");
            query1.Append($" SET {NomeTabella.Radioamatori}.{nameof(Radioamatore.PROTOCOLLO_PRESENTAZIONE_FE)} = @numeroProtocollo ");
            query1.Append($" , {NomeTabella.Radioamatori}.{nameof(Radioamatore.DATA_PROTOCOLLO_PRESENTAZIONE_FE)} = @dataProtocollo ");
            query1.Append($" , {NomeTabella.Radioamatori}.{nameof(Radioamatore.STATO_NOMINATIVO)} = @StatoNominativo ");
            query1.Append($" WHERE {NomeTabella.Radioamatori}.{nameof(Radioamatore.CODICE_DOMANDA_FE)} = @codiceDomandaFE ");


            using (var connection = CreateConnection())
            {
                connection.Open();
                using (var tran = connection.BeginTransaction())
                {
                    try
                    {
                        var ret = await connection.ExecuteAsync(query1.ToString(),
                            new
                            {
                                dataProtocollo = dataProtocollo,
                                codiceDomandaFE = codiceDomandaFE,
                                numeroProtocollo = numeroProtocollo,
                                statonominativo = EnumStatiRadioamatori.IN_ISTRUTTORIA.ToString()
                            },
                            tran);


                        // if it was successful, commit the transaction
                        tran.Commit();
                    }
                    catch (Exception ex)
                    {
                        // roll the transaction back
                        tran.Rollback();

                        // handle the error however you need to.
                        throw;
                    }
                }
            }
        }

        public async Task UpdateCodiceFiscaleCodiceProtocolloDataRilascioAsync(int idRadioamatore, string codiceFiscale, DateTime? dataRilascio)
        {
            // In caso di annullamento della nominativo allora si riporta disponibile il codice nominativo
            Radioamatore VerificaesistenzaNominativo;
            var query = new StringBuilder();
            query.Append($" SELECT * FROM  {NomeTabella.Radioamatori} ");
            query.Append($" WHERE {nameof(Radioamatore.STATO_NOMINATIVO)} NOT IN @statiEsclusi ");
            query.Append($" AND  {nameof(Radioamatore.ID_RADIOAMATORE)} = @idRadioamatore ");

            using (var connection = CreateConnection())
            {
                VerificaesistenzaNominativo = (await connection.QueryAsync<Radioamatore>(query.ToString(),
                   new
                   {
                       idRadioamatore = idRadioamatore,
                       statiEsclusi = new List<string>
                               {
                                    EnumStatiRadioamatori.ANNULLATO.ToString(),
                                    EnumStatiRadioamatori.ARCHIVIATA.ToString()
                               }
                   })).FirstOrDefault();
            }

            Console.WriteLine("Esistenza nominativo verificata positivamente");
            if (VerificaesistenzaNominativo != null)
            {
                var query1 = new StringBuilder();
                query1.Append($" UPDATE {NomeTabella.Radioamatori}");
                query1.Append($" SET {NomeTabella.Radioamatori}.{nameof(Radioamatore.CODICE_FISCALE)} = @codiceFiscale , ");
                query1.Append($" {NomeTabella.Radioamatori}.{nameof(Radioamatore.DATA_RILASCIO)} = @dataRilascio ");
                query1.Append($" WHERE {nameof(Radioamatore.ID_RADIOAMATORE)} = @idRadioamatore");
                query1.Append($" AND {nameof(Radioamatore.STATO_NOMINATIVO)} NOT IN @statiEsclusi ");

                using (var connection = CreateConnection())
                {
                    connection.Open();
                    using (var tran = connection.BeginTransaction())
                    {
                        try
                        {
                            var ret = await connection.ExecuteAsync(query1.ToString(),
                                new
                                {
                                    idRadioamatore = idRadioamatore,
                                    dataRilascio = dataRilascio,
                                    codiceFiscale = codiceFiscale,
                                    statiEsclusi = new List<string>
                                    {
                                    EnumStatiRadioamatori.ANNULLATO.ToString(),
                                    EnumStatiRadioamatori.ARCHIVIATA.ToString()
                                    }
                                },
                                tran);

                            if (ret > 2) // 2 per settaggi del DB TODO Rivedere
                            {
                                throw new Exception($"L'aggioranemtno del radioamatore con id {idRadioamatore} impatta più record {NomeTabella.Radioamatori} - rollback eseguito");
                            }


                            // if it was successful, commit the transaction
                            tran.Commit();
                        }
                        catch (Exception ex)
                        {
                            // roll the transaction back
                            tran.Rollback();

                            // handle the error however you need to.
                            throw;
                        }
                    }
                }
            }
        }

        public async Task UpdateDataRilascioAsync(int idRadioamatore, DateTime? dataRilascio)
        {
            var query = new StringBuilder();
            query.Append($" UPDATE {NomeTabella.Radioamatori}");
            query.Append($" SET {NomeTabella.Radioamatori}.{nameof(Radioamatore.DATA_RILASCIO)} = @dataRilascio ");
            query.Append($" , {NomeTabella.Radioamatori}.{nameof(Radioamatore.DATA_AGG)} = @dataAggiornamento ");
            query.Append($" WHERE {nameof(Radioamatore.ID_RADIOAMATORE)} = @idRadioamatore");

            using (var connection = CreateConnection())
            {
                connection.Open();

                _ = await connection.ExecuteAsync(query.ToString(),
                    new
                    {
                        dataRilascio,
                        dataAggiornamento = DateTimeHelper.GetZoneDateTimeNow(this._conf["TimeZoneId"]),
                        idRadioamatore
                    });
            }
        }

        public async Task UpdateCodiceFiscaleAsync(int idRadioamatore, string codiceFiscale)
        {
            var query = new StringBuilder();
            query.Append($" UPDATE {NomeTabella.Radioamatori}");
            query.Append($" SET {NomeTabella.Radioamatori}.{nameof(Radioamatore.CODICE_FISCALE)} = @codiceFiscale ");
            query.Append($" , {NomeTabella.Radioamatori}.{nameof(Radioamatore.DATA_AGG)} = @dataAggiornamento ");
            query.Append($" WHERE {nameof(Radioamatore.ID_RADIOAMATORE)} = @idRadioamatore");

            using (var connection = CreateConnection())
            {
                connection.Open();

                _ = await connection.ExecuteAsync(query.ToString(),
                    new
                    {
                        codiceFiscale,
                        dataAggiornamento = DateTimeHelper.GetZoneDateTimeNow(this._conf["TimeZoneId"]),
                        idRadioamatore
                    });
            }
        }

        public async Task<int> GeneraNominativoStandardAsync(Radioamatore radioamatore)
        {
            return await GeneraNominativoAsync(radioamatore, null, null);
        }

        public async Task<int> GeneraNominativoManifestazioneAsync(Radioamatore radioamatore, Manifestazione manifestazione)
        {
            return await GeneraNominativoAsync(radioamatore, null, manifestazione);
        }

        public async Task<int> GeneraNominativoContestAsync(Radioamatore radioamatore, List<Contest> elencoContest)
        {
            return await GeneraNominativoAsync(radioamatore, elencoContest, null);
        }

        private async Task<int> GeneraNominativoAsync(Radioamatore radioamatore, List<Contest> elencoContest, Manifestazione manifestazione)
        {
            bool isStandard = radioamatore != null && manifestazione == null && (elencoContest == null || !elencoContest.Any());
            bool isManifestazione = radioamatore != null && manifestazione != null && (elencoContest == null || !elencoContest.Any());
            bool isContest = radioamatore != null && (elencoContest != null && elencoContest.Any()) && manifestazione == null;

            if (!isStandard && !isManifestazione && !isContest)
            {
                throw new Exception("Non è stato possibile individuare la tipologia di nominativo");
            }

            StringBuilder insertQueryRadioamatore = new StringBuilder();
            insertQueryRadioamatore.Append($" INSERT INTO {NomeTabella.Radioamatori} ");
            insertQueryRadioamatore.Append($" ( {nameof(Radioamatore.CAP)} ");
            insertQueryRadioamatore.Append($" , {nameof(Radioamatore.CODICE_DOMANDA_FE)} ");
            insertQueryRadioamatore.Append($" , {nameof(Radioamatore.CODICE_FISCALE)} ");
            insertQueryRadioamatore.Append($" , {nameof(Radioamatore.CODICE_NOMINATIVO)} ");
            insertQueryRadioamatore.Append($" , {nameof(Radioamatore.COGNOME)} ");
            insertQueryRadioamatore.Append($" , {nameof(Radioamatore.COMUNE)} ");
            insertQueryRadioamatore.Append($" , {nameof(Radioamatore.DATA_AGG)} ");
            insertQueryRadioamatore.Append($" , {nameof(Radioamatore.DATA_NASCITA)} ");
            insertQueryRadioamatore.Append($" , {nameof(Radioamatore.DENOMINAZIONE)} ");
            insertQueryRadioamatore.Append($" , {nameof(Radioamatore.ID_CODICE_ZONA)} ");
            insertQueryRadioamatore.Append($" , {nameof(Radioamatore.ID_SUFFISSO)} ");
            insertQueryRadioamatore.Append($" , {nameof(Radioamatore.INDIRIZZO)} ");
            insertQueryRadioamatore.Append($" , {nameof(Radioamatore.NOME)} ");
            insertQueryRadioamatore.Append($" , {nameof(Radioamatore.PROVINCIA)} ");
            insertQueryRadioamatore.Append($" , {nameof(Radioamatore.REGIONE)} ");
            insertQueryRadioamatore.Append($" , {nameof(Radioamatore.STATO_NOMINATIVO)} ");
            insertQueryRadioamatore.Append($" , {nameof(Radioamatore.TIPO_NOMINATIVO)} ");
            insertQueryRadioamatore.Append($" , {nameof(Radioamatore.UTENTE)} ");
            insertQueryRadioamatore.Append($" , {nameof(Radioamatore.ID_ANAGRAFICA_ISPETTORATO)} ");
            insertQueryRadioamatore.Append($" , {nameof(Radioamatore.PROTOCOLLO_PRESENTAZIONE_FE)} ");
            insertQueryRadioamatore.Append($" , {nameof(Radioamatore.DATA_PROTOCOLLO_PRESENTAZIONE_FE)} ");
            insertQueryRadioamatore.Append($" , {nameof(Radioamatore.DATA_RILASCIO)} ");
            insertQueryRadioamatore.Append($" , {nameof(Radioamatore.DATA_CREAZIONE)} ");
            insertQueryRadioamatore.Append($" , {nameof(Radioamatore.TIPOLOGIA_ASSEGNATARIO)} )");
            insertQueryRadioamatore.Append($" OUTPUT INSERTED.ID_RADIOAMATORE ");
            insertQueryRadioamatore.Append($" VALUES (@CAP, @CODICE_DOMANDA_FE, @CODICE_FISCALE, @CODICE_NOMINATIVO, @COGNOME, @COMUNE, @DATA_AGG, ");
            insertQueryRadioamatore.Append($" @DATA_NASCITA, @DENOMINAZIONE, @ID_CODICE_ZONA, @ID_SUFFISSO, @INDIRIZZO, ");
            insertQueryRadioamatore.Append($" @NOME, @PROVINCIA, @REGIONE, @STATO_NOMINATIVO, @TIPO_NOMINATIVO, @UTENTE, @ID_ANAGRAFICA_ISPETTORATO, @PROTOCOLLO_PRESENTAZIONE_FE, ");
            insertQueryRadioamatore.Append($" @DATA_PROTOCOLLO_PRESENTAZIONE_FE, @DATA_RILASCIO, @DATA_CREAZIONE, @TIPOLOGIA_ASSEGNATARIO )");

            StringBuilder insertQueryRadioamatoreTriggerOutput = new StringBuilder();
            insertQueryRadioamatoreTriggerOutput.Append($" INSERT INTO {NomeTabella.RadioamatoriTriggerOutput} ");
            insertQueryRadioamatoreTriggerOutput.Append($" ( {nameof(Radioamatore.CAP)} ");
            insertQueryRadioamatoreTriggerOutput.Append($" , {nameof(Radioamatore.CODICE_DOMANDA_FE)} ");
            insertQueryRadioamatoreTriggerOutput.Append($" , {nameof(Radioamatore.CODICE_FISCALE)} ");
            insertQueryRadioamatoreTriggerOutput.Append($" , {nameof(Radioamatore.CODICE_NOMINATIVO)} ");
            insertQueryRadioamatoreTriggerOutput.Append($" , {nameof(Radioamatore.COGNOME)} ");
            insertQueryRadioamatoreTriggerOutput.Append($" , {nameof(Radioamatore.COMUNE)} ");
            insertQueryRadioamatoreTriggerOutput.Append($" , {nameof(Radioamatore.DATA_AGG)} ");
            insertQueryRadioamatoreTriggerOutput.Append($" , {nameof(Radioamatore.DATA_NASCITA)} ");
            insertQueryRadioamatoreTriggerOutput.Append($" , {nameof(Radioamatore.DENOMINAZIONE)} ");
            insertQueryRadioamatoreTriggerOutput.Append($" , {nameof(Radioamatore.ID_CODICE_ZONA)} ");
            insertQueryRadioamatoreTriggerOutput.Append($" , {nameof(Radioamatore.ID_SUFFISSO)} ");
            insertQueryRadioamatoreTriggerOutput.Append($" , {nameof(Radioamatore.INDIRIZZO)} ");
            insertQueryRadioamatoreTriggerOutput.Append($" , {nameof(Radioamatore.NOME)} ");
            insertQueryRadioamatoreTriggerOutput.Append($" , {nameof(Radioamatore.PROVINCIA)} ");
            insertQueryRadioamatoreTriggerOutput.Append($" , {nameof(Radioamatore.REGIONE)} ");
            insertQueryRadioamatoreTriggerOutput.Append($" , {nameof(Radioamatore.STATO_NOMINATIVO)} ");
            insertQueryRadioamatoreTriggerOutput.Append($" , {nameof(Radioamatore.TIPO_NOMINATIVO)} ");
            insertQueryRadioamatoreTriggerOutput.Append($" , {nameof(Radioamatore.UTENTE)} ");
            insertQueryRadioamatoreTriggerOutput.Append($" , {nameof(Radioamatore.ID_ANAGRAFICA_ISPETTORATO)} ");
            insertQueryRadioamatoreTriggerOutput.Append($" , {nameof(Radioamatore.PROTOCOLLO_PRESENTAZIONE_FE)} ");
            insertQueryRadioamatoreTriggerOutput.Append($" , {nameof(Radioamatore.DATA_PROTOCOLLO_PRESENTAZIONE_FE)} ");
            insertQueryRadioamatoreTriggerOutput.Append($" , {nameof(Radioamatore.DATA_RILASCIO)} ");
            insertQueryRadioamatoreTriggerOutput.Append($" , {nameof(Radioamatore.DATA_CREAZIONE)} ");
            insertQueryRadioamatoreTriggerOutput.Append($" , {nameof(Radioamatore.TIPOLOGIA_ASSEGNATARIO)} ");
            insertQueryRadioamatoreTriggerOutput.Append($" , {nameof(Radioamatore.ID_RADIOAMATORE)} ");
            insertQueryRadioamatoreTriggerOutput.Append($" , TIMESTAMP_MODIFICA )");

            insertQueryRadioamatoreTriggerOutput.Append($" VALUES (@CAP, @CODICE_DOMANDA_FE, @CODICE_FISCALE, @CODICE_NOMINATIVO, @COGNOME, @COMUNE, @DATA_AGG, ");
            insertQueryRadioamatoreTriggerOutput.Append($" @DATA_NASCITA, @DENOMINAZIONE, @ID_CODICE_ZONA, @ID_SUFFISSO, @INDIRIZZO, ");
            insertQueryRadioamatoreTriggerOutput.Append($" @NOME, @PROVINCIA, @REGIONE, @STATO_NOMINATIVO, @TIPO_NOMINATIVO, @UTENTE, @ID_ANAGRAFICA_ISPETTORATO, ");
            insertQueryRadioamatoreTriggerOutput.Append($" @PROTOCOLLO_PRESENTAZIONE_FE, @DATA_PROTOCOLLO_PRESENTAZIONE_FE, @DATA_RILASCIO, @DATA_CREAZIONE, ");
            insertQueryRadioamatoreTriggerOutput.Append($" @TIPOLOGIA_ASSEGNATARIO, @ID_RADIOAMATORE, @TIMESTAMP_MODIFICA )");

            StringBuilder insertQueryManifestazione = new StringBuilder();
            insertQueryManifestazione.Append($" INSERT INTO {NomeTabella.Manifestazione} ");
            insertQueryManifestazione.Append($" ( {nameof(Manifestazione.DATA_FINE)} ");
            insertQueryManifestazione.Append($" , {nameof(Manifestazione.DATA_INIZIO)} ");
            insertQueryManifestazione.Append($" , {nameof(Manifestazione.DENOMINAZIONE)} ");
            insertQueryManifestazione.Append($" , {nameof(Manifestazione.ID_ANAGRAFICA_ISPETTORATO)} ");
            insertQueryManifestazione.Append($" , {nameof(Manifestazione.ID_RADIOAMATORE)} ");
            insertQueryManifestazione.Append($" , {nameof(Manifestazione.ID_TIPOMANIFESTAZIONE)} ");
            insertQueryManifestazione.Append($" , {nameof(Manifestazione.SIGLA_REGIONE)} )");
            insertQueryManifestazione.Append($" OUTPUT INSERTED.ID_MANIFESTAZIONE ");
            insertQueryManifestazione.Append($" VALUES (@DATA_FINE, @DATA_INIZIO, @DENOMINAZIONE, @ID_ANAGRAFICA_ISPETTORATO, @ID_RADIOAMATORE, @ID_TIPOMANIFESTAZIONE, @SIGLA_REGIONE )");

            using (var connection = CreateConnection())
            {
                connection.Open();
                using (var tran = connection.BeginTransaction())
                {
                    try
                    {
                        int idRadioamatore = connection.QuerySingle<int>(
                            insertQueryRadioamatore.ToString(),
                            new
                            {
                                CAP = radioamatore.CAP,
                                CODICE_DOMANDA_FE = radioamatore.CODICE_DOMANDA_FE,
                                CODICE_FISCALE = radioamatore.CODICE_FISCALE,
                                CODICE_NOMINATIVO = radioamatore.CODICE_NOMINATIVO,
                                COGNOME = radioamatore.COGNOME,
                                COMUNE = radioamatore.COMUNE.ToUpperSafe(),
                                DATA_AGG = radioamatore.DATA_AGG,
                                DATA_NASCITA = radioamatore.DATA_NASCITA,
                                DENOMINAZIONE = radioamatore.DENOMINAZIONE,
                                ID_CODICE_ZONA = radioamatore.ID_CODICE_ZONA,
                                ID_SUFFISSO = radioamatore.ID_SUFFISSO,
                                INDIRIZZO = radioamatore.INDIRIZZO,
                                NOME = radioamatore.NOME,
                                PROVINCIA = radioamatore.PROVINCIA.ToUpperSafe(),
                                REGIONE = radioamatore.REGIONE.ToUpperSafe(),
                                STATO_NOMINATIVO = radioamatore.STATO_NOMINATIVO,
                                TIPO_NOMINATIVO = radioamatore.TIPO_NOMINATIVO,
                                UTENTE = radioamatore.UTENTE,
                                ID_ANAGRAFICA_ISPETTORATO = radioamatore.ID_ANAGRAFICA_ISPETTORATO,
                                PROTOCOLLO_PRESENTAZIONE_FE = radioamatore.PROTOCOLLO_PRESENTAZIONE_FE,
                                DATA_PROTOCOLLO_PRESENTAZIONE_FE = radioamatore.DATA_PROTOCOLLO_PRESENTAZIONE_FE,
                                DATA_RILASCIO = radioamatore.DATA_RILASCIO,
                                DATA_CREAZIONE = radioamatore.DATA_CREAZIONE,
                                TIPOLOGIA_ASSEGNATARIO = radioamatore.TIPOLOGIA_ASSEGNATARIO
                            },
                            tran);

                        connection.Execute(
                            insertQueryRadioamatoreTriggerOutput.ToString(),
                            new
                            {
                                CAP = radioamatore.CAP,
                                CODICE_DOMANDA_FE = radioamatore.CODICE_DOMANDA_FE,
                                CODICE_FISCALE = radioamatore.CODICE_FISCALE,
                                CODICE_NOMINATIVO = radioamatore.CODICE_NOMINATIVO,
                                COGNOME = radioamatore.COGNOME,
                                COMUNE = radioamatore.COMUNE.ToUpperSafe(),
                                DATA_AGG = radioamatore.DATA_AGG,
                                DATA_NASCITA = radioamatore.DATA_NASCITA,
                                DENOMINAZIONE = radioamatore.DENOMINAZIONE,
                                ID_CODICE_ZONA = radioamatore.ID_CODICE_ZONA,
                                ID_SUFFISSO = radioamatore.ID_SUFFISSO,
                                INDIRIZZO = radioamatore.INDIRIZZO,
                                NOME = radioamatore.NOME,
                                PROVINCIA = radioamatore.PROVINCIA.ToUpperSafe(),
                                REGIONE = radioamatore.REGIONE.ToUpperSafe(),
                                STATO_NOMINATIVO = radioamatore.STATO_NOMINATIVO,
                                TIPO_NOMINATIVO = radioamatore.TIPO_NOMINATIVO,
                                UTENTE = radioamatore.UTENTE,
                                ID_ANAGRAFICA_ISPETTORATO = radioamatore.ID_ANAGRAFICA_ISPETTORATO,
                                PROTOCOLLO_PRESENTAZIONE_FE = radioamatore.PROTOCOLLO_PRESENTAZIONE_FE,
                                DATA_PROTOCOLLO_PRESENTAZIONE_FE = radioamatore.DATA_PROTOCOLLO_PRESENTAZIONE_FE,
                                DATA_RILASCIO = radioamatore.DATA_RILASCIO,
                                DATA_CREAZIONE = radioamatore.DATA_CREAZIONE,
                                TIPOLOGIA_ASSEGNATARIO = radioamatore.TIPOLOGIA_ASSEGNATARIO,
                                ID_RADIOAMATORE = idRadioamatore,
                                TIMESTAMP_MODIFICA = DateTimeHelper.GetZoneDateTimeNow(this._conf["TimeZoneId"])
                            },
                            tran);

                        if (isManifestazione)
                        {
                            manifestazione.ID_RADIOAMATORE = idRadioamatore;
                            int idManifestazione = connection.QuerySingle<int>(
                                insertQueryManifestazione.ToString(),
                                new
                                {
                                    DATA_FINE = manifestazione.DATA_FINE,
                                    DATA_INIZIO = manifestazione.DATA_INIZIO,
                                    DENOMINAZIONE = manifestazione.DENOMINAZIONE,
                                    ID_ANAGRAFICA_ISPETTORATO = manifestazione.ID_ANAGRAFICA_ISPETTORATO,
                                    ID_RADIOAMATORE = manifestazione.ID_RADIOAMATORE,
                                    ID_TIPOMANIFESTAZIONE = manifestazione.ID_TIPOMANIFESTAZIONE,
                                    SIGLA_REGIONE = manifestazione.SIGLA_REGIONE
                                },
                                tran);
                        }
                        else if (isContest)
                        {

                            foreach (var contest in elencoContest)
                            {
                                contest.ID_RADIOAMATORE = idRadioamatore;

                                StringBuilder insertQueryContest = new StringBuilder();
                                insertQueryContest.Append($" INSERT INTO {NomeTabella.Contest} ");
                                insertQueryContest.Append($" ( {nameof(Contest.ID_RADIOAMATORE)} ");
                                insertQueryContest.Append($" , {nameof(Contest.CODICE_ANAGRAFICA_CONTEST)} )");
                                insertQueryContest.Append($" OUTPUT INSERTED.ID ");
                                insertQueryContest.Append($" VALUES (@ID_RADIOAMATORE, @CODICE_ANAGRAFICA_CONTEST )");

                                int idContest = connection.QuerySingle<int>(
                                insertQueryContest.ToString(),
                                new
                                {
                                    ID_RADIOAMATORE = contest.ID_RADIOAMATORE,
                                    CODICE_ANAGRAFICA_CONTEST = contest.CODICE_ANAGRAFICA_CONTEST
                                },
                                tran);
                            }
                        }

                        tran.Commit();

                        return idRadioamatore;
                    }
                    catch (Exception ex)
                    {
                        // roll the transaction back
                        tran.Rollback();

                        // handle the error however you need to.
                        throw;
                    }
                }
            }
        }

        public async Task<int> SetNominativiScadutiByIdAsync(List<Radioamatore> radioamatori)
        {
            var querySetSuffissiDisponibili = new StringBuilder();
            querySetSuffissiDisponibili.Append($" UPDATE {NomeTabella.Suffissi} ");
            querySetSuffissiDisponibili.Append($" SET {nameof(Suffisso.DISPONIBILE)} = 1 ");
            querySetSuffissiDisponibili.Append($" WHERE {nameof(Suffisso.ID_SUFFISSO)} IN @ListaIdSuffisso ");

            var querySetNominativiScaduti = new StringBuilder();
            querySetNominativiScaduti.Append($" UPDATE {NomeTabella.Radioamatori} ");
            querySetNominativiScaduti.Append($" SET {nameof(Radioamatore.STATO_NOMINATIVO)} = @StatoNominativi ");
            querySetNominativiScaduti.Append($" WHERE {nameof(Radioamatore.ID_RADIOAMATORE)} IN @ListaIdRadioamatore ");

            using (var connection = CreateConnection())
            {
                connection.Open();

                using (var tran = connection.BeginTransaction())
                {
                    try
                    {
                        if (radioamatori.Any(r => r.ID_SUFFISSO.HasValue))
                        {
                            _ = await connection.ExecuteAsync(
                            querySetSuffissiDisponibili.ToString(),
                            new
                            {
                                ListaIdSuffisso = radioamatori.Select(r => r.ID_SUFFISSO).ToList()
                            },
                            tran);
                        }

                        var result = await connection.ExecuteAsync(
                        querySetNominativiScaduti.ToString(),
                        new
                        {
                            StatoNominativi = EnumStatiRadioamatori.SCADUTO.ToString(),
                            ListaIdRadioamatore = radioamatori.Select(r => r.ID_RADIOAMATORE).ToList()
                        },
                        tran);

                        tran.Commit();

                        return result;
                    }
                    catch (Exception)
                    {
                        tran.Rollback();
                        throw;
                    }
                }
            }
        }

        public async Task<int> SetProtocolloUscitaAsync(int idRadioamatore,
                                                    DateTime dataProtocolloUscita,
                                                    string codiceProtocolloUscita,
                                                    DateTime dataRilascioNominativo)
        {
            var querySetProtocollo = new StringBuilder();
            querySetProtocollo.Append($" UPDATE {NomeTabella.Radioamatori} ");
            querySetProtocollo.Append($" SET {nameof(Radioamatore.STATO_NOMINATIVO)} = @statoNominativo ");
            querySetProtocollo.Append($" , {nameof(Radioamatore.DATA_PROTOCOLLO)} = @dataProtocollo ");
            querySetProtocollo.Append($" , {nameof(Radioamatore.PROTOCOLLO)} = @codiceProtocollo ");
            querySetProtocollo.Append($" , {nameof(Radioamatore.DATA_RILASCIO)} = @dataRilascio ");
            querySetProtocollo.Append($" WHERE {nameof(Radioamatore.ID_RADIOAMATORE)} = @idRadioamatore ");

            using (var connection = CreateConnection())
            {
                connection.Open();

                _ = await connection.ExecuteAsync(querySetProtocollo.ToString(),
                    new
                    {
                        statoNominativo = EnumStatoNominativo.ATTIVO.ToString(),
                        dataProtocollo = dataProtocolloUscita,
                        codiceProtocollo = codiceProtocolloUscita,
                        dataRilascio = dataRilascioNominativo,
                        idRadioamatore
                    });

                return idRadioamatore;
            }
        }

        #region Metodi privati

        private (StringBuilder, Dictionary<string, object>) PreparaParametriRicercaRadioamatoreQuery(ParametriRicercaRadioamatoreDtoBase parametri)
        {
            // query di ricerca
            var query = new StringBuilder();
            query.Append($" SELECT r.*, i.*, z.*, s.*, m.*, m.{nameof(Manifestazione.ID_MANIFESTAZIONE)} AS ID_MANIFESTAZIONE FROM {NomeTabella.Radioamatori} AS r");
            query.Append($" JOIN {NomeTabella.AnagraficaIspettorati} AS i");
            query.Append($"     ON r.{nameof(Radioamatore.ID_ANAGRAFICA_ISPETTORATO)} = i.{nameof(AnagraficaIspettorato.ID)}");
            query.Append($" JOIN {NomeTabella.CodiciZone} AS z");
            query.Append($"     ON r.{nameof(Radioamatore.ID_CODICE_ZONA)} = z.{nameof(CodiceZona.ID_CODICE_ZONA)}");
            query.Append($" LEFT OUTER JOIN {NomeTabella.Suffissi} AS s");
            query.Append($"     ON r.{nameof(Radioamatore.ID_SUFFISSO)} = s.{nameof(Suffisso.ID_SUFFISSO)}");
            query.Append($" LEFT OUTER JOIN {NomeTabella.Manifestazione} AS m");
            query.Append($"     ON r.{nameof(Radioamatore.ID_RADIOAMATORE)} = m.{nameof(Manifestazione.ID_RADIOAMATORE)}");

            query.Append($" WHERE r.{nameof(Radioamatore.STATO_NOMINATIVO)} NOT IN @stati ");

            if (!string.IsNullOrWhiteSpace(parametri.Nome))
                query.Append($" AND r.{nameof(Radioamatore.NOME)} LIKE @Nome ");

            if (!string.IsNullOrWhiteSpace(parametri.Cognome))
                query.Append($" AND r.{nameof(Radioamatore.COGNOME)} LIKE @Cognome ");

            if (!string.IsNullOrWhiteSpace(parametri.CodiceFiscale))
                query.Append($" AND r.{nameof(Radioamatore.CODICE_FISCALE)} LIKE @CodiceFiscale ");

            if (!string.IsNullOrWhiteSpace(parametri.Provincia))
                query.Append($" AND r.{nameof(Radioamatore.PROVINCIA)} LIKE @Provincia ");

            if (!string.IsNullOrWhiteSpace(parametri.Regione))
                query.Append($" AND r.{nameof(Radioamatore.REGIONE)} LIKE @Regione ");

            if (!string.IsNullOrWhiteSpace(parametri.Nominativo))
                query.Append($" AND r.{nameof(Radioamatore.CODICE_NOMINATIVO)} LIKE @Nominativo ");

            if (!string.IsNullOrWhiteSpace(parametri.CodiceProtocollo))
                query.Append($" AND r.{nameof(Radioamatore.CODICE_DOMANDA_FE)} LIKE @Codice_Protocollo "); //Check

            if (!string.IsNullOrWhiteSpace(parametri.StatoNominativo))
                query.Append($" AND r.{nameof(Radioamatore.STATO_NOMINATIVO)} LIKE @Stato_Nominativo ");

            if (!string.IsNullOrWhiteSpace(parametri.TipologiaNominativo))
                query.Append($" AND r.{nameof(Radioamatore.TIPO_NOMINATIVO)} = @Tipologia_Nominativo ");

            if (!string.IsNullOrWhiteSpace(parametri.DataNascita))
                query.Append($" AND r.{nameof(Radioamatore.DATA_NASCITA)} = @Data_Nascita ");

            if (!string.IsNullOrWhiteSpace(parametri.DataRilascio))
                query.Append($" AND r.{nameof(Radioamatore.DATA_RILASCIO)} = @Data_Rilascio ");

            if (!string.IsNullOrWhiteSpace(parametri.DataScadenza))
                query.Append($" AND r.{nameof(Radioamatore.DATA_SCADENZA)} = @Data_Scadenza");

            // Parametri per la ricerca
            var _p = new Dictionary<string, object>();

            _p.Add("Nome", $"%{parametri.Nome}%");
            _p.Add("Cognome", $"%{parametri.Cognome}%");
            _p.Add("CodiceFiscale", $"%{parametri.CodiceFiscale}%");
            _p.Add("Provincia", $"%{parametri.Provincia}%");
            _p.Add("Regione", $"%{parametri.Regione}%");
            _p.Add("Nominativo", $"%{parametri.Nominativo}%");
            _p.Add("Codice_Protocollo", $"%{parametri.CodiceProtocollo}%");
            _p.Add("Stato_Nominativo", $"%{parametri.StatoNominativo}%");
            _p.Add("Tipologia_Nominativo", parametri.TipologiaNominativo);
            _p.Add("Data_Nascita", parametri.DataNascita);
            _p.Add("Data_Rilascio", parametri.DataRilascio);
            _p.Add("Data_Scadenza", parametri.DataScadenza);
            _p.Add("stati", new List<string>
                {
                    EnumStatiRadioamatori.ANNULLATO.ToString(),
                    EnumStatiRadioamatori.ARCHIVIATA.ToString(),
                    EnumStatiRadioamatori.GENERATO.ToString()
                });

            return (query, _p);
        }

        #endregion
    }

}