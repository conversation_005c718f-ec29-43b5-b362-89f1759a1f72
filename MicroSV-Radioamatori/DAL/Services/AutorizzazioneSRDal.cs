﻿using Dapper;
using Invitalia.Misure.Standard2.Utilities.Extensions;
using Microsoft.Extensions.Configuration;
using MicroSV_Radioamatori.BL.DTOs;
using MicroSV_Radioamatori.BL.Enums;
using MicroSV_Radioamatori.BL.IncomingDTOs;
using MicroSV_Radioamatori.BL.ValueObject;
using MicroSV_Radioamatori.DAL.Common;
using MicroSV_Radioamatori.DAL.Entities;
using MicroSV_Radioamatori.DAL.Interfaces;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Reflection.Metadata;
using System.Text;
using System.Threading.Tasks;

namespace MicroSV_Radioamatori.DAL.Services
{
    public class AutorizzazioneSRDal : BaseDal, IAutorizzazioneSRDal
    {
        public AutorizzazioneSRDal(IConfiguration conf) : base(conf)
        {

        }

        public async Task<string> GetPrefissoByCodiceRegioneAsync(string codiceRegione)
        {
            var query = new StringBuilder();
            query.Append($" SELECT * FROM {NomeTabella.PrefissoSR} WHERE SIGLA_REGIONE = @regione");

            using (var connection = CreateConnection())
            {
                var result = (await connection.QueryAsync<PrefissoSR>(query.ToString(), new { regione = codiceRegione })).FirstOrDefault();
                return result?.PREFISSO;
            }
        }

        public async Task<SuffissoSR> GetSuffissoByPrefissoAsync(string prefisso)
        {
            var query = new StringBuilder();
            query.Append($"SELECT TOP 1 * from {NomeTabella.SuffissoSR} WHERE PREFISSO = @prefisso AND DISPONIBILE = @disponibile ORDER BY SUFFISSO DESC");
            using (var connection = CreateConnection())
            {
                var result = (await connection.QueryAsync<SuffissoSR>(query.ToString(), new { prefisso, disponibile = true })).FirstOrDefault();
                return result;
            }
        }

        public async Task<SuffissoSR> GetSuffissoByPrefissoAndSuffissoAsync(string prefisso, string suffisso)
        {
            var query = new StringBuilder();
            query.Append($"SELECT TOP 1 * from {NomeTabella.SuffissoSR} WHERE PREFISSO = @prefisso AND SUFFISSO = @suffisso ORDER BY SUFFISSO DESC");
            using (var connection = CreateConnection())
            {
                var result = (await connection.QueryAsync<SuffissoSR>(query.ToString(), new { prefisso, suffisso })).FirstOrDefault();
                return result;
            }
        }

        public async Task<RadioamatoreSR> InsertNominativoSRAsync(RadioamatoreSR dto, int? idSuffisso)
        {
            StringBuilder insertQueryRadioamatore = new StringBuilder();
            insertQueryRadioamatore.Append($" INSERT INTO {NomeTabella.RadioamatoriSR} ");
            insertQueryRadioamatore.Append("(ID_SUFFISSO_SR, ");
            insertQueryRadioamatore.Append("ID_ANAGRAFICA_ISPETTORATO, ");
            insertQueryRadioamatore.Append("NOME, ");
            insertQueryRadioamatore.Append("COGNOME, ");
            insertQueryRadioamatore.Append("DENOMINAZIONE, ");
            insertQueryRadioamatore.Append("DATA_NASCITA, ");
            insertQueryRadioamatore.Append("CODICE_FISCALE, ");
            insertQueryRadioamatore.Append("INDIRIZZO, ");
            insertQueryRadioamatore.Append("REGIONE, ");
            insertQueryRadioamatore.Append("COMUNE, ");
            insertQueryRadioamatore.Append("PROVINCIA, ");
            insertQueryRadioamatore.Append("CAP, ");
            insertQueryRadioamatore.Append("CODICE_NOMINATIVO, ");
            insertQueryRadioamatore.Append("STATO_NOMINATIVO, ");
            insertQueryRadioamatore.Append("DATA_RILASCIO, ");
            insertQueryRadioamatore.Append("UTENTE, ");
            insertQueryRadioamatore.Append("DATA_AGG, ");
            insertQueryRadioamatore.Append("PROTOCOLLO, ");
            insertQueryRadioamatore.Append("DATA_PROTOCOLLO, ");
            insertQueryRadioamatore.Append("NOTA, ");
            insertQueryRadioamatore.Append("CODICE_DOMANDA_FE, ");
            insertQueryRadioamatore.Append("DATA_CREAZIONE, ");
            insertQueryRadioamatore.Append("PROTOCOLLO_PRESENTAZIONE_FE, ");
            insertQueryRadioamatore.Append("DATA_PROTOCOLLO_PRESENTAZIONE_FE, ");
            insertQueryRadioamatore.Append("TIPOLOGIA_ASSEGNATARIO, ");
            insertQueryRadioamatore.Append("DATA_SCADENZA, ");
            insertQueryRadioamatore.Append("CODICE_DOMANDA, ");
            insertQueryRadioamatore.Append("PEC, ");
            insertQueryRadioamatore.Append("EMAIL, ");
            insertQueryRadioamatore.Append("TELEFONO) ");
            insertQueryRadioamatore.Append($" OUTPUT INSERTED.ID_RADIOAMATORE_SR ");
            insertQueryRadioamatore.Append(" VALUES (");

            insertQueryRadioamatore.Append("@ID_SUFFISSO_SR, ");
            insertQueryRadioamatore.Append("@ID_ANAGRAFICA_ISPETTORATO, ");
            insertQueryRadioamatore.Append("@NOME, ");
            insertQueryRadioamatore.Append("@COGNOME, ");
            insertQueryRadioamatore.Append("@DENOMINAZIONE, ");
            insertQueryRadioamatore.Append("@DATA_NASCITA, ");
            insertQueryRadioamatore.Append("@CODICE_FISCALE, ");
            insertQueryRadioamatore.Append("@INDIRIZZO, ");
            insertQueryRadioamatore.Append("@REGIONE, ");
            insertQueryRadioamatore.Append("@COMUNE, ");
            insertQueryRadioamatore.Append("@PROVINCIA, ");
            insertQueryRadioamatore.Append("@CAP, ");
            insertQueryRadioamatore.Append("@CODICE_NOMINATIVO, ");
            insertQueryRadioamatore.Append("@STATO_NOMINATIVO, ");
            insertQueryRadioamatore.Append("@DATA_RILASCIO, ");
            insertQueryRadioamatore.Append("@UTENTE, ");
            insertQueryRadioamatore.Append("@DATA_AGG, ");
            insertQueryRadioamatore.Append("@PROTOCOLLO, ");
            insertQueryRadioamatore.Append("@DATA_PROTOCOLLO, ");
            insertQueryRadioamatore.Append("@NOTA, ");
            insertQueryRadioamatore.Append("@CODICE_DOMANDA_FE, ");
            insertQueryRadioamatore.Append("@DATA_CREAZIONE, ");
            insertQueryRadioamatore.Append("@PROTOCOLLO_PRESENTAZIONE_FE, ");
            insertQueryRadioamatore.Append("@DATA_PROTOCOLLO_PRESENTAZIONE_FE, ");
            insertQueryRadioamatore.Append("@TIPOLOGIA_ASSEGNATARIO, ");
            insertQueryRadioamatore.Append("@DATA_SCADENZA, ");
            insertQueryRadioamatore.Append("@CODICE_DOMANDA, ");
            insertQueryRadioamatore.Append("@PEC, ");
            insertQueryRadioamatore.Append("@EMAIL, ");
            insertQueryRadioamatore.Append("@TELEFONO)");

            var param = new
            {
                dto.ID_SUFFISSO_SR,
                dto.ID_ANAGRAFICA_ISPETTORATO,
                dto.NOME,
                dto.COGNOME,
                dto.DENOMINAZIONE,
                dto.DATA_NASCITA,
                dto.CODICE_FISCALE,
                dto.INDIRIZZO,
                dto.REGIONE,
                dto.COMUNE,
                dto.PROVINCIA,
                dto.CAP,
                dto.CODICE_NOMINATIVO,
                dto.STATO_NOMINATIVO,
                dto.DATA_RILASCIO,
                dto.UTENTE,
                dto.DATA_AGG,
                dto.PROTOCOLLO,
                dto.DATA_PROTOCOLLO,
                dto.NOTA,
                dto.CODICE_DOMANDA_FE,
                dto.DATA_CREAZIONE,
                dto.PROTOCOLLO_PRESENTAZIONE_FE,
                dto.DATA_PROTOCOLLO_PRESENTAZIONE_FE,
                dto.TIPOLOGIA_ASSEGNATARIO,
                dto.DATA_SCADENZA,
                dto.CODICE_DOMANDA,
                dto.PEC,
                dto.EMAIL,
                dto.TELEFONO
            };


            StringBuilder insertQueryRadioamatoreTO = new StringBuilder();
            insertQueryRadioamatoreTO.Append($" INSERT INTO {NomeTabella.RadioamatoriSRTriggerOutput} ");
            insertQueryRadioamatoreTO.Append("(ID_RADIOAMATORE_SR, ");
            insertQueryRadioamatoreTO.Append("ID_SUFFISSO_SR, ");
            insertQueryRadioamatoreTO.Append("ID_ANAGRAFICA_ISPETTORATO, ");
            insertQueryRadioamatoreTO.Append("NOME, ");
            insertQueryRadioamatoreTO.Append("COGNOME, ");
            insertQueryRadioamatoreTO.Append("DENOMINAZIONE, ");
            insertQueryRadioamatoreTO.Append("DATA_NASCITA, ");
            insertQueryRadioamatoreTO.Append("CODICE_FISCALE, ");
            insertQueryRadioamatoreTO.Append("INDIRIZZO, ");
            insertQueryRadioamatoreTO.Append("REGIONE, ");
            insertQueryRadioamatoreTO.Append("COMUNE, ");
            insertQueryRadioamatoreTO.Append("PROVINCIA, ");
            insertQueryRadioamatoreTO.Append("CAP, ");
            insertQueryRadioamatoreTO.Append("CODICE_NOMINATIVO, ");
            insertQueryRadioamatoreTO.Append("STATO_NOMINATIVO, ");
            insertQueryRadioamatoreTO.Append("DATA_RILASCIO, ");
            insertQueryRadioamatoreTO.Append("UTENTE, ");
            insertQueryRadioamatoreTO.Append("DATA_AGG, ");
            insertQueryRadioamatoreTO.Append("PROTOCOLLO, ");
            insertQueryRadioamatoreTO.Append("DATA_PROTOCOLLO, ");
            insertQueryRadioamatoreTO.Append("NOTA, ");
            insertQueryRadioamatoreTO.Append("CODICE_DOMANDA_FE, ");
            insertQueryRadioamatoreTO.Append("DATA_CREAZIONE, ");
            insertQueryRadioamatoreTO.Append("PROTOCOLLO_PRESENTAZIONE_FE, ");
            insertQueryRadioamatoreTO.Append("DATA_PROTOCOLLO_PRESENTAZIONE_FE, ");
            insertQueryRadioamatoreTO.Append("TIPOLOGIA_ASSEGNATARIO, ");
            insertQueryRadioamatoreTO.Append("DATA_SCADENZA, ");
            insertQueryRadioamatoreTO.Append("CODICE_DOMANDA, ");
            insertQueryRadioamatoreTO.Append("PEC, ");
            insertQueryRadioamatoreTO.Append("EMAIL, ");
            insertQueryRadioamatoreTO.Append("TELEFONO, ");
            insertQueryRadioamatoreTO.Append("TIMESTAMP_MODIFICA) ");
            insertQueryRadioamatoreTO.Append(" VALUES (");


            insertQueryRadioamatoreTO.Append("@ID_RADIOAMATORE_SR, ");
            insertQueryRadioamatoreTO.Append("@ID_SUFFISSO_SR, ");
            insertQueryRadioamatoreTO.Append("@ID_ANAGRAFICA_ISPETTORATO, ");
            insertQueryRadioamatoreTO.Append("@NOME, ");
            insertQueryRadioamatoreTO.Append("@COGNOME, ");
            insertQueryRadioamatoreTO.Append("@DENOMINAZIONE, ");
            insertQueryRadioamatoreTO.Append("@DATA_NASCITA, ");
            insertQueryRadioamatoreTO.Append("@CODICE_FISCALE, ");
            insertQueryRadioamatoreTO.Append("@INDIRIZZO, ");
            insertQueryRadioamatoreTO.Append("@REGIONE, ");
            insertQueryRadioamatoreTO.Append("@COMUNE, ");
            insertQueryRadioamatoreTO.Append("@PROVINCIA, ");
            insertQueryRadioamatoreTO.Append("@CAP, ");
            insertQueryRadioamatoreTO.Append("@CODICE_NOMINATIVO, ");
            insertQueryRadioamatoreTO.Append("@STATO_NOMINATIVO, ");
            insertQueryRadioamatoreTO.Append("@DATA_RILASCIO, ");
            insertQueryRadioamatoreTO.Append("@UTENTE, ");
            insertQueryRadioamatoreTO.Append("@DATA_AGG, ");
            insertQueryRadioamatoreTO.Append("@PROTOCOLLO, ");
            insertQueryRadioamatoreTO.Append("@DATA_PROTOCOLLO, ");
            insertQueryRadioamatoreTO.Append("@NOTA, ");
            insertQueryRadioamatoreTO.Append("@CODICE_DOMANDA_FE, ");
            insertQueryRadioamatoreTO.Append("@DATA_CREAZIONE, ");
            insertQueryRadioamatoreTO.Append("@PROTOCOLLO_PRESENTAZIONE_FE, ");
            insertQueryRadioamatoreTO.Append("@DATA_PROTOCOLLO_PRESENTAZIONE_FE, ");
            insertQueryRadioamatoreTO.Append("@TIPOLOGIA_ASSEGNATARIO, ");
            insertQueryRadioamatoreTO.Append("@DATA_SCADENZA, ");
            insertQueryRadioamatoreTO.Append("@CODICE_DOMANDA, ");
            insertQueryRadioamatoreTO.Append("@PEC, ");
            insertQueryRadioamatoreTO.Append("@EMAIL, ");
            insertQueryRadioamatoreTO.Append("@TELEFONO, ");
            insertQueryRadioamatoreTO.Append("@TIMESTAMP_MODIFICA)");


            using (var connection = CreateConnection())
            {
                connection.Open();
                using (var tran = connection.BeginTransaction())
                {
                    try
                    {
                        var idNominativo = await connection.QuerySingleAsync<int>(insertQueryRadioamatore.ToString(), param, tran);
                        if (idSuffisso.HasValue)
                        {
                            await ImpegnaSuffissoSRAsync(idSuffisso.Value, connection, tran);
                        }

                        var paramTO = new
                        {
                            ID_RADIOAMATORE_SR = idNominativo,
                            ID_SUFFISSO_SR = idSuffisso,
                            dto.ID_ANAGRAFICA_ISPETTORATO,
                            dto.NOME,
                            dto.COGNOME,
                            dto.DENOMINAZIONE,
                            dto.DATA_NASCITA,
                            dto.CODICE_FISCALE,
                            dto.INDIRIZZO,
                            dto.REGIONE,
                            dto.COMUNE,
                            dto.PROVINCIA,
                            dto.CAP,
                            dto.CODICE_NOMINATIVO,
                            dto.STATO_NOMINATIVO,
                            dto.DATA_RILASCIO,
                            dto.UTENTE,
                            dto.DATA_AGG,
                            dto.PROTOCOLLO,
                            dto.DATA_PROTOCOLLO,
                            dto.NOTA,
                            dto.CODICE_DOMANDA_FE,
                            dto.DATA_CREAZIONE,
                            dto.PROTOCOLLO_PRESENTAZIONE_FE,
                            dto.DATA_PROTOCOLLO_PRESENTAZIONE_FE,
                            dto.TIPOLOGIA_ASSEGNATARIO,
                            dto.DATA_SCADENZA,
                            dto.CODICE_DOMANDA,
                            dto.PEC,
                            dto.EMAIL,
                            dto.TELEFONO,
                            TIMESTAMP_MODIFICA = DateTime.Now
                        };
                        await connection.ExecuteScalarAsync(insertQueryRadioamatoreTO.ToString(), paramTO, tran);
                        tran.Commit();

                        dto.ID_RADIOAMATORE_SR = idNominativo;
                        return dto;
                    }
                    catch (Exception ex)
                    {
                        tran.Rollback();
                        throw;
                    }
                }
            }
        }

        public async Task ImpegnaSuffissoSRAsync(int idSuffisso, IDbConnection connection, IDbTransaction tran)
        {
            var query = new StringBuilder();
            query.Append($"UPDATE {NomeTabella.SuffissoSR} SET DISPONIBILE = @disponibile WHERE ID_SUFFISSO_SR = @id");
            await connection.ExecuteScalarAsync<int>(query.ToString(), new { id = idSuffisso, disponibile = false }, tran);
            return;
        }

        public async Task DisimpegnaSuffissoSRAsync(int idSuffisso, IDbConnection connection, IDbTransaction tran)
        {
            var query = new StringBuilder();
            query.Append($"UPDATE {NomeTabella.SuffissoSR} SET DISPONIBILE = @disponibile WHERE ID_SUFFISSO_SR = @id");
            await connection.ExecuteScalarAsync<int>(query.ToString(), new { id = idSuffisso, disponibile = true }, tran);
            return;
        }

        public async Task<int> InsertStazioneRipetitriceAsync(StazioneRipetitrice entity)
        {
            int idStazioneRipetitrice = 0;
            StringBuilder query = new StringBuilder();
            StringBuilder queryTO = new StringBuilder();

            query.Append($" INSERT INTO {NomeTabella.StazioniRipetitrici} (");
            query.Append("REGIONE, ");
            query.Append("CODICE_REGIONE, ");
            query.Append("PROVINCIA, ");
            query.Append("CODICE_PROVINCIA, ");
            query.Append("COMUNE, ");
            query.Append("CODICE_COMUNE, ");
            query.Append("INDIRIZZO, ");
            query.Append("CIVICO, ");
            query.Append("CAP, ");
            query.Append("ID_ANAGRAFICA_ISPETTORATO, ");
            query.Append("LATITUDINE, ");
            query.Append("LONGITUDINE, ");
            query.Append("NOME_OPERATORE, ");
            query.Append("COGNOME_OPERATORE, ");
            query.Append("CODICE_FISCALE_OPERATORE, ");
            query.Append("NOMINATIVO_OPERATORE, ");
            query.Append("REGIONE_RESIDENZA_OPERATORE, ");
            query.Append("CODICE_REGIONE_RESIDENZA_OPERATORE, ");
            query.Append("PROVINCIA_RESIDENZA_OPERATORE, ");
            query.Append("CODICE_PROVINCIA_RESIDENZA_OPERATORE, ");
            query.Append("COMUNE_RESIDENZA_OPERATORE, ");
            query.Append("CODICE_COMUNE_RESIDENZA_OPERATORE, ");
            query.Append("INDIRIZZO_RESIDENZA_OPERATORE, ");
            query.Append("NUMERO_CIVICO_RESIDENZA_OPERATORE, ");
            query.Append("CAP_RESIDENZA_OPERATORE, ");
            query.Append("EMAIL_OPERATORE, ");
            query.Append("TELEFONO_OPERATORE) ");
            query.Append(" OUTPUT INSERTED.ID_STAZIONE_RIPETITRICE ");
            query.Append(" VALUES (");

            query.Append("@REGIONE, ");
            query.Append("@CODICE_REGIONE, ");
            query.Append("@PROVINCIA, ");
            query.Append("@CODICE_PROVINCIA, ");
            query.Append("@COMUNE, ");
            query.Append("@CODICE_COMUNE, ");
            query.Append("@INDIRIZZO, ");
            query.Append("@CIVICO, ");
            query.Append("@CAP, ");
            query.Append("@ID_ANAGRAFICA_ISPETTORATO, ");
            query.Append("@LATITUDINE, ");
            query.Append("@LONGITUDINE, ");
            query.Append("@NOME_OPERATORE, ");
            query.Append("@COGNOME_OPERATORE, ");
            query.Append("@CODICE_FISCALE_OPERATORE, ");
            query.Append("@NOMINATIVO_OPERATORE, ");
            query.Append("@REGIONE_RESIDENZA_OPERATORE, ");
            query.Append("@CODICE_REGIONE_RESIDENZA_OPERATORE, ");
            query.Append("@PROVINCIA_RESIDENZA_OPERATORE, ");
            query.Append("@CODICE_PROVINCIA_RESIDENZA_OPERATORE, ");
            query.Append("@COMUNE_RESIDENZA_OPERATORE, ");
            query.Append("@CODICE_COMUNE_RESIDENZA_OPERATORE, ");
            query.Append("@INDIRIZZO_RESIDENZA_OPERATORE, ");
            query.Append("@NUMERO_CIVICO_RESIDENZA_OPERATORE, ");
            query.Append("@CAP_RESIDENZA_OPERATORE, ");
            query.Append("@EMAIL_OPERATORE, ");
            query.Append("@TELEFONO_OPERATORE) ");

            queryTO.Append($" INSERT INTO {NomeTabella.StazioniRipetitriciTriggerOutput} (");
            queryTO.Append("ID_STAZIONE_RIPETITRICE, ");
            queryTO.Append("REGIONE, ");
            queryTO.Append("CODICE_REGIONE, ");
            queryTO.Append("PROVINCIA, ");
            queryTO.Append("CODICE_PROVINCIA, ");
            queryTO.Append("COMUNE, ");
            queryTO.Append("CODICE_COMUNE, ");
            queryTO.Append("INDIRIZZO, ");
            queryTO.Append("CIVICO, ");
            queryTO.Append("CAP, ");
            queryTO.Append("ID_ANAGRAFICA_ISPETTORATO, ");
            queryTO.Append("LATITUDINE, ");
            queryTO.Append("LONGITUDINE, ");
            queryTO.Append("NOME_OPERATORE, ");
            queryTO.Append("COGNOME_OPERATORE, ");
            queryTO.Append("CODICE_FISCALE_OPERATORE, ");
            queryTO.Append("NOMINATIVO_OPERATORE, ");
            queryTO.Append("REGIONE_RESIDENZA_OPERATORE, ");
            queryTO.Append("CODICE_REGIONE_RESIDENZA_OPERATORE, ");
            queryTO.Append("PROVINCIA_RESIDENZA_OPERATORE, ");
            queryTO.Append("CODICE_PROVINCIA_RESIDENZA_OPERATORE, ");
            queryTO.Append("COMUNE_RESIDENZA_OPERATORE, ");
            queryTO.Append("CODICE_COMUNE_RESIDENZA_OPERATORE, ");
            queryTO.Append("INDIRIZZO_RESIDENZA_OPERATORE, ");
            queryTO.Append("NUMERO_CIVICO_RESIDENZA_OPERATORE, ");
            queryTO.Append("CAP_RESIDENZA_OPERATORE, ");
            queryTO.Append("EMAIL_OPERATORE, ");
            queryTO.Append("TELEFONO_OPERATORE, ");
            queryTO.Append("TIMESTAMP_MODIFICA)");
            queryTO.Append(" VALUES (");

            queryTO.Append("@ID_STAZIONE_RIPETITRICE, ");
            queryTO.Append("@REGIONE, ");
            queryTO.Append("@CODICE_REGIONE, ");
            queryTO.Append("@PROVINCIA, ");
            queryTO.Append("@CODICE_PROVINCIA, ");
            queryTO.Append("@COMUNE, ");
            queryTO.Append("@CODICE_COMUNE, ");
            queryTO.Append("@INDIRIZZO, ");
            queryTO.Append("@CIVICO, ");
            queryTO.Append("@CAP, ");
            queryTO.Append("@ID_ANAGRAFICA_ISPETTORATO, ");
            queryTO.Append("@LATITUDINE, ");
            queryTO.Append("@LONGITUDINE, ");
            queryTO.Append("@NOME_OPERATORE, ");
            queryTO.Append("@COGNOME_OPERATORE, ");
            queryTO.Append("@CODICE_FISCALE_OPERATORE, ");
            queryTO.Append("@NOMINATIVO_OPERATORE, ");
            queryTO.Append("@REGIONE_RESIDENZA_OPERATORE, ");
            queryTO.Append("@CODICE_REGIONE_RESIDENZA_OPERATORE, ");
            queryTO.Append("@PROVINCIA_RESIDENZA_OPERATORE, ");
            queryTO.Append("@CODICE_PROVINCIA_RESIDENZA_OPERATORE, ");
            queryTO.Append("@COMUNE_RESIDENZA_OPERATORE, ");
            queryTO.Append("@CODICE_COMUNE_RESIDENZA_OPERATORE, ");
            queryTO.Append("@INDIRIZZO_RESIDENZA_OPERATORE, ");
            queryTO.Append("@NUMERO_CIVICO_RESIDENZA_OPERATORE, ");
            queryTO.Append("@CAP_RESIDENZA_OPERATORE, ");
            queryTO.Append("@EMAIL_OPERATORE, ");
            queryTO.Append("@TELEFONO_OPERATORE, ");
            queryTO.Append("@TIMESTAMP_MODIFICA)");


            using (var connection = CreateConnection())
            {
                connection.Open();
                using (var tran = connection.BeginTransaction())
                {
                    try
                    {
                        idStazioneRipetitrice = await connection.ExecuteScalarAsync<int>(query.ToString(), entity, tran);
                        entity.ID_STAZIONE_RIPETITRICE = idStazioneRipetitrice;
                        entity.TIMESTAMP_MODIFICA = DateTime.Now;
                        await connection.ExecuteScalarAsync(queryTO.ToString(), entity, tran);
                        tran.Commit();
                        return idStazioneRipetitrice;
                    }
                    catch (Exception ex)
                    {
                        tran.Rollback();
                        throw;
                    }
                }
            }
        }

        public async Task<int> InsertAutorizzazioneSRAsync(AutorizzazioneSR dto)
        {
            StringBuilder query = new StringBuilder();
            StringBuilder queryTO = new StringBuilder();

            query.Append($" INSERT INTO {NomeTabella.AutorizzazioniSR} (");
            query.Append("CODICE_DOMANDA, ");
            query.Append("COGNOME, ");
            query.Append("NOME, ");
            query.Append("DENOMINAZIONE, ");
            query.Append("CODICE_FISCALE, ");
            query.Append("SESSO, ");
            query.Append("LUOGO_NASCITA, ");
            query.Append("CODICE_LUOGO_NASCITA, ");
            query.Append("DATA_NASCITA, ");
            query.Append("REGIONE_RESIDENZA, ");
            query.Append("CODICE_REGIONE_RESIDENZA, ");
            query.Append("PROVINCIA_RESIDENZA, ");
            query.Append("CODICE_PROVINCIA_RESIDENZA, ");
            query.Append("COMUNE_RESIDENZA, ");
            query.Append("CODICE_COMUNE_RESIDENZA, ");
            query.Append("INDIRIZZO_RESIDENZA, ");
            query.Append("CAP_RESIDENZA, ");
            query.Append("ID_ANAGRAFICA_ISPETTORATO, ");
            query.Append("PEC, ");
            query.Append("EMAIL, ");
            query.Append("TELEFONO, ");
            query.Append("ID_TIPO_ASSEGNATARIO, ");
            query.Append("RINNOVO, ");
            query.Append("ID_AUTORIZZAZIONE_RINNOVATA, ");
            query.Append("NUMERO_AUTORIZZAZIONE, ");
            query.Append("ID_TIPO_AUTORIZZAZIONE, ");
            query.Append("ID_STATO_AUTORIZZAZIONE, ");
            query.Append("DATA_RILASCIO, ");
            query.Append("DATA_SCADENZA, ");
            query.Append("ID_RADIOAMATORE_SR, ");
            query.Append("CODICE_PROTOCOLLO_ENTRATA, ");
            query.Append("DATA_PROTOCOLLO_ENTRATA, ");
            query.Append("CODICE_PROTOCOLLO_USCITA, ");
            query.Append("DATA_PROTOCOLLO_USCITA, ");
            query.Append("UTENTE, ");
            query.Append("ID_STAZIONE_RIPETITRICE)");
            query.Append(" OUTPUT INSERTED.ID_AUTORIZZAZIONE_SR ");
            query.Append(" VALUES ( ");

            query.Append("@CODICE_DOMANDA, ");
            query.Append("@COGNOME, ");
            query.Append("@NOME, ");
            query.Append("@DENOMINAZIONE, ");
            query.Append("@CODICE_FISCALE, ");
            query.Append("@SESSO, ");
            query.Append("@LUOGO_NASCITA, ");
            query.Append("@CODICE_LUOGO_NASCITA, ");
            query.Append("@DATA_NASCITA, ");
            query.Append("@REGIONE_RESIDENZA, ");
            query.Append("@CODICE_REGIONE_RESIDENZA, ");
            query.Append("@PROVINCIA_RESIDENZA, ");
            query.Append("@CODICE_PROVINCIA_RESIDENZA, ");
            query.Append("@COMUNE_RESIDENZA, ");
            query.Append("@CODICE_COMUNE_RESIDENZA, ");
            query.Append("@INDIRIZZO_RESIDENZA, ");
            query.Append("@CAP_RESIDENZA, ");
            query.Append("@ID_ANAGRAFICA_ISPETTORATO, ");
            query.Append("@PEC, ");
            query.Append("@EMAIL, ");
            query.Append("@TELEFONO, ");
            query.Append("@ID_TIPO_ASSEGNATARIO, ");
            query.Append("@RINNOVO, ");
            query.Append("@ID_AUTORIZZAZIONE_RINNOVATA, ");
            query.Append("@NUMERO_AUTORIZZAZIONE, ");
            query.Append("@ID_TIPO_AUTORIZZAZIONE, ");
            query.Append("@ID_STATO_AUTORIZZAZIONE, ");
            query.Append("@DATA_RILASCIO, ");
            query.Append("@DATA_SCADENZA, ");
            query.Append("@ID_RADIOAMATORE_SR, ");
            query.Append("@CODICE_PROTOCOLLO_ENTRATA, ");
            query.Append("@DATA_PROTOCOLLO_ENTRATA, ");
            query.Append("@CODICE_PROTOCOLLO_USCITA, ");
            query.Append("@DATA_PROTOCOLLO_USCITA, ");
            query.Append("@UTENTE, ");
            query.Append("@ID_STAZIONE_RIPETITRICE) ");

            //----------------------------------------------------

            queryTO.Append($" INSERT INTO {NomeTabella.AutorizzazioniSRTriggerOutput} (");
            queryTO.Append("ID_AUTORIZZAZIONE_SR, ");
            queryTO.Append("CODICE_DOMANDA, ");
            queryTO.Append("COGNOME, ");
            queryTO.Append("NOME, ");
            queryTO.Append("DENOMINAZIONE, ");
            queryTO.Append("CODICE_FISCALE, ");
            queryTO.Append("SESSO, ");
            queryTO.Append("LUOGO_NASCITA, ");
            queryTO.Append("CODICE_LUOGO_NASCITA, ");
            queryTO.Append("DATA_NASCITA, ");
            queryTO.Append("REGIONE_RESIDENZA, ");
            queryTO.Append("CODICE_REGIONE_RESIDENZA, ");
            queryTO.Append("PROVINCIA_RESIDENZA, ");
            queryTO.Append("CODICE_PROVINCIA_RESIDENZA, ");
            queryTO.Append("COMUNE_RESIDENZA, ");
            queryTO.Append("CODICE_COMUNE_RESIDENZA, ");
            queryTO.Append("INDIRIZZO_RESIDENZA, ");
            queryTO.Append("CAP_RESIDENZA, ");
            queryTO.Append("ID_ANAGRAFICA_ISPETTORATO, ");
            queryTO.Append("PEC, ");
            queryTO.Append("EMAIL, ");
            queryTO.Append("TELEFONO, ");
            queryTO.Append("ID_TIPO_ASSEGNATARIO, ");
            queryTO.Append("RINNOVO, ");
            queryTO.Append("ID_AUTORIZZAZIONE_RINNOVATA, ");
            queryTO.Append("NUMERO_AUTORIZZAZIONE, ");
            queryTO.Append("ID_TIPO_AUTORIZZAZIONE, ");
            queryTO.Append("ID_STATO_AUTORIZZAZIONE, ");
            queryTO.Append("DATA_RILASCIO, ");
            queryTO.Append("DATA_SCADENZA, ");
            queryTO.Append("ID_RADIOAMATORE_SR, ");
            queryTO.Append("CODICE_PROTOCOLLO_ENTRATA, ");
            queryTO.Append("DATA_PROTOCOLLO_ENTRATA, ");
            queryTO.Append("CODICE_PROTOCOLLO_USCITA, ");
            queryTO.Append("DATA_PROTOCOLLO_USCITA, ");
            queryTO.Append("UTENTE, ");
            queryTO.Append("ID_STAZIONE_RIPETITRICE, ");
            queryTO.Append("TIMESTAMP_MODIFICA)");

            queryTO.Append(" VALUES ( ");


            queryTO.Append("@ID_AUTORIZZAZIONE_SR, ");
            queryTO.Append("@CODICE_DOMANDA, ");
            queryTO.Append("@COGNOME, ");
            queryTO.Append("@NOME, ");
            queryTO.Append("@DENOMINAZIONE, ");
            queryTO.Append("@CODICE_FISCALE, ");
            queryTO.Append("@SESSO, ");
            queryTO.Append("@LUOGO_NASCITA, ");
            queryTO.Append("@CODICE_LUOGO_NASCITA, ");
            queryTO.Append("@DATA_NASCITA, ");
            queryTO.Append("@REGIONE_RESIDENZA, ");
            queryTO.Append("@CODICE_REGIONE_RESIDENZA, ");
            queryTO.Append("@PROVINCIA_RESIDENZA, ");
            queryTO.Append("@CODICE_PROVINCIA_RESIDENZA, ");
            queryTO.Append("@COMUNE_RESIDENZA, ");
            queryTO.Append("@CODICE_COMUNE_RESIDENZA, ");
            queryTO.Append("@INDIRIZZO_RESIDENZA, ");
            queryTO.Append("@CAP_RESIDENZA, ");
            queryTO.Append("@ID_ANAGRAFICA_ISPETTORATO, ");
            queryTO.Append("@PEC, ");
            queryTO.Append("@EMAIL, ");
            queryTO.Append("@TELEFONO, ");
            queryTO.Append("@ID_TIPO_ASSEGNATARIO, ");
            queryTO.Append("@RINNOVO, ");
            queryTO.Append("@ID_AUTORIZZAZIONE_RINNOVATA, ");
            queryTO.Append("@NUMERO_AUTORIZZAZIONE, ");
            queryTO.Append("@ID_TIPO_AUTORIZZAZIONE, ");
            queryTO.Append("@ID_STATO_AUTORIZZAZIONE, ");
            queryTO.Append("@DATA_RILASCIO, ");
            queryTO.Append("@DATA_SCADENZA, ");
            queryTO.Append("@ID_RADIOAMATORE_SR, ");
            queryTO.Append("@CODICE_PROTOCOLLO_ENTRATA, ");
            queryTO.Append("@DATA_PROTOCOLLO_ENTRATA, ");
            queryTO.Append("@CODICE_PROTOCOLLO_USCITA, ");
            queryTO.Append("@DATA_PROTOCOLLO_USCITA, ");
            queryTO.Append("@UTENTE, ");
            queryTO.Append("@ID_STAZIONE_RIPETITRICE, ");
            queryTO.Append("@TIMESTAMP_MODIFICA)");

            using (var connection = CreateConnection())
            {
                connection.Open();
                using (var tran = connection.BeginTransaction())
                {
                    try
                    {
                        int idAutorizzazione = await connection.ExecuteScalarAsync<int>(query.ToString(), dto, tran);
                        dto.ID_AUTORIZZAZIONE_SR = idAutorizzazione;
                        dto.TIMESTAMP_MODIFICA = DateTime.Now;
                        await connection.ExecuteScalarAsync(queryTO.ToString(), dto, tran);
                        tran.Commit();
                        return idAutorizzazione;
                    }
                    catch (Exception ex)
                    {
                        tran.Rollback();
                        throw;
                    }
                }
            }
        }

        public async Task<long> GetNextSequenceValue()
        {
            string query = $"SELECT NEXT VALUE FOR {NomeSequence.CodiceAutorizzazioneSR}";
            using (var connection = CreateConnection())
            {
                connection.Open();
                long result = await connection.ExecuteScalarAsync<long>(query);
                return result;
            }
        }

        public async Task<int> UpdateIdRadioamatoreByIdAsync(int idRadioamatore, int idAutorizzazione)
        {
            var query = new StringBuilder();
            query.Append($" UPDATE {NomeTabella.AutorizzazioniSR} ");
            query.Append($" SET {nameof(AutorizzazioneSR.ID_RADIOAMATORE_SR)} = @IdRadioamatore ");
            query.Append($" WHERE {nameof(AutorizzazioneSR.ID_AUTORIZZAZIONE_SR)} = @IdAutorizzazione ");

            using (var connection = CreateConnection())
            {
                connection.Open();

                int result = await connection.ExecuteAsync(
                    query.ToString(),
                    new
                    {
                        IdRadioamatore = idRadioamatore,
                        IdAutorizzazione = idAutorizzazione
                    });
                return result;
            }
        }

        /*

Stato (mostrare tutti gli stati)
Nominativo stazione ripetitrice
Regione di ubicazione della stazione ripetitrice
Provincia di ubicazione della stazione ripetitrice
Comune di ubicazione della stazione ripetitrice
         */

        public async Task<List<AutorizzazioneSR>> GetAutorizzazioniSRByFilterAsync(AutorizzazioneSRFilterDto parametri)
        {

            var query = PreparaParametriRicercaAutorizzazioniSRQuery(parametri);

            if (!string.IsNullOrWhiteSpace(parametri.IdIspettorato))
                query.Item1.Append($" AND i.{nameof(AnagraficaIspettorato.DENOMINAZIONE)} LIKE @Ispettorato ");

            query.Item2.Add("Ispettorato", $"%{parametri.IdIspettorato}%");

            using (var connection = CreateConnection())
            {
                return (await connection.QueryAsync<AutorizzazioneSR, StazioneRipetitrice, AnagraficaIspettorato, StatoAutorizzazione, TipoAutorizzazione, RadioamatoreSR, AutorizzazioneSR>(query.Item1.ToString(),
                    (a, s, i, st, t, r) =>
                    {

                        a.ANAGRAFICA_ISPETTORATO = i;
                        a.STAZIONE_RIPETITRICE = s;
                        a.NOMINATIVO_STAZIONE_RIPETITRICE = r;
                        a.STATO_AUTORIZZAZIONE = st;
                        a.TIPO_AUTORIZZAZIONE = t;
                        return a;
                    },
                    splitOn: $"{nameof(StazioneRipetitrice.ID_STAZIONE_RIPETITRICE)}, {nameof(AnagraficaIspettorato.ID)}, {nameof(StatoAutorizzazione.ID_STATO_AUTORIZZAZIONE)}, " +
                                $"{nameof(TipoAutorizzazione.ID_TIPO_AUTORIZZAZIONE)}, {nameof(RadioamatoreSR.ID_RADIOAMATORE_SR)}",
                    param: query.Item2
                    )).AsQueryable().ToList();
            }
        }

        public async Task<List<AutorizzazioneSR>> GetAutorizzazioniSRByIspettoratoConGestioneSubentriAsync(AutorizzazioneSRFilterDtoBase parametri, string idSpettorato)
        {

            var query = PreparaParametriRicercaAutorizzazioniSRQuery(parametri);

            if (!string.IsNullOrWhiteSpace(idSpettorato))
                query.Item1.Append($" AND ( i.{nameof(AnagraficaIspettorato.ID)} = @Ispettorato OR i.{nameof(AnagraficaIspettorato.ID_SUBENTRO)} = @Ispettorato ) "); 

            query.Item2.Add("Ispettorato", idSpettorato);

            using (var connection = CreateConnection())
            {
                return (await connection.QueryAsync<AutorizzazioneSR, StazioneRipetitrice, AnagraficaIspettorato, StatoAutorizzazione, TipoAutorizzazione, RadioamatoreSR, AutorizzazioneSR>(query.Item1.ToString(),
                    (a, s, i, st, t, r) =>
                    {

                        a.ANAGRAFICA_ISPETTORATO = i;
                        a.STAZIONE_RIPETITRICE = s;
                        a.NOMINATIVO_STAZIONE_RIPETITRICE = r;
                        a.STATO_AUTORIZZAZIONE = st;
                        a.TIPO_AUTORIZZAZIONE = t;
                        return a;
                    },
                    splitOn: $"{nameof(StazioneRipetitrice.ID_STAZIONE_RIPETITRICE)}, {nameof(AnagraficaIspettorato.ID)}, {nameof(StatoAutorizzazione.ID_STATO_AUTORIZZAZIONE)}, " +
                                $"{nameof(TipoAutorizzazione.ID_TIPO_AUTORIZZAZIONE)}, {nameof(RadioamatoreSR.ID_RADIOAMATORE_SR)}",
                    param: query.Item2
                    )).AsQueryable().ToList();
            }
        }

        public async Task<AutorizzazioneSR> GetAutorizzazioniSRByIdAsync(int idAutorizzazione)
        {
            var query = new StringBuilder();
            query.Append($" SELECT p.*, s.*, i.* FROM {NomeTabella.AutorizzazioniSR} AS p");
            query.Append($" JOIN {NomeTabella.StatiAutorizzazione} AS s");
            query.Append($"     ON p.{nameof(AutorizzazioneSR.ID_STATO_AUTORIZZAZIONE)} = s.{nameof(StatoAutorizzazione.ID_STATO_AUTORIZZAZIONE)}");
            query.Append($" JOIN {NomeTabella.AnagraficaIspettorati} AS i");
            query.Append($"     ON p.{nameof(AutorizzazioneSR.ID_ANAGRAFICA_ISPETTORATO)} = i.{nameof(AnagraficaIspettorato.ID)}");
            query.Append($" WHERE p.{nameof(AutorizzazioneSR.ID_AUTORIZZAZIONE_SR)} = @Id");

            using (var connection = CreateConnection())
            {
                return (await connection.QueryAsync<AutorizzazioneSR, StatoAutorizzazione, AnagraficaIspettorato, AutorizzazioneSR>(query.ToString(),
                        (p, s, i) =>
                        {
                            p.STATO_AUTORIZZAZIONE = s;
                            p.ANAGRAFICA_ISPETTORATO = i;
                            return p;
                        },
                        splitOn: $"{nameof(StatoAutorizzazione.ID_STATO_AUTORIZZAZIONE)}, {nameof(AnagraficaIspettorato.ID)}",
                        param: new
                        {
                            id = idAutorizzazione
                        }
                        )).AsQueryable().FirstOrDefault();
            }
        }

        public async Task<List<RadioamatoreSR>> GetRadioamatoriSRAndAutorizzazioniByFilterAsync(string cognome, string nome, string denominazione, string codiceFiscale, DateTime? dataDiNascita, string regione, string provincia, string idIspettorato, List<string> statiNominativo, DateTime? dataRilascio, string nominativo)
        {
            var query = new StringBuilder();

            query.Append($" SELECT r.*, i.*, a.NUMERO_AUTORIZZAZIONE as NUMERO_AUTORIZZAZIONE FROM {NomeTabella.RadioamatoriSR} as r");
            query.Append($" JOIN {NomeTabella.AnagraficaIspettorati} i on r.{nameof(RadioamatoreSR.ID_ANAGRAFICA_ISPETTORATO)} = i.{nameof(AnagraficaIspettorato.ID)} ");
            query.Append($" LEFT JOIN {NomeTabella.AutorizzazioniSR} a on r.{nameof(RadioamatoreSR.ID_RADIOAMATORE_SR)} = a.{nameof(AutorizzazioneSR.ID_RADIOAMATORE_SR)}");
            query.Append($" WHERE (@Cognome is NULL or r.{nameof(RadioamatoreSR.COGNOME)} LIKE '%' + @Cognome + '%')");
            query.Append($" AND (@Nome is NULL or r.{nameof(RadioamatoreSR.NOME)} LIKE '%' + @Nome + '%')");
            query.Append($" AND (@Denominazione is NULL or r.{nameof(RadioamatoreSR.DENOMINAZIONE)} LIKE '%' + @Denominazione + '%')");
            query.Append($" AND (@CodiceFiscale is NULL or r.{nameof(RadioamatoreSR.CODICE_FISCALE)} LIKE '%' + @CodiceFiscale + '%')");
            query.Append($" AND (@DataNascita IS NULL OR r.{nameof(RadioamatoreSR.DATA_NASCITA)} = @DataNascita) ");
            query.Append($" AND (@Regione is NULL or r.{nameof(RadioamatoreSR.REGIONE)} LIKE '%' + @Regione + '%')");
            query.Append($" AND (@Provincia is NULL or r.{nameof(RadioamatoreSR.PROVINCIA)} LIKE '%' + @Provincia + '%')");
            query.Append($" AND (@IdIspettorato is NULL or i.{nameof(AnagraficaIspettorato.ID)} LIKE '%' + @IdIspettorato + '%')");
            query.Append($" AND (@DataRilascio IS NULL OR r.{nameof(RadioamatoreSR.DATA_RILASCIO)} = @DataRilascio) ");
            query.Append($" AND (@Nominativo is NULL or r.{nameof(RadioamatoreSR.CODICE_NOMINATIVO)} LIKE '%' + @Nominativo + '%')");
            if (statiNominativo != null && statiNominativo.Any())
            {
                query.Append($" AND (r.{nameof(RadioamatoreSR.STATO_NOMINATIVO)} IN @StatiNominativo )");
            }

            using (var connection = CreateConnection())
            {
                return (await connection.QueryAsync<RadioamatoreSR, AnagraficaIspettorato, string, RadioamatoreSR>(query.ToString(),
                        (r, i, NUMERO_AUTORIZZAZIONE) =>
                        {
                            r.ANAGRAFICA_ISPETTORATO = i;
                            r.NUMERO_AUTORIZZAZIONE_SR = NUMERO_AUTORIZZAZIONE;
                            return r;
                        },
                        splitOn: $"{nameof(AnagraficaIspettorato.ID)}, {nameof(AutorizzazioneSR.NUMERO_AUTORIZZAZIONE)}",
                        param: new
                        {
                            Cognome = cognome,
                            Nome = nome,
                            Denominazione = denominazione,
                            CodiceFiscale = codiceFiscale,
                            DataNascita = dataDiNascita,
                            Regione = regione,
                            Provincia = provincia,
                            IdIspettorato = idIspettorato,
                            StatiNominativo = statiNominativo,
                            DataRilascio = dataRilascio,
                            Nominativo = nominativo
                        }
                        )).AsQueryable().ToList();
            }
        }


        public async Task<List<RadioamatoreSR>> GetRadiomatoriSRByFilter(RadioamatoreSRFilterDto parametri)
        {
            var query = PreparaParametriRicercaRadioamatoreSRQuery(parametri);

            if (!string.IsNullOrWhiteSpace(parametri.IdIspettorato))
                query.Item1.Append($" AND (@IdIspettorato is NULL or i.{nameof(AnagraficaIspettorato.ID)} LIKE '%' + @IdIspettorato + '%')");

            query.Item2.Add("IdIspettorato", parametri.IdIspettorato);

            using (var connection = CreateConnection())
            {
                return (await connection.QueryAsync<RadioamatoreSR, AnagraficaIspettorato, RadioamatoreSR>(query.Item1.ToString(),
                (r, i) =>
                {
                    r.ANAGRAFICA_ISPETTORATO = i;
                    return r;
                },
                splitOn: $"{nameof(StatoIscrizioneSWL.ID_STATO_ISCRIZIONE_SWL)}, {nameof(AnagraficaIspettorato.ID)}",
                        param: query.Item2
                    )).AsQueryable().ToList();
            }
        }

        public async Task<List<RadioamatoreSR>> GetRadiomatoriSRByIspettoratoFilter(RadioamatoreSRFilterDtoBase parametri, string idIspettorato)
        {
            var query = PreparaParametriRicercaRadioamatoreSRQuery(parametri);

            if (!string.IsNullOrWhiteSpace(idIspettorato))
                query.Item1.Append($" AND ( @IdIspettorato IS NULL OR i.{nameof(AnagraficaIspettorato.ID)} = @IdIspettorato OR i.{nameof(AnagraficaIspettorato.ID_SUBENTRO)} = @IdIspettorato ) ");

            query.Item2.Add("IdIspettorato", idIspettorato);

            using (var connection = CreateConnection())
            {
                return (await connection.QueryAsync<RadioamatoreSR, AnagraficaIspettorato, RadioamatoreSR>(query.Item1.ToString(),
                (r, i) =>
                {
                    r.ANAGRAFICA_ISPETTORATO = i;
                    return r;
                },
                splitOn: $"{nameof(StatoIscrizioneSWL.ID_STATO_ISCRIZIONE_SWL)}, {nameof(AnagraficaIspettorato.ID)}",
                        param: query.Item2
                    )).AsQueryable().ToList();
            }
        }

        public async Task<RadioamatoreSR> GetRadioamatoriSRByIdAsync(int idRadioamatore)
        {
            var query = new StringBuilder();

            query.Append($" SELECT r.* ");
            query.Append($" FROM {NomeTabella.RadioamatoriSR} as r ");
            query.Append($" WHERE r.{nameof(RadioamatoreSR.ID_RADIOAMATORE_SR)} = @IdRadioamatore");

            using (var connection = CreateConnection())
            {
                return (await connection.QueryAsync<RadioamatoreSR>(query.ToString(),
                            param: new
                            {
                                IdRadioamatore = idRadioamatore
                            }
                        )).FirstOrDefault();
            }
        }

        public async Task<RadioamatoreSR> GetRadioamatoreSRByCodiceNominativoAsync(string codiceNominativo)
        {
            var query = new StringBuilder();

            query.Append($" SELECT r.* ");
            query.Append($" FROM {NomeTabella.RadioamatoriSR} as r ");
            query.Append($" WHERE r.{nameof(RadioamatoreSR.CODICE_NOMINATIVO)} = @CodiceNominativo");

            using (var connection = CreateConnection())
            {
                return (await connection.QueryAsync<RadioamatoreSR>(query.ToString(),
                            param: new
                            {
                                CodiceNominativo = codiceNominativo
                            }
                        )).FirstOrDefault();
            }
        }

        public async Task<List<string>> GetNominativiAutorizzazioniSRInScadenzaByCodiceFiscale(string codiceFiscale)
        {
            var query = new StringBuilder();
            query.Append($" SELECT r.{nameof(RadioamatoreSR.CODICE_NOMINATIVO)} from {NomeTabella.AutorizzazioniSR} a");
            query.Append($" JOIN {NomeTabella.RadioamatoriSR} r on a.{nameof(AutorizzazioneSR.ID_RADIOAMATORE_SR)} = r.{nameof(RadioamatoreSR.ID_RADIOAMATORE_SR)}");
            query.Append($" WHERE a.{nameof(AutorizzazioneSR.CODICE_FISCALE)} =  @codiceFiscale ");
            query.Append($" AND {nameof(AutorizzazioneSR.ID_STATO_AUTORIZZAZIONE)} = 3");

            using (var connection = CreateConnection())
            {
                var result = await connection.QueryAsync<string>(query.ToString(), param: new { codiceFiscale });
                return result?.ToList();
            }
        }

        public async Task<int> SetProtocolloAsync(string codiceDomandaFE, DateTime dataProtocollo, string numeroProtocollo)
        {
            var queryAutorizzazioneSR = new StringBuilder();
            queryAutorizzazioneSR.Append($" UPDATE {NomeTabella.AutorizzazioniSR} ");
            queryAutorizzazioneSR.Append($" SET {nameof(AutorizzazioneSR.CODICE_PROTOCOLLO_ENTRATA)} = @numeroProtocollo ");
            queryAutorizzazioneSR.Append($" , {nameof(AutorizzazioneSR.DATA_PROTOCOLLO_ENTRATA)} = @dataProtocollo ");
            queryAutorizzazioneSR.Append($" WHERE {nameof(AutorizzazioneSR.CODICE_DOMANDA)} = @codiceDomandaFE ");

            var queryRadioamatoreSR = new StringBuilder();
            queryRadioamatoreSR.Append($" UPDATE {NomeTabella.RadioamatoriSR} ");
            queryRadioamatoreSR.Append($" SET {nameof(RadioamatoreSR.PROTOCOLLO_PRESENTAZIONE_FE)} = @numeroProtocollo ");
            queryRadioamatoreSR.Append($" , {nameof(RadioamatoreSR.DATA_PROTOCOLLO_PRESENTAZIONE_FE)} = @dataProtocollo ");
            queryRadioamatoreSR.Append($" WHERE {nameof(RadioamatoreSR.CODICE_DOMANDA)} = @codiceDomandaFE ");

            using (var connection = CreateConnection())
            {
                connection.Open();
                using (var tran = connection.BeginTransaction())
                {
                    try
                    {
                        var param = new
                        {
                            dataProtocollo,
                            codiceDomandaFE,
                            numeroProtocollo
                        };

                        var ret = await connection.ExecuteAsync(queryAutorizzazioneSR.ToString(), param, tran);

                        _ = await connection.ExecuteAsync(queryRadioamatoreSR.ToString(), param, tran);

                        tran.Commit();

                        return ret;
                    }
                    catch (Exception)
                    {
                        tran.Rollback();
                        throw;
                    }
                }
            }
        }

        public async Task<List<AutorizzazioneSR>> GetAutorizzazioniSRDaSettareInScadenzaAsync()
        {
            var query = new StringBuilder();

            query.Append($" SELECT a.* ");
            query.Append($" FROM {NomeTabella.AutorizzazioniSR} as a ");
            query.Append($" WHERE a.{nameof(AutorizzazioneSR.ID_STATO_AUTORIZZAZIONE)} = @IdStatoAutorizzazione");
            query.Append($" AND CAST(GETDATE() AS DATE) >= CAST(DATEADD(YEAR, -1, a.{nameof(AutorizzazioneSR.DATA_SCADENZA)}) AS DATE) ");

            using (var connection = CreateConnection())
            {
                return (await connection.QueryAsync<AutorizzazioneSR>(query.ToString(),
                    param: new
                    {
                        IdStatoAutorizzazione = (int)EnumStatoAutorizzazione.Attiva
                    }
                    )).AsQueryable().ToList();
            }
        }

        public async Task<List<AutorizzazioneSR>> GetAutorizzazioniSRDaSettareScaduteAsync()
        {
            var query = new StringBuilder();

            query.Append($" SELECT a.* ");
            query.Append($" FROM {NomeTabella.AutorizzazioniSR} as a ");
            query.Append($" WHERE a.{nameof(AutorizzazioneSR.ID_STATO_AUTORIZZAZIONE)} = @IdStatoAutorizzazione");
            query.Append($" AND CAST(GETDATE() AS DATE) > CAST(a.{nameof(AutorizzazioneSR.DATA_SCADENZA)} AS DATE) ");

            using (var connection = CreateConnection())
            {
                return (await connection.QueryAsync<AutorizzazioneSR>(query.ToString(),
                    param: new
                    {
                        IdStatoAutorizzazione = (int)EnumStatoAutorizzazione.InScadenza
                    }
                    )).AsQueryable().ToList();
            }
        }

        public async Task<int> UpdateStatoAutorizzazioniSRByIdAsync(short idStatoAutorizzazione, List<int> listaIdAutorizzazioniSR)
        {
            var query = new StringBuilder();
            query.Append($" UPDATE {NomeTabella.AutorizzazioniSR} SET ");
            query.Append($" {nameof(AutorizzazioneSR.ID_STATO_AUTORIZZAZIONE)} = @IdStato");
            query.Append($" WHERE {nameof(AutorizzazioneSR.ID_AUTORIZZAZIONE_SR)} IN @IdAutorizzazione");

            using (var connection = CreateConnection())
            {
                connection.Open();

                int result = await connection.ExecuteAsync(
                    query.ToString(),
                    new
                    {
                        IdStato = idStatoAutorizzazione,
                        IdAutorizzazione = listaIdAutorizzazioniSR
                    });
                return result;
            }
        }

        public async Task<List<RadioamatoreSR>> GetNominativiSRDaSettareScadutiAsync()
        {
            var query = new StringBuilder();
            query.Append($" SELECT r.* ");
            query.Append($" FROM {NomeTabella.RadioamatoriSR} AS r");
            query.Append($" WHERE CAST(GETDATE() AS DATE) > CAST(r.{nameof(RadioamatoreSR.DATA_SCADENZA)} AS DATE) ");
            query.Append($" AND r.{nameof(RadioamatoreSR.STATO_NOMINATIVO)} = @StatoNominativo");

            using (var connection = CreateConnection())
            {
                var ret = (await connection.QueryAsync<RadioamatoreSR>(query.ToString(),
                            param: new
                            {
                                StatoNominativo = EnumStatiRadioamatori.ATTIVO.ToString()
                            }
                            )).AsQueryable().ToList();

                return ret;
            }
        }

        public async Task<int> UpdateStatoNominativoSRByIdAsync(string statoNominativo, int idRadioamatore)
        {
            var query = new StringBuilder();
            query.Append($" UPDATE {NomeTabella.RadioamatoriSR} SET ");
            query.Append($" {nameof(RadioamatoreSR.STATO_NOMINATIVO)} = @StatoNominativo");
            query.Append($" WHERE {nameof(RadioamatoreSR.ID_RADIOAMATORE_SR)} = @IdRadioamatore");

            using (var connection = CreateConnection())
            {
                connection.Open();
                using (var tran = connection.BeginTransaction())
                {
                    try
                    {
                        if (statoNominativo.EqualsIgnoreCaseAndTrim("ANNULLATO"))
                        {
                            var radioamatore = await GetRadioamatoriSRByIdAsync(idRadioamatore);
                            if (radioamatore.ID_SUFFISSO_SR.HasValue)
                            {
                                await DisimpegnaSuffissoSRAsync(radioamatore.ID_SUFFISSO_SR.Value, connection, tran);
                            }
                        }

                        int result = await connection.ExecuteAsync(
                                                        query.ToString(),
                                                        new
                                                        {
                                                            StatoNominativo = statoNominativo,
                                                            IdRadioamatore = idRadioamatore
                                                        }, tran);

                        tran.Commit();

                        return result > 0 ? idRadioamatore : -1;
                    }
                    catch (Exception)
                    {
                        tran.Rollback();
                        throw;
                    }
                }
            }
        }


        public async Task<int> SetNominativiSRScadutiByIdAsync(List<RadioamatoreSR> radioamatoriSR)
        {
            var querySetSuffissiDisponibili = new StringBuilder();
            querySetSuffissiDisponibili.Append($" UPDATE {NomeTabella.SuffissoSR} ");
            querySetSuffissiDisponibili.Append($" SET {nameof(SuffissoSR.DISPONIBILE)} = 1 ");
            querySetSuffissiDisponibili.Append($" WHERE {nameof(SuffissoSR.ID_SUFFISSO_SR)} IN @ListaIdSuffisso ");

            var querySetNominativiScaduti = new StringBuilder();
            querySetNominativiScaduti.Append($" UPDATE {NomeTabella.RadioamatoriSR} ");
            querySetNominativiScaduti.Append($" SET {nameof(RadioamatoreSR.STATO_NOMINATIVO)} = @StatoNominativi ");
            querySetNominativiScaduti.Append($" WHERE {nameof(RadioamatoreSR.ID_RADIOAMATORE_SR)} IN @ListaIdRadioamatore ");

            using (var connection = CreateConnection())
            {
                connection.Open();

                using (var tran = connection.BeginTransaction())
                {
                    try
                    {
                        if (radioamatoriSR.Any(r => r.ID_SUFFISSO_SR.HasValue))
                        {
                            _ = await connection.ExecuteAsync(
                                querySetSuffissiDisponibili.ToString(),
                                new
                                {
                                    ListaIdSuffisso = radioamatoriSR.Select(r => r.ID_SUFFISSO_SR).ToList()
                                },
                                tran);
                        }

                        var result = await connection.ExecuteAsync(
                            querySetNominativiScaduti.ToString(),
                            new
                            {
                                StatoNominativi = EnumStatiRadioamatori.SCADUTO.ToString(),
                                ListaIdRadioamatore = radioamatoriSR.Select(r => r.ID_RADIOAMATORE_SR).ToList()
                            },
                            tran);

                        tran.Commit();

                        return result;
                    }
                    catch (Exception)
                    {
                        tran.Rollback();
                        throw;
                    }
                }
            }
        }

        public async Task<int> UpdateStatoAutorizzazioneSRByIdAsync(short idStatoAutorizzazione, int idAutorizzazione)
        {
            var query = new StringBuilder();
            query.Append($" UPDATE {NomeTabella.AutorizzazioniSR} SET ");
            query.Append($" {nameof(AutorizzazioneSR.ID_STATO_AUTORIZZAZIONE)} = @IdStato");
            query.Append($" WHERE {nameof(AutorizzazioneSR.ID_AUTORIZZAZIONE_SR)} = @IdAutorizzazione");

            using (var connection = CreateConnection())
            {
                connection.Open();

                int result = await connection.ExecuteAsync(
                    query.ToString(),
                    new
                    {
                        IdStato = idStatoAutorizzazione,
                        IdAutorizzazione = idAutorizzazione
                    });
                return result > 0 ? idAutorizzazione : -1;
            }
        }

        public async Task<int> SetProtocolloUscitaAsync(int idAutorizzazione,
                                                    DateTime dataProtocolloUscita,
                                                    string codiceProtocolloUscita,
                                                    DateTime dataRilascioAutorizzazione)
        {
            var querySetProtocollo = new StringBuilder();
            querySetProtocollo.Append($" UPDATE {NomeTabella.AutorizzazioniSR} ");
            querySetProtocollo.Append($" SET {nameof(AutorizzazioneSR.ID_STATO_AUTORIZZAZIONE)} = @idStato ");
            querySetProtocollo.Append($" , {nameof(AutorizzazioneSR.CODICE_PROTOCOLLO_USCITA)} = @codiceProtocollo ");
            querySetProtocollo.Append($" , {nameof(AutorizzazioneSR.DATA_PROTOCOLLO_USCITA)} = @dataProtocollo ");
            querySetProtocollo.Append($" , {nameof(AutorizzazioneSR.DATA_RILASCIO)} = @dataRilascio ");
            querySetProtocollo.Append($" WHERE {nameof(AutorizzazioneSR.ID_AUTORIZZAZIONE_SR)} = @idAutorizzazione ");

            using (var connection = CreateConnection())
            {
                connection.Open();

                var autorizzazione = await GetAutorizzazioniSRByIdAsync(idAutorizzazione);

                using (var tran = connection.BeginTransaction())
                {
                    try
                    {
                        _ = await connection.ExecuteAsync(querySetProtocollo.ToString(),
                            new
                            {
                                idStato = (short)EnumStatoAutorizzazione.Attiva,
                                codiceProtocollo = codiceProtocolloUscita,
                                dataProtocollo = dataProtocolloUscita,
                                dataRilascio = dataRilascioAutorizzazione,
                                idAutorizzazione
                            },
                            tran);

                        if (autorizzazione.ID_RADIOAMATORE_SR.HasValue)
                        {
                            var scadenzaNominativo = new StringBuilder();
                            scadenzaNominativo.Append($"UPDATE {NomeTabella.RadioamatoriSR} ");
                            scadenzaNominativo.Append($"SET {nameof(RadioamatoreSR.DATA_SCADENZA)} = @DataScadenza ,");
                            scadenzaNominativo.Append($" {nameof(RadioamatoreSR.STATO_NOMINATIVO)} = @StatoNominativo , ");
                            scadenzaNominativo.Append($" {nameof(RadioamatoreSR.PROTOCOLLO)} = @Protocollo , ");
                            scadenzaNominativo.Append($" {nameof(RadioamatoreSR.DATA_PROTOCOLLO)} = @DataProtocollo , ");
                            scadenzaNominativo.Append($" {nameof(RadioamatoreSR.DATA_RILASCIO)} = @DataRilascio ");
                            scadenzaNominativo.Append($"WHERE {nameof(RadioamatoreSR.ID_RADIOAMATORE_SR)} = @IdRadioamatore ");

                            var dataScadenza = autorizzazione.DATA_SCADENZA?.AddYears(1);

                            _ = await connection.ExecuteAsync(scadenzaNominativo.ToString(),
                                new
                                {
                                    DataScadenza = dataScadenza,
                                    StatoNominativo = "ATTIVO",
                                    Protocollo = codiceProtocolloUscita,
                                    DataProtocollo = dataProtocolloUscita,
                                    DataRilascio = dataRilascioAutorizzazione,
                                    IdRadioamatore = autorizzazione.ID_RADIOAMATORE_SR
                                }, tran);
                        }

                        if (autorizzazione.ID_AUTORIZZAZIONE_RINNOVATA.HasValue)
                        {
                            var scadenzaNominativo = new StringBuilder();
                            scadenzaNominativo.Append($"UPDATE {NomeTabella.AutorizzazioniSR} ");
                            scadenzaNominativo.Append($"SET {nameof(AutorizzazioneSR.ID_STATO_AUTORIZZAZIONE)} = @idStato ");
                            scadenzaNominativo.Append($"WHERE {nameof(AutorizzazioneSR.ID_AUTORIZZAZIONE_SR)} = @IdAutorizzazione ");

                            _ = await connection.ExecuteAsync(scadenzaNominativo.ToString(),
                                new { idStato = (short)EnumStatoAutorizzazione.Disattivata, IdAutorizzazione = autorizzazione.ID_AUTORIZZAZIONE_RINNOVATA }, tran);
                        }

                        tran.Commit();

                        return idAutorizzazione;
                    }
                    catch (Exception)
                    {
                        tran.Rollback();
                        throw;
                    }
                }
            }
        }

        #region Metodi privati

        private (StringBuilder, Dictionary<string, object>) PreparaParametriRicercaAutorizzazioniSRQuery(AutorizzazioneSRFilterDtoBase parametri)
        {
            // query di ricerca
            var query = new StringBuilder();

            query.Append($" SELECT a.*, s.*, i.*, st.*, t.*, r.* FROM {NomeTabella.AutorizzazioniSR} as a");
            query.Append($" JOIN {NomeTabella.StazioniRipetitrici} s on a.{nameof(AutorizzazioneSR.ID_STAZIONE_RIPETITRICE)} = s.{nameof(StazioneRipetitrice.ID_STAZIONE_RIPETITRICE)}");
            query.Append($" JOIN {NomeTabella.AnagraficaIspettorati} i on i.{nameof(AnagraficaIspettorato.ID)} = s.{nameof(StazioneRipetitrice.ID_ANAGRAFICA_ISPETTORATO)}");
            query.Append($" JOIN {NomeTabella.StatiAutorizzazione} AS st on a.{nameof(AutorizzazioneSR.ID_STATO_AUTORIZZAZIONE)} = st.{nameof(StatoAutorizzazione.ID_STATO_AUTORIZZAZIONE)}");
            query.Append($" JOIN {NomeTabella.TipoAutorizzazione} AS t on a.{nameof(AutorizzazioneSR.ID_TIPO_AUTORIZZAZIONE)} = t.{nameof(TipoAutorizzazione.ID_TIPO_AUTORIZZAZIONE)}");
            query.Append($" LEFT JOIN {NomeTabella.RadioamatoriSR} r on a.{nameof(AutorizzazioneSR.ID_RADIOAMATORE_SR)} = r.{nameof(RadioamatoreSR.ID_RADIOAMATORE_SR)}");
            query.Append($" WHERE (@Cognome is NULL or a.{nameof(AutorizzazioneSR.COGNOME)} LIKE '%' + @Cognome + '%')");
            query.Append($" AND (@Nome is NULL or a.{nameof(AutorizzazioneSR.NOME)} LIKE '%' + @Nome + '%')");
            query.Append($" AND (@Denominazione is NULL or a.{nameof(AutorizzazioneSR.DENOMINAZIONE)} LIKE '%' + @Denominazione + '%')");
            query.Append($" AND (@CodiceFiscale is NULL or a.{nameof(AutorizzazioneSR.CODICE_FISCALE)} LIKE '%' + @CodiceFiscale + '%')");
            query.Append($" AND (@DataNascita IS NULL OR a.{nameof(Autorizzazione.DATA_NASCITA)}  = @DataNascita) ");
            query.Append($" AND (@Regione is NULL or a.{nameof(AutorizzazioneSR.REGIONE_RESIDENZA)} LIKE '%' + @Regione + '%')");
            query.Append($" AND (@RegioneSR  is NULL or s.{nameof(StazioneRipetitrice.CODICE_REGIONE)} LIKE '%' + @RegioneSR + '%')");
            query.Append($" AND (@ProvinciaSR is NULL or s.{nameof(StazioneRipetitrice.CODICE_PROVINCIA)} LIKE '%' + @ProvinciaSR + '%')");
            query.Append($" AND (@ComuneSR is NULL or s.{nameof(StazioneRipetitrice.CODICE_COMUNE)} LIKE '%' + @ComuneSR + '%')");
            query.Append($" AND (@NumeroAutorizzazione is NULL or a.{nameof(AutorizzazioneSR.NUMERO_AUTORIZZAZIONE)} LIKE '%' + @NumeroAutorizzazione + '%')");
            query.Append($" AND (@IdStatoAutorizzazione is NULL or a.{nameof(AutorizzazioneSR.ID_STATO_AUTORIZZAZIONE)} = @IdStatoAutorizzazione )");
            query.Append($" AND (@DataRilascio IS NULL OR a.{nameof(Autorizzazione.DATA_RILASCIO)}  = @DataRilascio) ");
            query.Append($" AND (@Nominativo is NULL or s.{nameof(StazioneRipetitrice.NOMINATIVO_OPERATORE)} LIKE '%' + @Nominativo + '%')");
            query.Append($" AND (@NominativoStazione is NULL or r.{nameof(RadioamatoreSR.CODICE_NOMINATIVO)} LIKE '%' + @NominativoStazione + '%')");
            query.Append($" AND (@IdRadioamatore is NULL or r.{nameof(RadioamatoreSR.ID_RADIOAMATORE_SR)} = @IdRadioamatore )");
            query.Append($" AND (@IdAutorizzazioneSR is NULL or a.{nameof(AutorizzazioneSR.ID_AUTORIZZAZIONE_SR)} = @IdAutorizzazioneSR )");

            // Parametri per la ricerca
            var _p = new Dictionary<string, object>();

            _p.Add("Cognome", parametri.Cognome);
            _p.Add("Nome", parametri.Nome);
            _p.Add("Denominazione", parametri.Denominazione);
            _p.Add("CodiceFiscale", parametri.CodiceFiscale);
            _p.Add("DataNascita", parametri.DataDiNascita);
            _p.Add("Regione", parametri.CodiceRegione);
            _p.Add("RegioneSR", parametri.CodiceRegioneStazione);
            _p.Add("ProvinciaSR", parametri.CodiceProvinciaStazione);
            _p.Add("ComuneSR", parametri.CodiceComuneStazione);
            _p.Add("NumeroAutorizzazione", parametri.NumeroAutorizzazione);
            _p.Add("IdStatoAutorizzazione", parametri.IdStatoAutorizzazione);
            _p.Add("DataRilascio", parametri.DataRilascio);
            _p.Add("Nominativo", parametri.NominativoOperatore);
            _p.Add("NominativoStazione", parametri.NominativoStazione);
            _p.Add("IdRadioamatore", parametri.IdRadioamatore);
            _p.Add("IdAutorizzazioneSR", parametri.IdAutorizzazioneSR);

            return (query, _p);
        }

        //Questo metodo è stato utilizzato one shot per il popolamento della tabella dei suffissi
        //Lo lascio privato per eventuali usi futuri
        private async Task InsertAllSuffissiAsync()
        {
            var query = "INSERT INTO [TB_SUFFISSI_SR] values (@suffisso, @prefisso, @disponibile)";
            string suffisso = string.Empty;
            string prefisso = string.Empty;
            bool disponibile = true;
            var count = 0;
            var alfabeto = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";

            using (var connection = CreateConnection())
            {
                connection.Open();


                for (int i = 0; i < 10; i++)
                {
                    for (int a1 = 25; a1 >= 0; a1--)
                        for (int a2 = 25; a2 >= 0; a2--)
                            for (int a3 = 25; a3 >= 0; a3--)

                            {
                                prefisso = $"IR{i}";
                                suffisso = $"{alfabeto[a1]}{alfabeto[a2]}{alfabeto[a3]}";
                                try
                                {
                                    count += await connection.ExecuteScalarAsync<int>(query, new { prefisso, suffisso, disponibile });
                                }
                                catch (Exception ex)
                                {
                                    throw ex;
                                }
                            }
                }
            }
        }

        private (StringBuilder, Dictionary<string, object>) PreparaParametriRicercaRadioamatoreSRQuery(RadioamatoreSRFilterDtoBase parametri)
        {
            var query = new StringBuilder();

            query.Append($" SELECT r.*, i.* FROM {NomeTabella.RadioamatoriSR} as r");
            query.Append($" JOIN {NomeTabella.AnagraficaIspettorati} i on r.{nameof(RadioamatoreSR.ID_ANAGRAFICA_ISPETTORATO)} = i.{nameof(AnagraficaIspettorato.ID)} ");
            query.Append($" WHERE (@Cognome is NULL or r.{nameof(RadioamatoreSR.COGNOME)} LIKE '%' + @Cognome + '%')");
            query.Append($" AND (@Nome is NULL or r.{nameof(RadioamatoreSR.NOME)} LIKE '%' + @Nome + '%')");
            query.Append($" AND (@Denominazione is NULL or r.{nameof(RadioamatoreSR.DENOMINAZIONE)} LIKE '%' + @Denominazione + '%')");
            query.Append($" AND (@CodiceFiscale is NULL or r.{nameof(RadioamatoreSR.CODICE_FISCALE)} LIKE '%' + @CodiceFiscale + '%')");
            query.Append($" AND (@DataNascita IS NULL OR r.{nameof(RadioamatoreSR.DATA_NASCITA)} = @DataNascita) ");
            query.Append($" AND (@Regione is NULL or r.{nameof(RadioamatoreSR.REGIONE)} LIKE '%' + @Regione + '%')");
            query.Append($" AND (@Provincia is NULL or r.{nameof(RadioamatoreSR.PROVINCIA)} LIKE '%' + @Provincia + '%')");            
            query.Append($" AND (@DataRilascio IS NULL OR r.{nameof(RadioamatoreSR.DATA_RILASCIO)} = @DataRilascio) ");
            query.Append($" AND (@Nominativo is NULL or r.{nameof(RadioamatoreSR.CODICE_NOMINATIVO)} LIKE '%' + @Nominativo + '%')");
            if (parametri.StatiNominativo != null && parametri.StatiNominativo.Any())
            {
                query.Append($" AND (r.{nameof(RadioamatoreSR.STATO_NOMINATIVO)} IN @StatiNominativo )");
            }

            // Parametri per la ricerca
            var _p = new Dictionary<string, object>();

            _p.Add("Nome", parametri.Nome);
            _p.Add("Cognome", parametri.Cognome);
            _p.Add("Denominazione", parametri.Denominazione);
            _p.Add("CodiceFiscale", parametri.CodiceFiscale);
            _p.Add("DataNascita", parametri.DataDiNascita);
            _p.Add("Regione", parametri.Regione);
            _p.Add("Provincia", parametri.Provincia);
            _p.Add("DataRilascio", parametri.DataRilascio);
            _p.Add("Nominativo", parametri.Nominativo);            
            _p.Add("StatiNominativo", parametri.StatiNominativo);

            return (query, _p);
        }
        #endregion
    }
}
