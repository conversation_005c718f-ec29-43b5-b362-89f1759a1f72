﻿using Dapper;
using Invitalia.Misure.Utilities.WebApi.Refit.XRM;
using Microsoft.Extensions.Configuration;
using MicroSV_Radioamatori.BL.Enums;
using MicroSV_Radioamatori.BL.IncomingDTOs;
using MicroSV_Radioamatori.DAL.Common;
using MicroSV_Radioamatori.DAL.Entities;
using MicroSV_Radioamatori.DAL.Interfaces;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MicroSV_Radioamatori.DAL.Services
{
    public class ContestDal : BaseDal, IContestDal
    {
        public ContestDal(IConfiguration conf, HttpClientFactory httpClientFactory) : base(conf, httpClientFactory)
        {
        }

        public async Task<List<AnagraficaContest>> GetAnagraficaContestAsync()
        {
            var query = new StringBuilder();
            query.Append($" SELECT * FROM {NomeTabella.AnagraficaContest} WHERE {nameof(AnagraficaContest.IS_VISIBILE)} = @visible ORDER BY {nameof(AnagraficaContest.DATA_INIZIO)}");

            using (var connection = CreateConnection())
            {
                return (await connection.QueryAsync<AnagraficaContest>(query.ToString(), new { visible = true })).ToList();
            }
        }

        /// <summary>
        /// Verifica se il nominativo associato alla Domanda risulta attivo sui contest.
        /// </summary>
        /// <param name="codiceDomandaFE">Codice Domanda FE</param>
        /// <param name="contest">Contest per i quali verificare se il nominativo è attivo</param>
        /// <returns>true se il nominativo è già attivo su uno dei contest altrimenti false</returns>
        public async Task<bool> EsisteNominativoAttivoSuContest(string codiceDomandaFE, List<string> contest)
        {
            var query = new StringBuilder();
            query.Append($" SELECT c.* FROM {NomeTabella.Radioamatori} AS r ");
            query.Append($" INNER JOIN {NomeTabella.Contest} AS c ");
            query.Append($" ON r.{nameof(Radioamatore.ID_RADIOAMATORE)} = c.{nameof(Contest.ID_RADIOAMATORE)} ");
            query.Append($" WHERE r.{nameof(Radioamatore.CODICE_DOMANDA_FE)} = @codiceDomandaFE AND r.{nameof(Radioamatore.STATO_NOMINATIVO)} = @statoNominativo");
            query.Append($" AND r.{nameof(Radioamatore.TIPO_NOMINATIVO)} = @tipoNominativo AND c.{nameof(Contest.CODICE_ANAGRAFICA_CONTEST)} IN @listaContest");

            using (var connection = CreateConnection())
            {
                return (await connection.QueryAsync<Contest>(query.ToString(), param: new
                {
                    codiceDomandaFE,
                    tipoNominativo = TipoNominativo.CONTEST.ToString(),
                    statoNominativo = StatoNominativo.ATTIVO.ToString(),
                    listaContest = contest
                })).Any();
            }
        }

        public async Task<List<AnagraficaContest>> GetContestbyRadioamatoreAsync(string idradioamatore)
        {
            var query = new StringBuilder();
            query.Append($" SELECT * FROM {NomeTabella.AnagraficaContest} WHERE {nameof(AnagraficaContest.CODICE)} IN " +
                $" (SELECT {nameof(Contest.CODICE_ANAGRAFICA_CONTEST)} FROM {NomeTabella.Contest} WHERE {nameof(Contest.ID_RADIOAMATORE)} = @idradioamatore) " +
                $" ORDER BY {nameof(AnagraficaContest.DATA_INIZIO)}");

            using (var connection = CreateConnection())
            {
                var ret = (await connection.QueryAsync<AnagraficaContest>(query.ToString(), new { idradioamatore = idradioamatore })).ToList();
                return ret;
            }
        }

        public async Task<List<Contest>> GetContesByListRadioamatoritAsync(List<int> elencoIdRadioamatori)
        {
            var query = new StringBuilder();
            query.Append($" SELECT * FROM {NomeTabella.Contest} AS c ");
            query.Append($"     JOIN {NomeTabella.AnagraficaContest} AS a ");
            query.Append($"         ON c.{nameof(Contest.CODICE_ANAGRAFICA_CONTEST)} = a.{nameof(AnagraficaContest.CODICE)} ");
            query.Append($" WHERE {nameof(Contest.ID_RADIOAMATORE)} IN @idradioamatori ORDER BY {nameof(Contest.ID)}");

            using (var connection = CreateConnection())
            {
                return connection.Query<Contest, AnagraficaContest, Contest>(query.ToString(),
                    (c, a) =>
                    {
                        c.ANAGRAFICA_CONTEST = a;
                        return c;
                    },
                    splitOn: $"{nameof(Contest.CODICE_ANAGRAFICA_CONTEST)}, {nameof(AnagraficaContest.CODICE)}",
                    param: new { idradioamatori = elencoIdRadioamatori })
                    .AsQueryable()
                    .ToList();
            }
        }

    }
}
