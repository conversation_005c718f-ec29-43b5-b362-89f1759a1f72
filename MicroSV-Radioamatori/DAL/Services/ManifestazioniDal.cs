﻿using Dapper;
using Invitalia.Misure.Utilities.WebApi.Refit.XRM;
using Microsoft.Extensions.Configuration;
using MicroSV_Radioamatori.DAL.Common;
using MicroSV_Radioamatori.DAL.Entities;
using MicroSV_Radioamatori.DAL.Interfaces;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MicroSV_Radioamatori.DAL.Services
{
    public class ManifestazioniDal : BaseDal, IManifestazioneDal
    {
        public ManifestazioniDal(IConfiguration conf, HttpClientFactory httpClientFactory) : base(conf, httpClientFactory)
        {
        }

        public async Task<List<TipoManifestazione>> GetTipologieManifestazioni()
        {
            var query = new StringBuilder();
            query.Append($" SELECT * FROM {NomeTabella.TipoManifestazione}");

            using (var connection = CreateConnection())
            {
                return (await connection.QueryAsync<TipoManifestazione>(query.ToString())).ToList();
            }
        }

        public async Task<List<Manifestazione>> GetManifestazioniByRadioamatoreId(int idRadioamatore)
        {
            var query = new StringBuilder();
            query.Append($" SELECT * FROM {NomeTabella.Manifestazione}");
            query.Append($" WHERE {nameof(Manifestazione.ID_RADIOAMATORE)} = @IdRadioamatore ");

            using (var connection = CreateConnection())
            {
                return (await connection.QueryAsync<Manifestazione>(query.ToString(), 
                    new 
                    { 
                        IdRadioamatore = idRadioamatore 
                    }
                    )).ToList();
            }
        }
    }
}
