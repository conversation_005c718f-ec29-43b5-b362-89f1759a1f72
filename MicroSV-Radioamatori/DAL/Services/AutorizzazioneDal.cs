﻿using Dapper;
using Microsoft.Extensions.Configuration;
using MicroSV_Radioamatori.BL.DTOs;
using MicroSV_Radioamatori.BL.Enums;
using MicroSV_Radioamatori.BL.IncomingDTOs;
using MicroSV_Radioamatori.BL.ValueObject;
using MicroSV_Radioamatori.DAL.Common;
using MicroSV_Radioamatori.DAL.Entities;
using MicroSV_Radioamatori.DAL.Interfaces;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Reflection.Metadata;
using System.Text;
using System.Threading.Tasks;

namespace MicroSV_Radioamatori.DAL.Services
{
    public class AutorizzazioneDal : BaseDal, IAutorizzazioneDal
    {
        public AutorizzazioneDal(IConfiguration conf) : base(conf)
        {

        }

        public async Task<List<Autorizzazione>> GetAutorizzazioniByFilterAsync(AutorizzazioneFilterDto parametri)
        {
            var query = PreparaParametriRicercaAutorizzazioniQuery(parametri);

            if (!string.IsNullOrWhiteSpace(parametri.IdIspettorato))
                query.Item1.Append($" AND (@IdIspettorato IS NULL OR p.{nameof(Autorizzazione.ID_ANAGRAFICA_ISPETTORATO)}  LIKE '%' + @IdIspettorato + '%') ");

            query.Item2.Add("IdIspettorato", parametri.IdIspettorato);


            using (var connection = CreateConnection())
            {
                return (await connection.QueryAsync<Autorizzazione, StatoAutorizzazione, AnagraficaIspettorato, TipoAutorizzazione, Radioamatore, Autorizzazione>(query.Item1.ToString(),
                (p, s, i, t, r) =>
                {
                    p.STATO_AUTORIZZAZIONE = s;
                    p.ANAGRAFICA_ISPETTORATO = i;
                    p.RADIOAMATORE = r;
                    p.CODICE_NOMINATIVO = r?.CODICE_NOMINATIVO;
                    p.TIPO_AUTORIZZAZIONE = t;
                    return p;
                },
                splitOn: $"{nameof(StatoAutorizzazione.ID_STATO_AUTORIZZAZIONE)}, {nameof(AnagraficaIspettorato.ID)}, {nameof(Autorizzazione.ID_TIPO_AUTORIZZAZIONE)},{nameof(Radioamatore.ID_RADIOAMATORE)}",
                        param: query.Item2
                        )).AsQueryable().ToList();
            }
        }

        public async Task<List<Autorizzazione>>GetAutorizzazioniByCodiceFiscaleAsync(string codiceFiscale)
        {
            var query = new StringBuilder();

            query.Append($" SELECT a.* ");
            query.Append($" FROM {NomeTabella.Autorizzazioni} AS a ");
            query.Append($" WHERE a.{nameof(Autorizzazione.CODICE_FISCALE)} = @CodiceFiscale");

            using (var connection = CreateConnection())
            {
                return (await connection.QueryAsync<Autorizzazione>(query.ToString(),
                            param: new
                            {
                                CodiceFiscale = codiceFiscale
                            }
                        )).AsQueryable().ToList();
            }
        }

        public async Task<List<Autorizzazione>> GetAutorizzazioniByIspettoratoConGestioneSubentriAsync(AutorizzazioneFilterDtoBase parametri, string idIspettorato)
        {
            var query = PreparaParametriRicercaAutorizzazioniQuery(parametri);

            if (!string.IsNullOrWhiteSpace(idIspettorato))
                query.Item1.Append($" AND (@IdIspettorato IS NULL OR i.{nameof(AnagraficaIspettorato.ID)} = @IdIspettorato OR i.{nameof(AnagraficaIspettorato.ID_SUBENTRO)} = @IdIspettorato) ");

            query.Item2.Add("IdIspettorato", idIspettorato);


            using (var connection = CreateConnection())
            {
                return (await connection.QueryAsync<Autorizzazione, StatoAutorizzazione, AnagraficaIspettorato, TipoAutorizzazione, Radioamatore, Autorizzazione>(query.Item1.ToString(),
                (p, s, i, t, r) =>
                {
                    p.STATO_AUTORIZZAZIONE = s;
                    p.ANAGRAFICA_ISPETTORATO = i;
                    p.RADIOAMATORE = r;
                    p.CODICE_NOMINATIVO = r?.CODICE_NOMINATIVO;
                    p.TIPO_AUTORIZZAZIONE = t;
                    return p;
                },
                splitOn: $"{nameof(StatoAutorizzazione.ID_STATO_AUTORIZZAZIONE)}, {nameof(AnagraficaIspettorato.ID)}, {nameof(Autorizzazione.ID_TIPO_AUTORIZZAZIONE)},{nameof(Radioamatore.ID_RADIOAMATORE)}",
                        param: query.Item2
                        )).AsQueryable().ToList();
            }
        }

        public async Task<Autorizzazione> GetAutorizzazioniByIdAsync(int idAutorizzazione)
        {
            var query = new StringBuilder();
            query.Append($" SELECT p.*, s.*, i.*, r.* FROM {NomeTabella.Autorizzazioni} AS p");
            query.Append($" JOIN {NomeTabella.StatiAutorizzazione} AS s");
            query.Append($"     ON p.{nameof(Autorizzazione.ID_STATO_AUTORIZZAZIONE)} = s.{nameof(StatoAutorizzazione.ID_STATO_AUTORIZZAZIONE)}");
            query.Append($" JOIN {NomeTabella.AnagraficaIspettorati} AS i");
            query.Append($"     ON p.{nameof(Autorizzazione.ID_ANAGRAFICA_ISPETTORATO)} = i.{nameof(AnagraficaIspettorato.ID)}");
            query.Append($" LEFT OUTER JOIN {NomeTabella.Radioamatori} AS r");
            query.Append($"     ON p.{nameof(Autorizzazione.ID_RADIOAMATORE)} = r.{nameof(Radioamatore.ID_RADIOAMATORE)}");
            query.Append($" WHERE p.{nameof(Autorizzazione.ID_AUTORIZZAZIONE)} = @Id");



            using (var connection = CreateConnection())
            {
                return (await connection.QueryAsync<Autorizzazione, StatoAutorizzazione, AnagraficaIspettorato, Radioamatore, Autorizzazione>(query.ToString(),
                        (p, s, i, r) =>
                        {
                            p.STATO_AUTORIZZAZIONE = s;
                            p.ANAGRAFICA_ISPETTORATO = i;
                            p.RADIOAMATORE = r;
                            return p;
                        },
                        splitOn: $"{nameof(StatoAutorizzazione.ID_STATO_AUTORIZZAZIONE)}, {nameof(AnagraficaIspettorato.ID)}, {nameof(Radioamatore.ID_RADIOAMATORE)}",
                        param: new
                        {
                            id = idAutorizzazione
                        }
                        )).AsQueryable().FirstOrDefault();
            }
        }

        public async Task<Autorizzazione> GetAutorizzazioneByNumeroAsync(string numeroAutorizzazione)
        {
            var query = new StringBuilder();
            query.Append($" SELECT p.*, s.*, i.*, r.* FROM {NomeTabella.Autorizzazioni} AS p");
            query.Append($" JOIN {NomeTabella.StatiAutorizzazione} AS s");
            query.Append($"     ON p.{nameof(Autorizzazione.ID_STATO_AUTORIZZAZIONE)} = s.{nameof(StatoAutorizzazione.ID_STATO_AUTORIZZAZIONE)}");
            query.Append($" JOIN {NomeTabella.AnagraficaIspettorati} AS i");
            query.Append($"     ON p.{nameof(Autorizzazione.ID_ANAGRAFICA_ISPETTORATO)} = i.{nameof(AnagraficaIspettorato.ID)}");

            query.Append($" LEFT OUTER JOIN {NomeTabella.Radioamatori} AS r");
            query.Append($"     ON p.{nameof(Autorizzazione.ID_RADIOAMATORE)} = r.{nameof(Radioamatore.ID_RADIOAMATORE)}");

            query.Append($" WHERE p.{nameof(Autorizzazione.NUMERO_AUTORIZZAZIONE)} = @NumeroAutorizzazione");

            using (var connection = CreateConnection())
            {
                return (await connection.QueryAsync<Autorizzazione, StatoAutorizzazione, AnagraficaIspettorato, Radioamatore, Autorizzazione>(query.ToString(),
                    (p, s, i, r) =>
                    {
                        p.STATO_AUTORIZZAZIONE = s;
                        p.ANAGRAFICA_ISPETTORATO = i;
                        p.RADIOAMATORE = r;
                        return p;
                    },
                    splitOn: $"{nameof(StatoAutorizzazione.ID_STATO_AUTORIZZAZIONE)}, {nameof(AnagraficaIspettorato.ID)}, {nameof(Radioamatore.ID_RADIOAMATORE)}",
                    param: new
                    {
                        NumeroAutorizzazione = numeroAutorizzazione.Trim()
                    }
                    )).AsQueryable().FirstOrDefault();
            }
        }

        public async Task<List<Autorizzazione>> GetAutorizzazioniDaSettareInScadenzaAsync()
        {
            var query = new StringBuilder();

            query.Append($" SELECT a.* ");
            query.Append($" FROM {NomeTabella.Autorizzazioni} AS a ");
            query.Append($" WHERE a.{nameof(Autorizzazione.ID_STATO_AUTORIZZAZIONE)} = @IdStatoAutorizzazione ");
            query.Append($" AND CAST(GETDATE() AS DATE) >= CAST(DATEADD(YEAR, -1, a.{nameof(Autorizzazione.DATA_SCADENZA)}) AS DATE) ");

            using (var connection = CreateConnection())
            {
                return (await connection.QueryAsync<Autorizzazione>(query.ToString(),
                            param: new
                            {
                                IdStatoAutorizzazione = (int)EnumStatoAutorizzazione.Attiva
                            }
                        )).AsQueryable().ToList();
            }
        }

        public async Task<List<Autorizzazione>> GetAutorizzazioniDaSettareScaduteAsync()
        {
            var query = new StringBuilder();

            query.Append($" SELECT a.* ");
            query.Append($" FROM {NomeTabella.Autorizzazioni} AS a ");
            query.Append($" WHERE a.{nameof(Autorizzazione.ID_STATO_AUTORIZZAZIONE)} = @IdStatoAutorizzazione ");
            query.Append($" AND CAST(GETDATE() AS DATE) > CAST(a.{nameof(Autorizzazione.DATA_SCADENZA)} AS DATE) ");

            using (var connection = CreateConnection())
            {
                return (await connection.QueryAsync<Autorizzazione>(query.ToString(),
                            param: new
                            {
                                IdStatoAutorizzazione = (int)EnumStatoAutorizzazione.InScadenza
                            }
                        )).AsQueryable().ToList();
            }
        }

        public async Task<int> InsertAutorizzazioneAsync(Autorizzazione autorizzazione)
        {
            var query = new StringBuilder();
            query.Append($" INSERT INTO {NomeTabella.Autorizzazioni}");
            query.Append($" ([CODICE_DOMANDA]");
            query.Append($",[COGNOME]");
            query.Append($",[NOME]");
            query.Append($",[DENOMINAZIONE]");
            query.Append($",[CODICE_FISCALE]");
            query.Append($",[SESSO]");
            query.Append($",[LUOGO_NASCITA]");
            query.Append($",[CODICE_LUOGO_NASCITA]");
            query.Append($",[DATA_NASCITA]");
            query.Append($",[REGIONE_RESIDENZA]");
            query.Append($",[CODICE_REGIONE_RESIDENZA]");
            query.Append($",[PROVINCIA_RESIDENZA]");
            query.Append($",[CODICE_PROVINCIA_RESIDENZA]");
            query.Append($",[COMUNE_RESIDENZA]");
            query.Append($",[CODICE_COMUNE_RESIDENZA]");
            query.Append($",[INDIRIZZO_RESIDENZA]");
            query.Append($",[CAP_RESIDENZA]");
            query.Append($",[ID_ANAGRAFICA_ISPETTORATO]");
            query.Append($",[PEC]");
            query.Append($",[EMAIL]");
            query.Append($",[TELEFONO]");
            query.Append($",[ID_TIPO_ASSEGNATARIO]");
            query.Append($",[NUMERO_AUTORIZZAZIONE]");
            query.Append($",[ID_TIPO_AUTORIZZAZIONE]");
            query.Append($",[ID_STATO_AUTORIZZAZIONE]");
            query.Append($",[DATA_RILASCIO]");
            query.Append($",[DATA_SCADENZA]");
            query.Append($",[RIFERIMENTO_DOCUMENTO_AUTORIZZAZIONE]");
            query.Append($",[DOCUMENTO_AUTORIZZAZIONE_VISIBILE_FE]");
            query.Append($",[ID_RADIOAMATORE]");
            query.Append($",[CODICE_PROTOCOLLO_ENTRATA]");
            query.Append($",[DATA_PROTOCOLLO_ENTRATA]");
            query.Append($",[CODICE_PROTOCOLLO_USCITA]");
            query.Append($",[DATA_PROTOCOLLO_USCITA]");
            query.Append($",[UTENTE]");
            query.Append($",[RINNOVO]");
            query.Append($",[ID_AUTORIZZAZIONE_RINNOVATA]");
            query.Append(") OUTPUT Inserted.ID_AUTORIZZAZIONE VALUES ");

            query.Append($"(@CodiceDomanda, ");
            query.Append($"@Cognome, ");
            query.Append($"@Nome, ");
            query.Append($"@Denominazione, ");
            query.Append($"@CodiceFiscale, ");
            query.Append($"@Sesso, ");
            query.Append($"@LuogoNascita, ");
            query.Append($"@CodiceLuogoNascita, ");
            query.Append($"@DataNascita, ");
            query.Append($"@Regione, ");
            query.Append($"@CodiceRegione, ");
            query.Append($"@Provincia, ");
            query.Append($"@CodiceProvincia, ");
            query.Append($"@Comune, ");
            query.Append($"@CodiceComune, ");
            query.Append($"@Indirizzo, ");
            query.Append($"@Cap, ");
            query.Append($"@IdIspettorato, ");
            query.Append($"@Pec, ");
            query.Append($"@Email, ");
            query.Append($"@Telefono, ");
            query.Append($"@IdTipologiaAssegnatario, ");
            query.Append($"@NumeroAutorizzazione, ");
            query.Append($"@IdTipologiaAutorizzazione, ");
            query.Append($"@IdStatoAutorizzazione, ");
            query.Append($"@DataRilascio, ");
            query.Append($"@DataScadenza, ");
            query.Append($"@RiferimentoDocAutorizzazione, ");
            query.Append($"@DocumentoVisibileFe, ");
            query.Append($"@IdRadioamatore, ");
            query.Append($"@CodiceProtocolloEntrata, ");
            query.Append($"@DataProtocolloEntrata, ");
            query.Append($"@CodiceProtocolloUscita, ");
            query.Append($"@DataProtocolloUscita, ");
            query.Append($"@Utente, ");
            query.Append($"@IsRinnovo, ");
            query.Append($"@IdAutorizzazioneRinnovata)");


            var queryTriggerOutput = new StringBuilder();

            queryTriggerOutput.Append($" INSERT INTO {NomeTabella.AutorizzazioniTriggerOutput}");
            queryTriggerOutput.Append($" ([ID_AUTORIZZAZIONE]");
            queryTriggerOutput.Append($",[CODICE_DOMANDA]");
            queryTriggerOutput.Append($",[COGNOME]");
            queryTriggerOutput.Append($",[NOME]");
            queryTriggerOutput.Append($",[DENOMINAZIONE]");
            queryTriggerOutput.Append($",[CODICE_FISCALE]");
            queryTriggerOutput.Append($",[SESSO]");
            queryTriggerOutput.Append($",[LUOGO_NASCITA]");
            queryTriggerOutput.Append($",[CODICE_LUOGO_NASCITA]");
            queryTriggerOutput.Append($",[DATA_NASCITA]");
            queryTriggerOutput.Append($",[REGIONE_RESIDENZA]");
            queryTriggerOutput.Append($",[CODICE_REGIONE_RESIDENZA]");
            queryTriggerOutput.Append($",[PROVINCIA_RESIDENZA]");
            queryTriggerOutput.Append($",[CODICE_PROVINCIA_RESIDENZA]");
            queryTriggerOutput.Append($",[COMUNE_RESIDENZA]");
            queryTriggerOutput.Append($",[CODICE_COMUNE_RESIDENZA]");
            queryTriggerOutput.Append($",[INDIRIZZO_RESIDENZA]");
            queryTriggerOutput.Append($",[CAP_RESIDENZA]");
            queryTriggerOutput.Append($",[ID_ANAGRAFICA_ISPETTORATO]");
            queryTriggerOutput.Append($",[PEC]");
            queryTriggerOutput.Append($",[EMAIL]");
            queryTriggerOutput.Append($",[TELEFONO]");
            queryTriggerOutput.Append($",[ID_TIPO_ASSEGNATARIO]");
            queryTriggerOutput.Append($",[NUMERO_AUTORIZZAZIONE]");
            queryTriggerOutput.Append($",[ID_TIPO_AUTORIZZAZIONE]");
            queryTriggerOutput.Append($",[ID_STATO_AUTORIZZAZIONE]");
            queryTriggerOutput.Append($",[DATA_RILASCIO]");
            queryTriggerOutput.Append($",[DATA_SCADENZA]");
            queryTriggerOutput.Append($",[RIFERIMENTO_DOCUMENTO_AUTORIZZAZIONE]");
            queryTriggerOutput.Append($",[DOCUMENTO_AUTORIZZAZIONE_VISIBILE_FE]");
            queryTriggerOutput.Append($",[ID_RADIOAMATORE]");
            queryTriggerOutput.Append($",[CODICE_PROTOCOLLO_ENTRATA]");
            queryTriggerOutput.Append($",[DATA_PROTOCOLLO_ENTRATA]");
            queryTriggerOutput.Append($",[CODICE_PROTOCOLLO_USCITA]");
            queryTriggerOutput.Append($",[DATA_PROTOCOLLO_USCITA]");
            queryTriggerOutput.Append($",[UTENTE]");
            queryTriggerOutput.Append($",[RINNOVO]");
            queryTriggerOutput.Append($",[ID_AUTORIZZAZIONE_RINNOVATA]");
            queryTriggerOutput.Append($",[TIMESTAMP_MODIFICA]");
            queryTriggerOutput.Append(") VALUES ");

            queryTriggerOutput.Append($"(@IdAutorizzazione, ");
            queryTriggerOutput.Append($"@CodiceDomanda, ");
            queryTriggerOutput.Append($"@Cognome, ");
            queryTriggerOutput.Append($"@Nome, ");
            queryTriggerOutput.Append($"@Denominazione, ");
            queryTriggerOutput.Append($"@CodiceFiscale, ");
            queryTriggerOutput.Append($"@Sesso, ");
            queryTriggerOutput.Append($"@LuogoNascita, ");
            queryTriggerOutput.Append($"@CodiceLuogoNascita, ");
            queryTriggerOutput.Append($"@DataNascita, ");
            queryTriggerOutput.Append($"@Regione, ");
            queryTriggerOutput.Append($"@CodiceRegione, ");
            queryTriggerOutput.Append($"@Provincia, ");
            queryTriggerOutput.Append($"@CodiceProvincia, ");
            queryTriggerOutput.Append($"@Comune, ");
            queryTriggerOutput.Append($"@CodiceComune, ");
            queryTriggerOutput.Append($"@Indirizzo, ");
            queryTriggerOutput.Append($"@Cap, ");
            queryTriggerOutput.Append($"@IdIspettorato, ");
            queryTriggerOutput.Append($"@Pec, ");
            queryTriggerOutput.Append($"@Email, ");
            queryTriggerOutput.Append($"@Telefono, ");
            queryTriggerOutput.Append($"@IdTipologiaAssegnatario, ");
            queryTriggerOutput.Append($"@NumeroAutorizzazione, ");
            queryTriggerOutput.Append($"@IdTipologiaAutorizzazione, ");
            queryTriggerOutput.Append($"@IdStatoAutorizzazione, ");
            queryTriggerOutput.Append($"@DataRilascio, ");
            queryTriggerOutput.Append($"@DataScadenza, ");
            queryTriggerOutput.Append($"@RiferimentoDocAutorizzazione, ");
            queryTriggerOutput.Append($"@DocumentoVisibileFe, ");
            queryTriggerOutput.Append($"@IdRadioamatore, ");
            queryTriggerOutput.Append($"@CodiceProtocolloEntrata, ");
            queryTriggerOutput.Append($"@DataProtocolloEntrata, ");
            queryTriggerOutput.Append($"@CodiceProtocolloUscita, ");
            queryTriggerOutput.Append($"@DataProtocolloUscita, ");
            queryTriggerOutput.Append($"@Utente, ");
            queryTriggerOutput.Append($"@IsRinnovo, ");
            queryTriggerOutput.Append($"@IdAutorizzazioneRinnovata, ");
            queryTriggerOutput.Append($"@Timestamp)");


            using (var connection = CreateConnection())
            {
                connection.Open();
                using (var tran = connection.BeginTransaction())
                {
                    try
                    {
                        int idAutorizzazione = await connection.ExecuteScalarAsync<int>(
                        query.ToString(),
                        new
                        {
                            CodiceDomanda = autorizzazione.CODICE_DOMANDA,
                            Cognome = autorizzazione.COGNOME,
                            Nome = autorizzazione.NOME,
                            Denominazione = autorizzazione.DENOMINAZIONE,
                            CodiceFiscale = autorizzazione.CODICE_FISCALE,
                            Sesso = autorizzazione.SESSO,
                            LuogoNascita = autorizzazione.LUOGO_NASCITA,
                            CodiceLuogoNascita = autorizzazione.CODICE_LUOGO_NASCITA,
                            DataNascita = autorizzazione.DATA_NASCITA,
                            Regione = autorizzazione.REGIONE_RESIDENZA,
                            CodiceRegione = autorizzazione.CODICE_REGIONE_RESIDENZA,
                            Provincia = autorizzazione.PROVINCIA_RESIDENZA,
                            CodiceProvincia = autorizzazione.CODICE_PROVINCIA_RESIDENZA,
                            Comune = autorizzazione.COMUNE_RESIDENZA,
                            CodiceComune = autorizzazione.CODICE_COMUNE_RESIDENZA,
                            Indirizzo = autorizzazione.INDIRIZZO_RESIDENZA,
                            Cap = autorizzazione.CAP_RESIDENZA,
                            IdIspettorato = autorizzazione.ID_ANAGRAFICA_ISPETTORATO,
                            Pec = autorizzazione.PEC,
                            Email = autorizzazione.EMAIL,
                            Telefono = autorizzazione.TELEFONO,
                            IdTipologiaAssegnatario = autorizzazione.ID_TIPO_ASSEGNATARIO,
                            NumeroAutorizzazione = autorizzazione.NUMERO_AUTORIZZAZIONE,
                            IdTipologiaAutorizzazione = autorizzazione.ID_TIPO_AUTORIZZAZIONE,
                            IdStatoAutorizzazione = autorizzazione.ID_STATO_AUTORIZZAZIONE,
                            DataRilascio = autorizzazione.DATA_RILASCIO,
                            DataScadenza = autorizzazione.DATA_SCADENZA,
                            RiferimentoDocAutorizzazione = autorizzazione.RIFERIMENTO_DOCUMENTO_AUTORIZZAZIONE,
                            DocumentoVisibileFe = autorizzazione.DOCUMENTO_AUTORIZZAZIONE_VISIBILE_FE,
                            IdRadioamatore = autorizzazione.ID_RADIOAMATORE,
                            CodiceProtocolloEntrata = autorizzazione.CODICE_PROTOCOLLO_ENTRATA,
                            DataProtocolloEntrata = autorizzazione.DATA_PROTOCOLLO_ENTRATA,
                            CodiceProtocolloUscita = autorizzazione.CODICE_PROTOCOLLO_USCITA,
                            DataProtocolloUscita = autorizzazione.DATA_PROTOCOLLO_USCITA,
                            Utente = autorizzazione.UTENTE,
                            IsRinnovo = autorizzazione.RINNOVO,
                            IdAutorizzazioneRinnovata = autorizzazione.ID_AUTORIZZAZIONE_RINNOVATA
                        }, tran);

                        _ = await connection.ExecuteAsync(
                            queryTriggerOutput.ToString(),
                            new
                            {
                                IdAutorizzazione = idAutorizzazione,
                                CodiceDomanda = autorizzazione.CODICE_DOMANDA,
                                Cognome = autorizzazione.COGNOME,
                                Nome = autorizzazione.NOME,
                                Denominazione = autorizzazione.DENOMINAZIONE,
                                CodiceFiscale = autorizzazione.CODICE_FISCALE,
                                Sesso = autorizzazione.SESSO,
                                LuogoNascita = autorizzazione.LUOGO_NASCITA,
                                CodiceLuogoNascita = autorizzazione.CODICE_LUOGO_NASCITA,
                                DataNascita = autorizzazione.DATA_NASCITA,
                                Regione = autorizzazione.REGIONE_RESIDENZA,
                                CodiceRegione = autorizzazione.CODICE_REGIONE_RESIDENZA,
                                Provincia = autorizzazione.PROVINCIA_RESIDENZA,
                                CodiceProvincia = autorizzazione.CODICE_PROVINCIA_RESIDENZA,
                                Comune = autorizzazione.COMUNE_RESIDENZA,
                                CodiceComune = autorizzazione.CODICE_COMUNE_RESIDENZA,
                                Indirizzo = autorizzazione.INDIRIZZO_RESIDENZA,
                                Cap = autorizzazione.CAP_RESIDENZA,
                                IdIspettorato = autorizzazione.ID_ANAGRAFICA_ISPETTORATO,
                                Pec = autorizzazione.PEC,
                                Email = autorizzazione.EMAIL,
                                Telefono = autorizzazione.TELEFONO,
                                IdTipologiaAssegnatario = autorizzazione.ID_TIPO_ASSEGNATARIO,
                                NumeroAutorizzazione = autorizzazione.NUMERO_AUTORIZZAZIONE,
                                IdTipologiaAutorizzazione = autorizzazione.ID_TIPO_AUTORIZZAZIONE,
                                IdStatoAutorizzazione = autorizzazione.ID_STATO_AUTORIZZAZIONE,
                                DataRilascio = autorizzazione.DATA_RILASCIO,
                                DataScadenza = autorizzazione.DATA_SCADENZA,
                                RiferimentoDocAutorizzazione = autorizzazione.RIFERIMENTO_DOCUMENTO_AUTORIZZAZIONE,
                                DocumentoVisibileFe = autorizzazione.DOCUMENTO_AUTORIZZAZIONE_VISIBILE_FE,
                                IdRadioamatore = autorizzazione.ID_RADIOAMATORE,
                                CodiceProtocolloEntrata = autorizzazione.CODICE_PROTOCOLLO_ENTRATA,
                                DataProtocolloEntrata = autorizzazione.DATA_PROTOCOLLO_ENTRATA,
                                CodiceProtocolloUscita = autorizzazione.CODICE_PROTOCOLLO_USCITA,
                                DataProtocolloUscita = autorizzazione.DATA_PROTOCOLLO_USCITA,
                                Utente = autorizzazione.UTENTE,
                                IsRinnovo = autorizzazione.RINNOVO,
                                IdAutorizzazioneRinnovata = autorizzazione.ID_AUTORIZZAZIONE_RINNOVATA,
                                Timestamp = DateTime.Now
                            }, tran);
                        tran.Commit();
                        return idAutorizzazione;
                    }
                    catch (Exception ex)
                    {
                        tran.Rollback();
                        throw;
                    }
                }
            }
        }

        public async Task<int> UpdateAutorizzazioneAsync(Autorizzazione autorizzazione)
        {
            var query = new StringBuilder();
            query.Append($" UPDATE [dbo].[TB_AUTORIZZAZIONI] SET ");
            query.Append($" [COGNOME] = @Cognome");
            query.Append($",[NOME] = @Nome");
            query.Append($",[DENOMINAZIONE] = @Denominazione");
            query.Append($",[CODICE_FISCALE] = @CodiceFiscale");
            query.Append($",[SESSO] = @Sesso");
            query.Append($",[LUOGO_NASCITA] = @LuogoNascita");
            query.Append($",[CODICE_LUOGO_NASCITA] = @CodiceLuogoNascita");
            query.Append($",[DATA_NASCITA] = @DataNascita");
            query.Append($",[REGIONE_RESIDENZA] = @Regione");
            query.Append($",[CODICE_REGIONE_RESIDENZA] = @CodiceRegione");
            query.Append($",[PROVINCIA_RESIDENZA] = @Provincia");
            query.Append($",[CODICE_PROVINCIA_RESIDENZA] = @CodiceProvincia");
            query.Append($",[COMUNE_RESIDENZA] = @Comune");
            query.Append($",[CODICE_COMUNE_RESIDENZA] = @CodiceComune");
            query.Append($",[INDIRIZZO_RESIDENZA] = @Indirizzo");
            query.Append($",[CAP_RESIDENZA] = @Cap");
            query.Append($",[ID_ANAGRAFICA_ISPETTORATO] = @IdIspettorato");
            query.Append($",[PEC] = @Pec");
            query.Append($",[EMAIL] = @Email");
            query.Append($",[TELEFONO] = @Telefono");
            query.Append($",[ID_TIPO_ASSEGNATARIO] = @IdTipologiaAssegnatario");
            query.Append($",[ID_TIPO_AUTORIZZAZIONE] = @IdTipologiaAutorizzazione");
            query.Append($",[ID_STATO_AUTORIZZAZIONE] = @IdStatoAutorizzazione");
            query.Append($",[DATA_RILASCIO] = @DataRilascio");
            query.Append($",[DATA_SCADENZA] = @DataScadenza");
            query.Append($",[RIFERIMENTO_DOCUMENTO_AUTORIZZAZIONE] = @RiferimentoDocAutorizzazione");
            query.Append($",[DOCUMENTO_AUTORIZZAZIONE_VISIBILE_FE] = @DocumentoVisibileFe");
            query.Append($",[ID_RADIOAMATORE] = @IdRadioamatore");
            query.Append($",[CODICE_PROTOCOLLO_ENTRATA] = @CodiceProtocolloEntrata");
            query.Append($",[DATA_PROTOCOLLO_ENTRATA] = @DataProtocolloEntrata");
            query.Append($",[CODICE_PROTOCOLLO_USCITA] = @CodiceProtocolloUscita");
            query.Append($",[DATA_PROTOCOLLO_USCITA] = @DataProtocolloUscita");
            query.Append($",[UTENTE] = @Utente");
            query.Append($",[RINNOVO] = @IsRinnovo");
            query.Append($",[ID_AUTORIZZAZIONE_RINNOVATA] = @IdAutorizzazioneRinnovata");
            query.Append($" WHERE [ID_AUTORIZZAZIONE] = @IdAutorizzazione");

            using (var connection = CreateConnection())
            {
                connection.Open();

                int result = await connection.ExecuteAsync(
                    query.ToString(),
                    new
                    {
                        CodiceDomanda = autorizzazione.CODICE_DOMANDA,
                        Cognome = autorizzazione.COGNOME,
                        Nome = autorizzazione.NOME,
                        Denominazione = autorizzazione.DENOMINAZIONE,
                        CodiceFiscale = autorizzazione.CODICE_FISCALE,
                        Sesso = autorizzazione.SESSO,
                        LuogoNascita = autorizzazione.LUOGO_NASCITA,
                        CodiceLuogoNascita = autorizzazione.CODICE_LUOGO_NASCITA,
                        DataNascita = autorizzazione.DATA_NASCITA,
                        Regione = autorizzazione.REGIONE_RESIDENZA,
                        CodiceRegione = autorizzazione.CODICE_REGIONE_RESIDENZA,
                        Provincia = autorizzazione.PROVINCIA_RESIDENZA,
                        CodiceProvincia = autorizzazione.CODICE_PROVINCIA_RESIDENZA,
                        Comune = autorizzazione.COMUNE_RESIDENZA,
                        CodiceComune = autorizzazione.CODICE_COMUNE_RESIDENZA,
                        Indirizzo = autorizzazione.INDIRIZZO_RESIDENZA,
                        Cap = autorizzazione.CAP_RESIDENZA,
                        IdIspettorato = autorizzazione.ID_ANAGRAFICA_ISPETTORATO,
                        Pec = autorizzazione.PEC,
                        Email = autorizzazione.EMAIL,
                        Telefono = autorizzazione.TELEFONO,
                        IdTipologiaAssegnatario = autorizzazione.ID_TIPO_ASSEGNATARIO,
                        IdTipologiaAutorizzazione = autorizzazione.ID_TIPO_AUTORIZZAZIONE,
                        IdStatoAutorizzazione = autorizzazione.ID_STATO_AUTORIZZAZIONE,
                        DataRilascio = autorizzazione.DATA_RILASCIO,
                        DataScadenza = autorizzazione.DATA_SCADENZA,
                        RiferimentoDocAutorizzazione = autorizzazione.RIFERIMENTO_DOCUMENTO_AUTORIZZAZIONE,
                        DocumentoVisibileFe = autorizzazione.DOCUMENTO_AUTORIZZAZIONE_VISIBILE_FE,
                        IdRadioamatore = autorizzazione.ID_RADIOAMATORE,
                        CodiceProtocolloEntrata = autorizzazione.CODICE_PROTOCOLLO_ENTRATA,
                        DataProtocolloEntrata = autorizzazione.DATA_PROTOCOLLO_ENTRATA,
                        CodiceProtocolloUscita = autorizzazione.CODICE_PROTOCOLLO_USCITA,
                        DataProtocolloUscita = autorizzazione.DATA_PROTOCOLLO_USCITA,
                        Utente = autorizzazione.UTENTE,
                        IsRinnovo = autorizzazione.RINNOVO,
                        IdAutorizzazioneRinnovata = autorizzazione.ID_AUTORIZZAZIONE_RINNOVATA,
                        IdAutorizzazione = autorizzazione.ID_AUTORIZZAZIONE
                    });
                return result > 0 ? autorizzazione.ID_AUTORIZZAZIONE : 0;
            }
        }

        public async Task<int> UpdateVisibilitaAttestatoByIdAsync(int idAutorizzazione, bool isAttestatoVisibile)
        {
            var query = new StringBuilder();
            query.Append($" UPDATE {NomeTabella.Autorizzazioni} ");
            query.Append($" SET {nameof(Autorizzazione.DOCUMENTO_AUTORIZZAZIONE_VISIBILE_FE)} = @IsAttestatoVisibile");
            query.Append($" WHERE {nameof(Autorizzazione.ID_AUTORIZZAZIONE)} = @IdAutorizzazione");

            using (var connection = CreateConnection())
            {
                connection.Open();

                int result = await connection.ExecuteAsync(
                    query.ToString(),
                    new
                    {
                        IsAttestatoVisibile = isAttestatoVisibile,
                        IdAutorizzazione = idAutorizzazione
                    });
                return result;
            }
        }

        public async Task<int> UpdateIdRadioamatoreByIdAsync(int idRadioamatore, int idAutorizzazione)
        {
            var query = new StringBuilder();
            query.Append($" UPDATE {NomeTabella.Autorizzazioni} ");
            query.Append($" SET {nameof(Autorizzazione.ID_RADIOAMATORE)} = @IdRadioamatore ");
            query.Append($" WHERE {nameof(Autorizzazione.ID_AUTORIZZAZIONE)} = @IdAutorizzazione ");

            using (var connection = CreateConnection())
            {
                connection.Open();

                int result = await connection.ExecuteAsync(
                    query.ToString(),
                    new
                    {
                        IdRadioamatore = idRadioamatore,
                        IdAutorizzazione = idAutorizzazione
                    });
                return result;
            }
        }

        public async Task<int> UpdateRiferimentoDocumentoByIdAsync(int idAutorizzazione, string riferimentoDocumento)
        {
            var query = new StringBuilder();
            query.Append($" UPDATE {NomeTabella.Autorizzazioni} ");
            query.Append($" SET {nameof(Autorizzazione.RIFERIMENTO_DOCUMENTO_AUTORIZZAZIONE)} = @RiferimentoDocumento");
            query.Append($" WHERE {nameof(Autorizzazione.ID_AUTORIZZAZIONE)} = @IdAutorizzazione");

            using (var connection = CreateConnection())
            {
                connection.Open();

                int result = await connection.ExecuteAsync(
                    query.ToString(),
                    new
                    {
                        RiferimentoDocumento = riferimentoDocumento,
                        IdAutorizzazione = idAutorizzazione
                    });
                return result;
            }
        }

        public async Task<int> UpdateStatoAutorizzazioneByIdAsync(short idStatoAutorizzazione, int idAutorizzazione)
        {
            var query = new StringBuilder();
            query.Append($" UPDATE {NomeTabella.Autorizzazioni} SET ");
            query.Append($" {nameof(Autorizzazione.ID_STATO_AUTORIZZAZIONE)} = @IdStato");
            query.Append($" WHERE {nameof(Autorizzazione.ID_AUTORIZZAZIONE)} = @IdAutorizzazione");

            using (var connection = CreateConnection())
            {
                connection.Open();

                int result = await connection.ExecuteAsync(
                    query.ToString(),
                    new
                    {
                        IdStato = idStatoAutorizzazione,
                        IdAutorizzazione = idAutorizzazione
                    });
                return result > 0 ? idAutorizzazione : -1;
            }
        }

        public async Task<int> UpdateStatoAutorizzazioniByIdAsync(short idStatoAutorizzazione, List<int> listaIdAutorizzazione)
        {
            var query = new StringBuilder();
            query.Append($" UPDATE {NomeTabella.Autorizzazioni} SET ");
            query.Append($" {nameof(Autorizzazione.ID_STATO_AUTORIZZAZIONE)} = @IdStato");
            query.Append($" WHERE {nameof(Autorizzazione.ID_AUTORIZZAZIONE)} IN @IdAutorizzazione");

            using (var connection = CreateConnection())
            {
                connection.Open();

                int result = await connection.ExecuteAsync(
                    query.ToString(),
                    new
                    {
                        IdStato = idStatoAutorizzazione,
                        IdAutorizzazione = listaIdAutorizzazione
                    });
                return result;
            }
        }

        public async Task<long> GetNextSequenceValue()
        {
            string query = $"SELECT NEXT VALUE FOR {NomeSequence.CodiceAutorizzazione}";
            using (var connection = CreateConnection())
            {
                connection.Open();
                long result = await connection.ExecuteScalarAsync<long>(query);
                return result;
            }
        }

        public async Task<int> SetProtocolloAsync(string codiceDomandaFE, DateTime dataProtocollo, string numeroProtocollo)
        {
            var query = new StringBuilder();
            query.Append($" UPDATE {NomeTabella.Autorizzazioni} ");
            query.Append($" SET [CODICE_PROTOCOLLO_ENTRATA] = @numeroProtocollo ");
            query.Append($" , [DATA_PROTOCOLLO_ENTRATA] = @dataProtocollo ");
            query.Append($" WHERE [CODICE_DOMANDA] = @codiceDomandaFE ");

            using (var connection = CreateConnection())
            {
                connection.Open();
                using (var tran = connection.BeginTransaction())
                {
                    try
                    {
                        var ret = await connection.ExecuteAsync(query.ToString(),
                            new
                            {
                                dataProtocollo = dataProtocollo,
                                codiceDomandaFE = codiceDomandaFE,
                                numeroProtocollo = numeroProtocollo
                            },
                            tran);

                        tran.Commit();

                        return ret;
                    }
                    catch (Exception ex)
                    {
                        tran.Rollback();
                        throw;
                    }
                }
            }
        }

        public async Task<int> SetProtocolloUscitaAsync(int idAutorizzazione,
                                                            DateTime dataProtocolloUscita,
                                                            string codiceProtocolloUscita,
                                                            DateTime dataRilascioAutorizzazione,
                                                            string riferimentoAutorizzazione)
        {
            var querySetProtocollo = new StringBuilder();
            querySetProtocollo.Append($" UPDATE {NomeTabella.Autorizzazioni} ");
            querySetProtocollo.Append($" SET {nameof(Autorizzazione.ID_STATO_AUTORIZZAZIONE)} = @idStato ");
            querySetProtocollo.Append($" , {nameof(Autorizzazione.CODICE_PROTOCOLLO_USCITA)} = @codiceProtocollo ");
            querySetProtocollo.Append($" , {nameof(Autorizzazione.DATA_PROTOCOLLO_USCITA)} = @dataProtocollo ");
            querySetProtocollo.Append($" , {nameof(Autorizzazione.DATA_RILASCIO)} = @dataRilascio ");
            querySetProtocollo.Append($" , {nameof(Autorizzazione.RIFERIMENTO_DOCUMENTO_AUTORIZZAZIONE)} = @riferimentoAutorizzazione ");
            querySetProtocollo.Append($" WHERE {nameof(Autorizzazione.ID_AUTORIZZAZIONE)} = @idAutorizzazione ");

            using (var connection = CreateConnection())
            {
                connection.Open();

                var autorizzazione = await GetAutorizzazioniByIdAsync(idAutorizzazione);

                using (var tran = connection.BeginTransaction())
                {
                    try
                    {
                        _ = await connection.ExecuteAsync(querySetProtocollo.ToString(),
                            new
                            {
                                idStato = (short)EnumStatoAutorizzazione.Attiva,
                                codiceProtocollo = codiceProtocolloUscita,
                                dataProtocollo = dataProtocolloUscita,
                                dataRilascio = dataRilascioAutorizzazione,
                                riferimentoAutorizzazione,
                                idAutorizzazione
                            },
                            tran);

                        if (autorizzazione.ID_RADIOAMATORE.HasValue)
                        {
                            var scadenzaNominativo = new StringBuilder();
                            scadenzaNominativo.Append($"UPDATE {NomeTabella.Radioamatori} ");
                            scadenzaNominativo.Append($"SET {nameof(Radioamatore.DATA_SCADENZA)} = @DataScadenza ");
                            scadenzaNominativo.Append($"WHERE {nameof(Radioamatore.ID_RADIOAMATORE)} = @IdRadioamatore ");

                            var dataScadenza = autorizzazione.DATA_SCADENZA?.AddYears(1);

                            _ = await connection.ExecuteAsync(scadenzaNominativo.ToString(),
                                new { DataScadenza = dataScadenza, IdRadioamatore = autorizzazione.ID_RADIOAMATORE }, tran);
                        }

                        if (autorizzazione.ID_AUTORIZZAZIONE_RINNOVATA.HasValue)
                        {
                            var scadenzaNominativo = new StringBuilder();
                            scadenzaNominativo.Append($"UPDATE {NomeTabella.Autorizzazioni} ");
                            scadenzaNominativo.Append($"SET {nameof(Autorizzazione.ID_STATO_AUTORIZZAZIONE)} = @idStato ");
                            scadenzaNominativo.Append($"WHERE {nameof(Autorizzazione.ID_AUTORIZZAZIONE)} = @IdAutorizzazione ");

                            _ = await connection.ExecuteAsync(scadenzaNominativo.ToString(),
                                new { idStato = (short)EnumStatoAutorizzazione.Disattivata, IdAutorizzazione = autorizzazione.ID_AUTORIZZAZIONE_RINNOVATA }, tran);
                        }

                        tran.Commit();

                        return idAutorizzazione;
                    }
                    catch (Exception)
                    {
                        tran.Rollback();
                        throw;
                    }
                }
            }
        }

        #region Metodi privati

        private (StringBuilder, Dictionary<string, object>) PreparaParametriRicercaAutorizzazioniQuery(AutorizzazioneFilterDtoBase parametri)
        {
            // query di ricerca
            var query = new StringBuilder();
            query.Append($" SELECT p.*, s.*, i.*, t.*, r.* FROM {NomeTabella.Autorizzazioni} AS p");
            query.Append($" JOIN {NomeTabella.StatiAutorizzazione} AS s");
            query.Append($"     ON p.{nameof(Autorizzazione.ID_STATO_AUTORIZZAZIONE)} = s.{nameof(StatoAutorizzazione.ID_STATO_AUTORIZZAZIONE)}");
            query.Append($" JOIN {NomeTabella.AnagraficaIspettorati} AS i");
            query.Append($"     ON p.{nameof(Autorizzazione.ID_ANAGRAFICA_ISPETTORATO)} = i.{nameof(AnagraficaIspettorato.ID)}");
            query.Append($" JOIN {NomeTabella.TipoAutorizzazione} AS t");
            query.Append($"     ON p.{nameof(Autorizzazione.ID_TIPO_AUTORIZZAZIONE)} = t.{nameof(TipoAutorizzazione.ID_TIPO_AUTORIZZAZIONE)}");
            query.Append($" LEFT OUTER JOIN {NomeTabella.Radioamatori} AS r");
            query.Append($"     ON p.{nameof(Autorizzazione.ID_RADIOAMATORE)} = r.{nameof(Radioamatore.ID_RADIOAMATORE)}");

            query.Append($" WHERE ( @Cognome IS NULL OR p.{nameof(Autorizzazione.COGNOME)} LIKE '%' +  @Cognome + '%')");
            query.Append($" AND (@Nome IS NULL OR p.{nameof(Autorizzazione.NOME)}  LIKE '%' + @Nome + '%') ");
            query.Append($" AND (@Denominazione IS NULL OR p.{nameof(Autorizzazione.DENOMINAZIONE)}  LIKE '%' + @Denominazione + '%') ");
            query.Append($" AND (@CodiceFiscale IS NULL OR p.{nameof(Autorizzazione.CODICE_FISCALE)}  LIKE '%' + @CodiceFiscale + '%') ");
            query.Append($" AND (@DataNascita IS NULL OR p.{nameof(Autorizzazione.DATA_NASCITA)}  = @DataNascita) ");
            query.Append($" AND (@CodiceRegione IS NULL OR p.{nameof(Autorizzazione.CODICE_REGIONE_RESIDENZA)}  LIKE '%' + @CodiceRegione + '%') ");
            query.Append($" AND (@CodiceProvincia IS NULL OR p.{nameof(Autorizzazione.CODICE_PROVINCIA_RESIDENZA)}  LIKE '%' + @CodiceProvincia + '%') ");
            query.Append($" AND (@Regione IS NULL OR p.{nameof(Autorizzazione.REGIONE_RESIDENZA)}  LIKE '%' + @Regione + '%') ");
            query.Append($" AND (@Provincia IS NULL OR p.{nameof(Autorizzazione.PROVINCIA_RESIDENZA)}  LIKE '%' + @Provincia + '%') ");
            query.Append($" AND (@NumeroAutorizzazione IS NULL OR p.{nameof(Autorizzazione.NUMERO_AUTORIZZAZIONE)}  LIKE '%' + @NumeroAutorizzazione + '%') ");
            query.Append($" AND (@IdStato IS NULL OR p.{nameof(Autorizzazione.ID_STATO_AUTORIZZAZIONE)}  = @IdStato) ");
            query.Append($" AND (@DataRilascio IS NULL OR p.{nameof(Autorizzazione.DATA_RILASCIO)}  = @DataRilascio) ");
            query.Append($" AND (@Nominativo IS NULL OR r.{nameof(Radioamatore.CODICE_NOMINATIVO)}  LIKE '%' + @Nominativo + '%') ");
            query.Append($" AND (@IdTipologiaAutorizzazione IS NULL OR p.{nameof(Autorizzazione.ID_TIPO_AUTORIZZAZIONE)}  = @IdTipologiaAutorizzazione) ");
            query.Append($" AND (@IdRadioamatore IS NULL OR p.{nameof(Autorizzazione.ID_RADIOAMATORE)}  = @IdRadioamatore) ");

            // Parametri per la ricerca
            var _p = new Dictionary<string, object>();

            _p.Add("Cognome", parametri.Cognome);
            _p.Add("Nome", parametri.Nome);
            _p.Add("Denominazione", parametri.Denominazione);
            _p.Add("CodiceFiscale", parametri.CodiceFiscale);
            _p.Add("DataNascita", parametri.DataDiNascita);
            _p.Add("CodiceRegione", parametri.CodiceRegione);
            _p.Add("CodiceProvincia", parametri.CodiceProvincia);
            _p.Add("Regione", parametri.Regione);
            _p.Add("Provincia", parametri.Provincia);
            _p.Add("NumeroAutorizzazione", parametri.NumeroAutorizzazione);
            _p.Add("IdStato", parametri.IdStatoAutorizzazione);
            _p.Add("DataRilascio", parametri.DataRilascio);
            _p.Add("Nominativo", parametri.Nominativo);
            _p.Add("IdTipologiaAutorizzazione", parametri.IdTipologiaAutorizzazione);
            _p.Add("IdRadioamatore", parametri.IdRadioamatore);

            return (query, _p);
        }

        #endregion
    }

}
