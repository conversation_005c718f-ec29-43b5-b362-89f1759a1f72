﻿using Dapper;
using Microsoft.Extensions.Configuration;
using MicroSV_Radioamatori.BL.DTOs;
using MicroSV_Radioamatori.BL.Enums;
using MicroSV_Radioamatori.BL.IncomingDTOs;
using MicroSV_Radioamatori.BL.ValueObject;
using MicroSV_Radioamatori.DAL.Common;
using MicroSV_Radioamatori.DAL.Entities;
using MicroSV_Radioamatori.DAL.Interfaces;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using static Microsoft.ApplicationInsights.MetricDimensionNames.TelemetryContext;

namespace MicroSV_Radioamatori.DAL.Services
{
    public class IscrizioniSWLDal : BaseDal, IIscrizioniSWLDal
    {
        public IscrizioniSWLDal(IConfiguration conf) : base(conf)
        {

        }

        public async Task<IscrizioneSWL> GetIscrizioneSWLByIdAsync(int idIscrizioneSWL)
        {
            var query = new StringBuilder();
            query.Append($" SELECT isc.*, s.*, i.* FROM {NomeTabella.IscrizioniSWL} AS isc");
            query.Append($" JOIN {NomeTabella.StatiIscrizione} AS s");
            query.Append($"     ON isc.{nameof(IscrizioneSWL.ID_STATO_ISCRIZIONE_SWL)} = s.{nameof(StatoIscrizioneSWL.ID_STATO_ISCRIZIONE_SWL)}");
            query.Append($" JOIN {NomeTabella.AnagraficaIspettorati} AS i");
            query.Append($"     ON isc.{nameof(IscrizioneSWL.ID_ANAGRAFICA_ISPETTORATO)} = i.{nameof(AnagraficaIspettorato.ID)}");

            query.Append($" WHERE ( @IdIscrizioneSWL IS NULL OR isc.{nameof(IscrizioneSWL.ID_ISCRIZIONE_SWL)} = @IdIscrizioneSWL )");

            using (var connection = CreateConnection())
            {
                return (await connection.QueryAsync<IscrizioneSWL, StatoIscrizioneSWL, AnagraficaIspettorato, IscrizioneSWL>(query.ToString(),
                        (isc, s, i) =>
                        {
                            isc.STATO_ISCRIZIONE_SWL = s;
                            isc.ANAGRAFICA_ISPETTORATO = i;
                            return isc;
                        },
                        splitOn: $"{nameof(StatoIscrizioneSWL.ID_STATO_ISCRIZIONE_SWL)}, {nameof(AnagraficaIspettorato.ID)}",
                        param: new
                        {
                            IdIscrizioneSWL = idIscrizioneSWL
                        }
                    )).AsQueryable().FirstOrDefault();
            }
        }

        public async Task<List<IscrizioneSWL>> GetIscrizioniSWLByFilterAsync(IscrizioneSWLFilterDto parametri)
        {

            var query = PreparaParametriRicercaRadioamatoreQuery(parametri);

            if (!string.IsNullOrWhiteSpace(parametri.IdIspettorato))
                query.Item1.Append($" AND (@IdIspettorato IS NULL OR isc.{nameof(IscrizioneSWL.ID_ANAGRAFICA_ISPETTORATO)}  LIKE '%' + @IdIspettorato + '%') ");

            query.Item2.Add("IdIspettorato", parametri.IdIspettorato);

            using (var connection = CreateConnection())
            {
                var ret = (await connection.QueryAsync<IscrizioneSWL, StatoIscrizioneSWL, AnagraficaIspettorato, IscrizioneSWL>(query.Item1.ToString(),
                        (isc, s, i) =>
                        {
                            isc.STATO_ISCRIZIONE_SWL = s;
                            isc.ANAGRAFICA_ISPETTORATO = i;
                            return isc;
                        },
                        splitOn: $"{nameof(StatoIscrizioneSWL.ID_STATO_ISCRIZIONE_SWL)}, {nameof(AnagraficaIspettorato.ID)}",
                        param: query.Item2
                    )).AsQueryable().ToList();

                return ret;
            }


        }
        
        public async Task<List<IscrizioneSWL>> GetIscrizioniSWLByIspettoratoConGestioneSubentriAsync(IscrizioneSWLFilterDtoBase parametri, string idIspettorato)
        {

            var query = PreparaParametriRicercaRadioamatoreQuery(parametri);

            if (!string.IsNullOrWhiteSpace(idIspettorato))
                query.Item1.Append($" AND ( @IdIspettorato IS NULL OR i.{nameof(AnagraficaIspettorato.ID)} = @IdIspettorato OR i.{nameof(AnagraficaIspettorato.ID_SUBENTRO)} = @IdIspettorato ) "); // Come prenderlo?

            query.Item2.Add("IdIspettorato", idIspettorato);

            using (var connection = CreateConnection())
            {
                var ret = (await connection.QueryAsync<IscrizioneSWL, StatoIscrizioneSWL, AnagraficaIspettorato, IscrizioneSWL>(query.Item1.ToString(),
                        (isc, s, i) =>
                        {
                            isc.STATO_ISCRIZIONE_SWL = s;
                            isc.ANAGRAFICA_ISPETTORATO = i;
                            return isc;
                        },
                        splitOn: $"{nameof(StatoIscrizioneSWL.ID_STATO_ISCRIZIONE_SWL)}, {nameof(AnagraficaIspettorato.ID)}",
                        param: query.Item2
                    )).AsQueryable().ToList();

                return ret;
            }


        }

        public Task<long> GetNextSequenceValue()
        {
            throw new NotImplementedException();
        }

        public async Task<int> InsertIscrizioneSWLAsync(IscrizioneSWL iscrizioneSWL)
        {
            var query = new StringBuilder();
            query.Append($" INSERT INTO {NomeTabella.IscrizioniSWL} ");
            query.Append($"({nameof(IscrizioneSWL.CODICE_DOMANDA)}");
            query.Append($",{nameof(IscrizioneSWL.COGNOME)}");
            query.Append($",{nameof(IscrizioneSWL.NOME)}");
            query.Append($",{nameof(IscrizioneSWL.CODICE_FISCALE)}");
            query.Append($",{nameof(IscrizioneSWL.SESSO)}");
            query.Append($",{nameof(IscrizioneSWL.LUOGO_NASCITA)}");
            query.Append($",{nameof(IscrizioneSWL.CODICE_LUOGO_NASCITA)}");
            query.Append($",{nameof(IscrizioneSWL.DATA_NASCITA)}");
            query.Append($",{nameof(IscrizioneSWL.REGIONE_RESIDENZA)}");
            query.Append($",{nameof(IscrizioneSWL.CODICE_REGIONE_RESIDENZA)}");
            query.Append($",{nameof(IscrizioneSWL.PROVINCIA_RESIDENZA)}");
            query.Append($",{nameof(IscrizioneSWL.CODICE_PROVINCIA_RESIDENZA)}");
            query.Append($",{nameof(IscrizioneSWL.COMUNE_RESIDENZA)}");
            query.Append($",{nameof(IscrizioneSWL.CODICE_COMUNE_RESIDENZA)}");
            query.Append($",{nameof(IscrizioneSWL.INDIRIZZO_RESIDENZA)}");
            query.Append($",{nameof(IscrizioneSWL.CAP_RESIDENZA)}");
            query.Append($",{nameof(IscrizioneSWL.ID_ANAGRAFICA_ISPETTORATO)}");
            query.Append($",{nameof(IscrizioneSWL.PEC)}");
            query.Append($",{nameof(IscrizioneSWL.EMAIL)}");
            query.Append($",{nameof(IscrizioneSWL.TELEFONO)}");
            query.Append($",{nameof(IscrizioneSWL.TIPOLOGIA_RICHIEDENTE)}");
            query.Append($",{nameof(IscrizioneSWL.NUMERO_ISCRIZIONE_SWL)}");
            query.Append($",{nameof(IscrizioneSWL.ID_STATO_ISCRIZIONE_SWL)}");
            query.Append($",{nameof(IscrizioneSWL.DATA_RILASCIO)}");
            query.Append($",{nameof(IscrizioneSWL.RIFERIMENTO_DOCUMENTO_ISCRIZIONE_SWL)}");
            query.Append($",{nameof(IscrizioneSWL.CODICE_PROTOCOLLO_ENTRATA)}");
            query.Append($",{nameof(IscrizioneSWL.DATA_PROTOCOLLO_ENTRATA)}");
            query.Append($",{nameof(IscrizioneSWL.CODICE_PROTOCOLLO_USCITA)}");
            query.Append($",{nameof(IscrizioneSWL.DATA_PROTOCOLLO_USCITA)}");
            query.Append($",{nameof(IscrizioneSWL.UTENTE)})");
            query.Append($" OUTPUT Inserted.{nameof(IscrizioneSWL.ID_ISCRIZIONE_SWL)} VALUES ");

            query.Append($"(@codice_domanda");
            query.Append($",@cognome");
            query.Append($",@nome");
            query.Append($",@codice_fiscale");
            query.Append($",@sesso");
            query.Append($",@luogo_nascita");
            query.Append($",@codice_luogo_nascita");
            query.Append($",@data_nascita");
            query.Append($",@regione_residenza");
            query.Append($",@codice_regione_residenza");
            query.Append($",@provincia_residenza");
            query.Append($",@codice_provincia_residenza");
            query.Append($",@comune_residenza");
            query.Append($",@codice_comune_residenza");
            query.Append($",@indirizzo_residenza");
            query.Append($",@cap_residenza");
            query.Append($",@id_anagrafica_ispettorato");
            query.Append($",@pec");
            query.Append($",@email");
            query.Append($",@telefono");
            query.Append($",@id_tipologia_richiedente");
            query.Append($",@numero_iscrizione_swl");
            query.Append($",@id_stato_iscrizione_swl");
            query.Append($",@data_rilascio");
            query.Append($",@riferimento_documento_iscrizione_swl");
            query.Append($",@codice_protocollo_entrata");
            query.Append($",@data_protocollo_entrata");
            query.Append($",@codice_protocollo_uscita");
            query.Append($",@data_protocollo_uscita");
            query.Append($",@utente)");

            var queryTO = new StringBuilder();
            queryTO.Append($" INSERT INTO {NomeTabella.IscrizioniSWLTriggerOutput} ");
            queryTO.Append($"({nameof(IscrizioneSWL.ID_ISCRIZIONE_SWL)}");
            queryTO.Append($", {nameof(IscrizioneSWL.CODICE_DOMANDA)}");
            queryTO.Append($",{nameof(IscrizioneSWL.COGNOME)}");
            queryTO.Append($",{nameof(IscrizioneSWL.NOME)}");
            queryTO.Append($",{nameof(IscrizioneSWL.CODICE_FISCALE)}");
            queryTO.Append($",{nameof(IscrizioneSWL.SESSO)}");
            queryTO.Append($",{nameof(IscrizioneSWL.LUOGO_NASCITA)}");
            queryTO.Append($",{nameof(IscrizioneSWL.CODICE_LUOGO_NASCITA)}");
            queryTO.Append($",{nameof(IscrizioneSWL.DATA_NASCITA)}");
            queryTO.Append($",{nameof(IscrizioneSWL.REGIONE_RESIDENZA)}");
            queryTO.Append($",{nameof(IscrizioneSWL.CODICE_REGIONE_RESIDENZA)}");
            queryTO.Append($",{nameof(IscrizioneSWL.PROVINCIA_RESIDENZA)}");
            queryTO.Append($",{nameof(IscrizioneSWL.CODICE_PROVINCIA_RESIDENZA)}");
            queryTO.Append($",{nameof(IscrizioneSWL.COMUNE_RESIDENZA)}");
            queryTO.Append($",{nameof(IscrizioneSWL.CODICE_COMUNE_RESIDENZA)}");
            queryTO.Append($",{nameof(IscrizioneSWL.INDIRIZZO_RESIDENZA)}");
            queryTO.Append($",{nameof(IscrizioneSWL.CAP_RESIDENZA)}");
            queryTO.Append($",{nameof(IscrizioneSWL.ID_ANAGRAFICA_ISPETTORATO)}");
            queryTO.Append($",{nameof(IscrizioneSWL.PEC)}");
            queryTO.Append($",{nameof(IscrizioneSWL.EMAIL)}");
            queryTO.Append($",{nameof(IscrizioneSWL.TELEFONO)}");
            queryTO.Append($",{nameof(IscrizioneSWL.TIPOLOGIA_RICHIEDENTE)}");
            queryTO.Append($",{nameof(IscrizioneSWL.NUMERO_ISCRIZIONE_SWL)}");
            queryTO.Append($",{nameof(IscrizioneSWL.ID_STATO_ISCRIZIONE_SWL)}");
            queryTO.Append($",{nameof(IscrizioneSWL.DATA_RILASCIO)}");
            queryTO.Append($",{nameof(IscrizioneSWL.RIFERIMENTO_DOCUMENTO_ISCRIZIONE_SWL)}");
            queryTO.Append($",{nameof(IscrizioneSWL.CODICE_PROTOCOLLO_ENTRATA)}");
            queryTO.Append($",{nameof(IscrizioneSWL.DATA_PROTOCOLLO_ENTRATA)}");
            queryTO.Append($",{nameof(IscrizioneSWL.CODICE_PROTOCOLLO_USCITA)}");
            queryTO.Append($",{nameof(IscrizioneSWL.DATA_PROTOCOLLO_USCITA)}");
            queryTO.Append($",{nameof(IscrizioneSWL.UTENTE)}");
            queryTO.Append($",TIMESTAMP_MODIFICA)");
            queryTO.Append($" OUTPUT Inserted.{nameof(IscrizioneSWL.ID_ISCRIZIONE_SWL)} VALUES ");

            queryTO.Append($"(@id_iscrizione_swl");
            queryTO.Append($",@codice_domanda");
            queryTO.Append($",@cognome");
            queryTO.Append($",@nome");
            queryTO.Append($",@codice_fiscale");
            queryTO.Append($",@sesso");
            queryTO.Append($",@luogo_nascita");
            queryTO.Append($",@codice_luogo_nascita");
            queryTO.Append($",@data_nascita");
            queryTO.Append($",@regione_residenza");
            queryTO.Append($",@codice_regione_residenza");
            queryTO.Append($",@provincia_residenza");
            queryTO.Append($",@codice_provincia_residenza");
            queryTO.Append($",@comune_residenza");
            queryTO.Append($",@codice_comune_residenza");
            queryTO.Append($",@indirizzo_residenza");
            queryTO.Append($",@cap_residenza");
            queryTO.Append($",@id_anagrafica_ispettorato");
            queryTO.Append($",@pec");
            queryTO.Append($",@email");
            queryTO.Append($",@telefono");
            queryTO.Append($",@id_tipologia_richiedente");
            queryTO.Append($",@numero_iscrizione_swl");
            queryTO.Append($",@id_stato_iscrizione_swl");
            queryTO.Append($",@data_rilascio");
            queryTO.Append($",@riferimento_documento_iscrizione_swl");
            queryTO.Append($",@codice_protocollo_entrata");
            queryTO.Append($",@data_protocollo_entrata");
            queryTO.Append($",@codice_protocollo_uscita");
            queryTO.Append($",@data_protocollo_uscita");
            queryTO.Append($",@utente");
            queryTO.Append($",@timestamp_modifica)");

            using (var connection = CreateConnection())
            {
                connection.Open();
                using (var tran = connection.BeginTransaction())
                {
                    try
                    {
                        int idIscrizione = await connection.ExecuteScalarAsync<int>(
                        query.ToString(),
                        new
                        {
                            codice_domanda = iscrizioneSWL.CODICE_DOMANDA,
                            cognome = iscrizioneSWL.COGNOME,
                            nome = iscrizioneSWL.NOME,
                            codice_fiscale = iscrizioneSWL.CODICE_FISCALE,
                            sesso = iscrizioneSWL.SESSO,
                            luogo_nascita = iscrizioneSWL.LUOGO_NASCITA,
                            codice_luogo_nascita = iscrizioneSWL.CODICE_LUOGO_NASCITA,
                            data_nascita = iscrizioneSWL.DATA_NASCITA,
                            regione_residenza = iscrizioneSWL.REGIONE_RESIDENZA,
                            codice_regione_residenza = iscrizioneSWL.CODICE_REGIONE_RESIDENZA,
                            provincia_residenza = iscrizioneSWL.PROVINCIA_RESIDENZA,
                            codice_provincia_residenza = iscrizioneSWL.CODICE_PROVINCIA_RESIDENZA,
                            comune_residenza = iscrizioneSWL.COMUNE_RESIDENZA,
                            codice_comune_residenza = iscrizioneSWL.CODICE_COMUNE_RESIDENZA,
                            indirizzo_residenza = iscrizioneSWL.INDIRIZZO_RESIDENZA,
                            cap_residenza = iscrizioneSWL.CAP_RESIDENZA,
                            id_anagrafica_ispettorato = iscrizioneSWL.ID_ANAGRAFICA_ISPETTORATO,
                            pec = iscrizioneSWL.PEC,
                            email = iscrizioneSWL.EMAIL,
                            telefono = iscrizioneSWL.TELEFONO,
                            id_tipologia_richiedente = iscrizioneSWL.TIPOLOGIA_RICHIEDENTE,
                            numero_iscrizione_swl = iscrizioneSWL.NUMERO_ISCRIZIONE_SWL,
                            id_stato_iscrizione_swl = iscrizioneSWL.ID_STATO_ISCRIZIONE_SWL,
                            data_rilascio = iscrizioneSWL.DATA_RILASCIO,
                            riferimento_documento_iscrizione_swl = iscrizioneSWL.RIFERIMENTO_DOCUMENTO_ISCRIZIONE_SWL,
                            codice_protocollo_entrata = iscrizioneSWL.CODICE_PROTOCOLLO_ENTRATA,
                            data_protocollo_entrata = iscrizioneSWL.DATA_PROTOCOLLO_ENTRATA,
                            codice_protocollo_uscita = iscrizioneSWL.CODICE_PROTOCOLLO_USCITA,
                            data_protocollo_uscita = iscrizioneSWL.DATA_PROTOCOLLO_USCITA,
                            utente = iscrizioneSWL.UTENTE
                        }, tran);

                        _ = await connection.ExecuteAsync(
                            queryTO.ToString(),
                            new
                            {
                                id_iscrizione_swl = idIscrizione,
                                codice_domanda = iscrizioneSWL.CODICE_DOMANDA,
                                cognome = iscrizioneSWL.COGNOME,
                                nome = iscrizioneSWL.NOME,
                                codice_fiscale = iscrizioneSWL.CODICE_FISCALE,
                                sesso = iscrizioneSWL.SESSO,
                                luogo_nascita = iscrizioneSWL.LUOGO_NASCITA,
                                codice_luogo_nascita = iscrizioneSWL.CODICE_LUOGO_NASCITA,
                                data_nascita = iscrizioneSWL.DATA_NASCITA,
                                regione_residenza = iscrizioneSWL.REGIONE_RESIDENZA,
                                codice_regione_residenza = iscrizioneSWL.CODICE_REGIONE_RESIDENZA,
                                provincia_residenza = iscrizioneSWL.PROVINCIA_RESIDENZA,
                                codice_provincia_residenza = iscrizioneSWL.CODICE_PROVINCIA_RESIDENZA,
                                comune_residenza = iscrizioneSWL.COMUNE_RESIDENZA,
                                codice_comune_residenza = iscrizioneSWL.CODICE_COMUNE_RESIDENZA,
                                indirizzo_residenza = iscrizioneSWL.INDIRIZZO_RESIDENZA,
                                cap_residenza = iscrizioneSWL.CAP_RESIDENZA,
                                id_anagrafica_ispettorato = iscrizioneSWL.ID_ANAGRAFICA_ISPETTORATO,
                                pec = iscrizioneSWL.PEC,
                                email = iscrizioneSWL.EMAIL,
                                telefono = iscrizioneSWL.TELEFONO,
                                id_tipologia_richiedente = iscrizioneSWL.TIPOLOGIA_RICHIEDENTE,
                                numero_iscrizione_swl = iscrizioneSWL.NUMERO_ISCRIZIONE_SWL,
                                id_stato_iscrizione_swl = iscrizioneSWL.ID_STATO_ISCRIZIONE_SWL,
                                data_rilascio = iscrizioneSWL.DATA_RILASCIO,
                                riferimento_documento_iscrizione_swl = iscrizioneSWL.RIFERIMENTO_DOCUMENTO_ISCRIZIONE_SWL,
                                codice_protocollo_entrata = iscrizioneSWL.CODICE_PROTOCOLLO_ENTRATA,
                                data_protocollo_entrata = iscrizioneSWL.DATA_PROTOCOLLO_ENTRATA,
                                codice_protocollo_uscita = iscrizioneSWL.CODICE_PROTOCOLLO_USCITA,
                                data_protocollo_uscita = iscrizioneSWL.DATA_PROTOCOLLO_USCITA,
                                utente = iscrizioneSWL.UTENTE,
                                timestamp_modifica = DateTime.Now
                            }, tran);
                        tran.Commit();
                        return idIscrizione;
                    }
                    catch (Exception)
                    {
                        tran.Rollback();
                        throw;
                    }
                }
            }

        }

        public async Task<int> SetProtocolloAsync(string codiceDomandaFE, DateTime dataProtocollo, string numeroProtocollo, string numeroIscrizioneSWL)
        {
            var query = new StringBuilder();
            query.Append($" UPDATE {NomeTabella.IscrizioniSWL} ");
            query.Append($" SET {nameof(IscrizioneSWL.CODICE_PROTOCOLLO_ENTRATA)} = @numeroProtocollo ");
            query.Append($" , {nameof(IscrizioneSWL.DATA_PROTOCOLLO_ENTRATA)} = @dataProtocollo ");
            query.Append($" , {nameof(IscrizioneSWL.NUMERO_ISCRIZIONE_SWL)} = @numeroIscrizioneSWL ");
            query.Append($" WHERE {nameof(IscrizioneSWL.CODICE_DOMANDA)} = @codiceDomandaFE ");

            using (var connection = CreateConnection())
            {
                connection.Open();
                using (var tran = connection.BeginTransaction())
                {
                    try
                    {
                        var ret = await connection.ExecuteAsync(query.ToString(),
                            new
                            {
                                numeroProtocollo,
                                dataProtocollo,
                                numeroIscrizioneSWL,
                                codiceDomandaFE
                            },
                            tran);

                        tran.Commit();

                        return ret;
                    }
                    catch (Exception)
                    {
                        tran.Rollback();
                        throw;
                    }
                }
            }
        }

        public async Task<int> SetProtocolloUscitaAsync(int idIscrizioneSWL, DateTime dataProtocolloUscita, string codiceProtocolloUscita, DateTime dataRilascio, string riferimentoDocumento)
        {
            var query = new StringBuilder();
            query.Append($" UPDATE {NomeTabella.IscrizioniSWL} ");
            query.Append($" SET {nameof(IscrizioneSWL.ID_STATO_ISCRIZIONE_SWL)} = @idStato ");
            query.Append($" , {nameof(IscrizioneSWL.CODICE_PROTOCOLLO_USCITA)} = @codiceProtocollo ");
            query.Append($" , {nameof(IscrizioneSWL.DATA_PROTOCOLLO_USCITA)} = @dataProtocollo ");
            query.Append($" , {nameof(IscrizioneSWL.DATA_RILASCIO)} = @dataRilascio ");
            query.Append($" , {nameof(IscrizioneSWL.RIFERIMENTO_DOCUMENTO_ISCRIZIONE_SWL)} = @riferimentoDocumento ");
            query.Append($" WHERE {nameof(IscrizioneSWL.ID_ISCRIZIONE_SWL)} = @idIscrizioneSWL ");

            using (var connection = CreateConnection())
            {
                connection.Open();

                var iscrizione = await GetIscrizioneSWLByIdAsync(idIscrizioneSWL);

                using (var tran = connection.BeginTransaction())
                {
                    try
                    {
                        _ = await connection.ExecuteAsync(query.ToString(),
                            new
                            {
                                idStato = (short)EnumStatoIscrizioneSWL.Attiva,
                                codiceProtocollo = codiceProtocolloUscita,
                                dataProtocollo = dataProtocolloUscita,
                                dataRilascio,
                                riferimentoDocumento,
                                idIscrizioneSWL
                            },
                            tran);

                        tran.Commit();

                        return idIscrizioneSWL;
                    }
                    catch (Exception)
                    {
                        tran.Rollback();
                        throw;
                    }
                }
            }
        }

        public async Task<int> UpdateStatoIscrizioneSWLByCodiceAsync(int idStatoIscrizioneSWL, int idIscrizioneSWL)
        {
            var query = new StringBuilder();
            query.Append($" UPDATE {NomeTabella.IscrizioniSWL} SET ");
            query.Append($" {nameof(IscrizioneSWL.ID_STATO_ISCRIZIONE_SWL)} = @IdStato");
            query.Append($" WHERE {nameof(IscrizioneSWL.ID_ISCRIZIONE_SWL)} = @IdAutorizzazione");

            using (var connection = CreateConnection())
            {
                connection.Open();

                int result = await connection.ExecuteAsync(
                    query.ToString(),
                    new
                    {
                        IdStato = idStatoIscrizioneSWL,
                        IdAutorizzazione = idIscrizioneSWL
                    });
                return result > 0 ? idIscrizioneSWL : -1;
            }
        }

        #region Metodi privati

        private (StringBuilder, Dictionary<string, object>) PreparaParametriRicercaRadioamatoreQuery(IscrizioneSWLFilterDtoBase parametri)
        {
            var query = new StringBuilder();
            query.Append($" SELECT isc.*, s.*, i.* FROM {NomeTabella.IscrizioniSWL} AS isc");
            query.Append($" JOIN {NomeTabella.StatiIscrizione} AS s");
            query.Append($"     ON isc.{nameof(IscrizioneSWL.ID_STATO_ISCRIZIONE_SWL)} = s.{nameof(StatoIscrizioneSWL.ID_STATO_ISCRIZIONE_SWL)}");
            query.Append($" JOIN {NomeTabella.AnagraficaIspettorati} AS i");
            query.Append($"     ON isc.{nameof(IscrizioneSWL.ID_ANAGRAFICA_ISPETTORATO)} = i.{nameof(AnagraficaIspettorato.ID)}");

            query.Append($" WHERE ( @Cognome IS NULL OR isc.{nameof(IscrizioneSWL.COGNOME)} LIKE '%' +  @Cognome + '%')");
            query.Append($" AND (@Nome IS NULL OR isc.{nameof(IscrizioneSWL.NOME)}  LIKE '%' + @Nome + '%') ");
            query.Append($" AND (@CodiceFiscale IS NULL OR isc.{nameof(IscrizioneSWL.CODICE_FISCALE)}  LIKE '%' + @CodiceFiscale + '%') ");
            query.Append($" AND (@Regione IS NULL OR isc.{nameof(IscrizioneSWL.REGIONE_RESIDENZA)}  LIKE '%' + @Regione + '%') ");
            query.Append($" AND (@Provincia IS NULL OR isc.{nameof(IscrizioneSWL.PROVINCIA_RESIDENZA)}  LIKE '%' + @Provincia + '%') ");
            query.Append($" AND (@NumeroIscrizione IS NULL OR isc.{nameof(IscrizioneSWL.NUMERO_ISCRIZIONE_SWL)}  LIKE '%' + @NumeroIscrizione + '%') ");
            query.Append($" AND (@IdStato IS NULL OR isc.{nameof(IscrizioneSWL.ID_STATO_ISCRIZIONE_SWL)}  = @IdStato) ");
            query.Append($" AND (@DataRilascio IS NULL OR isc.{nameof(IscrizioneSWL.DATA_RILASCIO)}  = @DataRilascio) ");

            // Parametri per la ricerca
            var _p = new Dictionary<string, object>();

            _p.Add("Nome", parametri.Nome);
            _p.Add("Cognome", parametri.Cognome);
            _p.Add("CodiceFiscale", parametri.CodiceFiscale);
            _p.Add("NumeroIscrizione", parametri.NumeroIscrizioneSWL);
            _p.Add("IdStato", parametri.IdStatoIscrizioneSWL);
            _p.Add("Provincia", parametri.ProvinciaResidenza);
            _p.Add("Regione", parametri.RegioneResidenza);
            _p.Add("DataRilascio", parametri.DataRilascio);
            
            

            

            return (query, _p);
        }

        #endregion
    }
}
