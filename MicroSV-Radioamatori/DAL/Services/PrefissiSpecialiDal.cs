﻿using Dapper;
using Invitalia.Misure.Utilities.WebApi.Refit.XRM;
using Microsoft.Extensions.Configuration;
using MicroSV_Radioamatori.DAL.Common;
using MicroSV_Radioamatori.DAL.Entities;
using MicroSV_Radioamatori.DAL.Interfaces;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MicroSV_Radioamatori.DAL.Services
{
    public class PrefissiSpecialiDal : BaseDal, IPrefissiSpecialiDal
    {
        public PrefissiSpecialiDal(IConfiguration conf, HttpClientFactory httpClientFactory) : base(conf, httpClientFactory)
        {
        }

        public async Task<List<PrefissoSpeciale>> GetPrefissiNominativiSpecialiAsync()
        {
            var query = new StringBuilder();
            query.Append($" SELECT * FROM {NomeTabella.PrefissoNominativoSpeciale} as p");
            query.Append($"   JOIN {NomeTabella.Regioni} AS r ");
            query.Append($"     ON p.{nameof(PrefissoSpeciale.SIGLA_REGIONE)} = r.{nameof(Regione.SIGLA)} ");
            query.Append($" ORDER BY r.{nameof(Regione.DENOMINAZIONE)}");


            using (var connection = CreateConnection())
            {
                return connection.Query<PrefissoSpeciale, Regione, PrefissoSpeciale>(query.ToString(),
                    (p, r) =>
                    {
                        p.REGIONE = r;
                        return p;
                    },
                    splitOn: $"{nameof(PrefissoSpeciale.SIGLA_REGIONE)}, {nameof(Regione.SIGLA)}")
                    .AsQueryable()
                    .ToList();
            }           
        }

        /// <summary>
        /// Se basilicata inserire la sigla provincia (MT/PZ)
        /// </summary>
        /// <param name="siglaRegione"></param>
        /// <returns></returns>
        public async Task<PrefissoSpeciale> GetPrefissoNominativoSpecialiBySiglaRegioneAsync(string siglaRegione)
        {
            var query = new StringBuilder();
            query.Append($" SELECT * FROM {NomeTabella.PrefissoNominativoSpeciale} as p");
            query.Append($"   JOIN {NomeTabella.Regioni} AS r ");
            query.Append($"     ON p.{nameof(PrefissoSpeciale.SIGLA_REGIONE)} = r.{nameof(Regione.SIGLA)} ");
            query.Append($" WHERE p.{nameof(PrefissoSpeciale.SIGLA_REGIONE)} = @siglaRegione");


            using (var connection = CreateConnection())
            {
                return connection.Query<PrefissoSpeciale, Regione, PrefissoSpeciale>(query.ToString(),
                    (p, r) =>
                    {
                        p.REGIONE = r;
                        return p;
                    },
                    splitOn: $"{nameof(PrefissoSpeciale.SIGLA_REGIONE)}, {nameof(Regione.SIGLA)}",
                    param: new { siglaRegione = siglaRegione }
                    ).AsQueryable()
                    .FirstOrDefault();
            }
        }
    }
}
