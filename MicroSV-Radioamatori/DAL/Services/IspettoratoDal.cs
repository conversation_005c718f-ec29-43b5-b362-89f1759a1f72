﻿using Dapper;
using Invitalia.Misure.Utilities.WebApi.Refit.XRM;
using Microsoft.Extensions.Configuration;
using MicroSV_Radioamatori.DAL.Common;
using MicroSV_Radioamatori.DAL.Entities;
using MicroSV_Radioamatori.DAL.Interfaces;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using System.Linq;
using System;

namespace MicroSV_Radioamatori.DAL.Services
{
    public class IspettoratoDal : BaseDal, IIspettoratoDal
    {
        public IspettoratoDal(IConfiguration conf, HttpClientFactory httpClientFactory) : base(conf, httpClientFactory)
        {
        }

        public async Task<List<AnagraficaIspettorato>> GetAnagraficaIspettoratiAsync(bool? visualizzaAncheDismessi = null)
        {
            var query = new StringBuilder();
            query.Append($" SELECT * FROM {NomeTabella.AnagraficaIspettorati} ");

            if (!visualizzaAncheDismessi.HasValue || (visualizzaAncheDismessi.HasValue && !visualizzaAncheDismessi.Value))
            {
                query.Append($" WHERE {nameof(AnagraficaIspettorato.DATA_DISMISSIONE)} IS NULL ");
            }            

            query.Append($" ORDER BY {nameof(AnagraficaIspettorato.ORDINE)}");

            using (var connection = CreateConnection())
            {
                return (await connection.QueryAsync<AnagraficaIspettorato>(query.ToString())).ToList();
            }
        }

        public async Task<IspettoratoRegione> GetIspettoratoBySiglaRegioneAsync(string siglaRegione)
        {
            var query = new StringBuilder();
            query.Append($" SELECT * FROM {NomeTabella.IspettoratiRegioni} AS i ");
            query.Append($"   JOIN {NomeTabella.AnagraficaIspettorati} AS a ");
            query.Append($"     ON i.{nameof(IspettoratoRegione.ID_ANAGRAFICA_ISPETTORATO)} = a.{nameof(AnagraficaIspettorato.ID)} ");
            query.Append($" WHERE i.{nameof(IspettoratoRegione.SIGLA_REGIONE)} = @siglaRegione");

            using (var connection = CreateConnection())
            {
                return connection.Query<IspettoratoRegione, AnagraficaIspettorato, IspettoratoRegione>(query.ToString(),
                    (i, a) =>
                    {
                        i.Ispettorato = a;
                        return i;
                    },
                    splitOn: $"{nameof(IspettoratoRegione.ID_ANAGRAFICA_ISPETTORATO)}, {nameof(AnagraficaIspettorato.ID)}",
                    param: new { siglaRegione = siglaRegione })
                    .AsQueryable()
                    .FirstOrDefault();
            }
        }
    }
}
