﻿using Dapper;
using Invitalia.Misure.Utilities.WebApi.Refit.XRM;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;

namespace MicroSV_Radioamatori.DAL.Services
{
    public class BaseDal
    {
        protected readonly IConfiguration _conf;
        protected readonly HttpClientFactory _httpClientFactory;
        protected readonly int NUMBER_OF_RETRIES;
        protected readonly double TIMEOUT_SEC;
        protected JsonSerializer jsonSerializerSettings;

        public BaseDal(IConfiguration conf, HttpClientFactory httpClientFactory)
        {
            _conf = conf;
            this._httpClientFactory = httpClientFactory;
            NUMBER_OF_RETRIES = httpClientFactory.GetNumberOfRetries(conf);
            TIMEOUT_SEC = httpClientFactory.GetTimeout(conf);

            this.jsonSerializerSettings = new JsonSerializer()
            {
                NullValueHandling = NullValueHandling.Ignore
            };
        }

        public BaseDal(IConfiguration conf)
        {
            _conf = conf;           

            this.jsonSerializerSettings = new JsonSerializer()
            {
                NullValueHandling = NullValueHandling.Ignore
            };
        }


        protected IDbConnection CreateConnection()
        {
            return new SqlConnection(_conf.GetConnectionString("MainConnection"));
        }

        protected List<dynamic> ExecuteStoredProcedure(string procedure, object[] param)
        {
            using (var connection = CreateConnection())
            {
                var results = connection.Query(procedure, param, commandType: CommandType.Text).ToList();
                return results;
            }
        }
    }
}
