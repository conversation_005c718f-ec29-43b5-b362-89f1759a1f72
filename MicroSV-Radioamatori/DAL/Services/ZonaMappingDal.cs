﻿using Dapper;
using Invitalia.Misure.Utilities.WebApi.Refit.XRM;
using Microsoft.Extensions.Configuration;
using MicroSV_Radioamatori.DAL.Common;
using MicroSV_Radioamatori.DAL.Entities;
using MicroSV_Radioamatori.DAL.Interfaces;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MicroSV_Radioamatori.DAL.Services
{
    public class ZonaMappingDal : BaseDal, IZonaMappingDal
    {
        public ZonaMappingDal(IConfiguration conf, HttpClientFactory httpClientFactory) : base(conf, httpClientFactory)
        {
        }

        public async Task<ZonaMapping> GetMappingAsync(string siglaRegione, string siglaProvincia, string codiceIstatComune)
        {
            var query = new StringBuilder();
            query.Append($" SELECT * FROM {NomeTabella.ZoneMapping} AS z ");
            query.Append($"   JOIN {NomeTabella.Regioni} AS r ");
            query.Append($"     ON z.{nameof(ZonaMapping.SIGLA_REGIONE)} = r.{nameof(Regione.SIGLA)} ");
            query.Append($" WHERE z.{nameof(ZonaMapping.SIGLA_REGIONE)} = @siglaRegione ");
            query.Append($" AND z.{nameof(ZonaMapping.SIGLA_PROVINCIA)} = @siglaProvincia ");
            query.Append($" AND z.{nameof(ZonaMapping.CODICE_COMUNE)} = @codiceIstatComune ");

            using (var connection = CreateConnection())
            {
                return connection.Query<ZonaMapping, Regione, ZonaMapping>(query.ToString(),
                    (z, r) =>
                    {
                        z.Regione = r;
                        return z;
                    },
                    splitOn: $"{nameof(ZonaMapping.SIGLA_REGIONE)}, {nameof(Regione.SIGLA)}",
                    param: new { siglaRegione = siglaRegione, siglaProvincia, codiceIstatComune })
                    .AsQueryable()
                    .FirstOrDefault();
            }
        }

        public async Task<CodiceZona> GetCodiceZonaAsync(string zona)
        {
            var query = new StringBuilder();
            query.Append($" SELECT * FROM {NomeTabella.CodiciZone}");
            query.Append($" WHERE {nameof(CodiceZona.ZONA)} = @zona");

            using (var connection = CreateConnection())
            {
                return await connection.QueryFirstOrDefaultAsync<CodiceZona>(query.ToString(), new { zona = zona });
            }
        }

    }
}
