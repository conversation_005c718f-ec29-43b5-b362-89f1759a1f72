﻿using System;

namespace MicroSV_Radioamatori.DAL.Entities
{
    public class IscrizioneSWL
    {

        public int? ID_ISCRIZIONE_SWL { get; set; }
        public string CODICE_DOMANDA { get; set; }
        public string COGNOME { get; set; }
        public string NOME { get; set; }
        public string CODICE_FISCALE { get; set; }
        public string SESSO { get; set; }
        public string LUOGO_NASCITA { get; set; }
        public string CODICE_LUOGO_NASCITA { get; set; }
        public DateTime? DATA_NASCITA { get; set; }
        public string REGIONE_RESIDENZA { get; set; }
        public string CODICE_REGIONE_RESIDENZA { get; set; }
        public string PROVINCIA_RESIDENZA { get; set; }
        public string CODICE_PROVINCIA_RESIDENZA { get; set; }
        public string COMUNE_RESIDENZA { get; set; }
        public string CODICE_COMUNE_RESIDENZA { get; set; }
        public string INDIRIZZO_RESIDENZA { get; set; }
        public string CAP_RESIDENZA { get; set; }
        public string ID_ANAGRAFICA_ISPETTORATO { get; set; }
        public string PEC { get; set; }
        public string EMAIL { get; set; }
        public string TELEFONO { get; set; }
        public string TIPOLOGIA_RICHIEDENTE { get; set; }
        public string NUMERO_ISCRIZIONE_SWL { get; set; }
        public int? ID_STATO_ISCRIZIONE_SWL { get; set; }
        public DateTime? DATA_RILASCIO { get; set; }
        public string RIFERIMENTO_DOCUMENTO_ISCRIZIONE_SWL { get; set; }
        public string CODICE_PROTOCOLLO_ENTRATA { get; set; }
        public DateTime? DATA_PROTOCOLLO_ENTRATA { get; set; }
        public string CODICE_PROTOCOLLO_USCITA { get; set; }
        public DateTime? DATA_PROTOCOLLO_USCITA { get; set; }
        public string UTENTE { get; set; }


        #region Navigation Properties

        public AnagraficaIspettorato ANAGRAFICA_ISPETTORATO { get; set; }
        public StatoIscrizioneSWL STATO_ISCRIZIONE_SWL { get; set; }

        #endregion

    }
}
