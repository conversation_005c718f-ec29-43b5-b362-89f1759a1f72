﻿using System;
using System.Collections.Generic;

namespace MicroSV_Radioamatori.DAL.Entities
{
    public class Radioamatore
    {
        public int ID_RADIOAMATORE { get; set; }
        public int ID_CODICE_ZONA { get; set; }
        public int? ID_SUFFISSO { get; set; }
        public string ID_ANAGRAFICA_ISPETTORATO { get; set; }
        public string NOME { get; set; }
        public string COGNOME { get; set; }
        public string DENOMINAZIONE { get; set; }        
        public DateTime? DATA_NASCITA { get; set; }
        public string CODICE_FISCALE { get; set; }
        public string INDIRIZZO { get; set; }
        public string REGIONE { get; set; }
        public string COMUNE { get; set; }
        public string PROVINCIA { get; set; }
        public string CAP { get; set; }
        public string CODICE_NOMINATIVO { get; set; }
        public string TIPO_NOMINATIVO { get; set; }
        public string STATO_NOMINATIVO { get; set; }
        public DateTime? DATA_RILASCIO { get; set; }
        public string UTENTE { get; set; }
        public DateTime? DATA_AGG { get; set; }
        public string PROTOCOLLO { get; set; }
        public DateTime? DATA_PROTOCOLLO { get; set; }
        public string PROTOCOLLO_PRESENTAZIONE_FE { get; set; }
        public DateTime? DATA_PROTOCOLLO_PRESENTAZIONE_FE { get; set; }
        public string NOTA { get; set; }
        public string CODICE_DOMANDA_FE { get; set; }
        public DateTime? DATA_CREAZIONE { get; set; }
        public string TIPOLOGIA_ASSEGNATARIO { get; set; }
        public DateTime? DATA_SCADENZA { get; set; }

        #region Property di utilità
        public string CODICE_ZONA_DENOMINAZIONE { get; set; }
        public string CODICE_ZONA_STRING { get; set; }
        public bool IS_ZONA_COMUNE_SPECIALE { get; set; }
        public String TIPO_ZONA { get; set; }
        public string SIGLA_REGIONE { get; set; }
        #endregion

        #region Navigation Property
        public CodiceZona CODICE_ZONA { get; set; }
        public Suffisso SUFFISSO { get; set; }   
        public Manifestazione MANIFESTAZIONE { get; set; }
        public Contest CONTEST { get; set; }
        #endregion
    }
}
