﻿using Microsoft.VisualBasic;
using System;

namespace MicroSV_Radioamatori.DAL.Entities
{
    public class AnagraficaIspettorato
    {
        public string ID { get; set; }
        public string ID_SUBENTRO { get; set; }
        public string DENOMINAZIONE { get; set; }
        public short ORDINE { get; set; }
        public string IBAN { get; set; }
        public string CC_POSTALE { get; set; }
        public DateTime? DATA_DISMISSIONE { get; set; }
    }
}
