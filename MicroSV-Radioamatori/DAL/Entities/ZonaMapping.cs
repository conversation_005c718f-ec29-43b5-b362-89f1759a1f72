﻿namespace MicroSV_Radioamatori.DAL.Entities
{
    public class ZonaMapping
    {
        public int ID { get; set; }
        public string SIGLA_REGIONE { get; set; }
        public string SIGLA_PROVINCIA { get; set; }
        public string CODICE_COMUNE { get; set; }
        public bool COMUNE_SPECIALE { get; set; }
        public string ZONA { get; set; }
        public string CODICE { get; set; }
        public Regione Regione { get; set; }
    }
}
