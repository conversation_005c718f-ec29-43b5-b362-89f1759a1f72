﻿using System;

namespace MicroSV_Radioamatori.DAL.Entities
{
    public class Manifestazione
    {
        public int ID_MANIFESTAZIONE { get; set; }
        public int ID_RADIOAMATORE { get; set; }
        public string ID_ANAGRAFICA_ISPETTORATO { get; set; }
        public int ID_TIPOMANIFESTAZIONE { get; set; }
        public string DEN<PERSON>INAZIONE { get; set; }
        public string SIGLA_REGIONE { get; set; }
        public DateTime DATA_INIZIO { get; set; }
        public DateTime DATA_FINE { get; set; }

        #region Property di utilità
        public string NOMINATIVO_PROPOSTO { get; set; }
        #endregion

    }
}
