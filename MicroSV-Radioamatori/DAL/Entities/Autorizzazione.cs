﻿using System;
using static Microsoft.ApplicationInsights.MetricDimensionNames.TelemetryContext;

namespace MicroSV_Radioamatori.DAL.Entities
{
    public class Autorizzazione
    {
        public int ID_AUTORIZZAZIONE {get; set;}
        public string CODICE_DOMANDA  {get; set;}
        public string COGNOME{ get; set;}
        public string NOME { get; set;}
        public string DENOMINAZIONE {get; set;}
        public string CODICE_FISCALE  {get; set;}
        public string SESSO {get; set;}
        public string LUOGO_NASCITA   {get; set;}
        public string CODICE_LUOGO_NASCITA {get; set;}
        public DateTime? DATA_NASCITA    {get; set;}
        public string REGIONE_RESIDENZA {get; set;}
        public string CODICE_REGIONE_RESIDENZA    {get; set;}
        public string PROVINCIA_RESIDENZA {get; set;}
        public string CODICE_PROVINCIA_RESIDENZA  {get; set;}
        public string COMUNE_RESIDENZA {get; set;}
        public string CODICE_COMUNE_RESIDENZA {get; set;}
        public string INDIRIZZO_RESIDENZA {get; set;}
        public string CAP_RESIDENZA   {get; set;}
        public string ID_ANAGRAFICA_ISPETTORATO {get; set;}
        public string PEC {get; set;}
        public string EMAIL {get; set;}
        public string TELEFONO    {get; set;}
        public int? ID_TIPO_ASSEGNATARIO {get; set;}
        public string NUMERO_AUTORIZZAZIONE   {get; set;}
        public int? ID_TIPO_AUTORIZZAZIONE {get; set;}
        public int? ID_STATO_AUTORIZZAZIONE {get; set;}
        public DateTime? DATA_RILASCIO {get; set;}
        public DateTime? DATA_SCADENZA   {get; set;}
        public string RIFERIMENTO_DOCUMENTO_AUTORIZZAZIONE {get; set;}
        public bool? DOCUMENTO_AUTORIZZAZIONE_VISIBILE_FE    {get; set;}
        public int? ID_RADIOAMATORE {get; set;}
        public string CODICE_PROTOCOLLO_ENTRATA   {get; set;}
        public DateTime? DATA_PROTOCOLLO_ENTRATA {get; set;}
        public string CODICE_PROTOCOLLO_USCITA    {get; set;}
        public DateTime? DATA_PROTOCOLLO_USCITA {get; set;}
        public string UTENTE  {get; set;}
        public bool? RINNOVO {get; set;}
        public int? ID_AUTORIZZAZIONE_RINNOVATA { get; set; }
        public string CODICE_NOMINATIVO { get; set; }

        #region Navigation properties
        public TipoAssegnatario TIPO_ASSEGNATARIO { get; set; }
        public TipoAutorizzazione TIPO_AUTORIZZAZIONE { get; set; }
        public StatoAutorizzazione STATO_AUTORIZZAZIONE { get; set; }
        public AnagraficaIspettorato ANAGRAFICA_ISPETTORATO { get; set; }
        public Radioamatore RADIOAMATORE { get; set; }
        #endregion
    }
}
