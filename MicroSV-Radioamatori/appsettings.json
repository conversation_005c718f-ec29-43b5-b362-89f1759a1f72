{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft": "Warning",
      "Microsoft.Hosting.Lifetime": "Information"
    },
    "Console": {
      "LogToStandardErrorThreshold": "Warning"
    },
    "AdapterType": "EventHub",
    "LogInfo": "True",
    "EventHub": {
      "serviceUri": "svilogging-lab.servicebus.windows.net",
      "key": "Write_Event",
      "secret": "YhgBgUal3yNwp0uLef4EGqwF5yOmBdXXBDmSqmLW7M0=",
      "path": "arcsightlogging"
    },
    "SQL": {
      "ConnectionString": "Server=tcp:invsqlsvi001.database.windows.net,1433;Initial Catalog=Arcsight;Persist Security Info=False;User ID=misurer3;Password=*************;MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;"
    },

    "AppDetails": {
      "name": "API.Radioamatore",
      "version": "1.0",
      "vendor": "GRUPPO"
    }
  },
  "AllowedHosts": "*",
  "ConnectionStrings": {
    // Dev
    "MainConnection": "Server=tcp:invsqlsvi001.database.windows.net,1433;Initial Catalog=RadioamatoriDgat-Dev;Persist Security Info=False;User ID=radioamatori_col;Password=****************;MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;"

    // Coll
    //"MainConnection": "Server=tcp:invsqlsvi001.database.windows.net,1433;Initial Catalog=RadioamatoriDgat-Col;Persist Security Info=False;User ID=radioamatori_col;Password=****************;MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;"

  },
  "Swagger.Enabled": true,
  "TimeZoneId": "Central Europe Standard Time", // CET per Docker/Linux
  "ApiConfiguration": "http://devfeconfigurationapiv1.aksingr-internal.lab.invitalia.it",
  "ApiStorageManager": "http://devstoragemanagerapiv1.aksingr-internal.lab.invitalia.it",
  "ApiTipologiche": "http://devapitipologichev1.aksingr-internal.lab.invitalia.it",
  "ApiBlobStorage": "http://devapiblobstoragev1.aksingr-internal.lab.invitalia.it",
  "HttpClient": {
    "NumberRetries": 5,
    "TimeoutSecs": 120
  }
}
