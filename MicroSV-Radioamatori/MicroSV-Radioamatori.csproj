<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net5.0</TargetFramework>
    <RootNamespace>MicroSV_Radioamatori</RootNamespace>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="Controllers\**" />
    <Content Remove="Controllers\**" />
    <EmbeddedResource Remove="Controllers\**" />
    <None Remove="Controllers\**" />
  </ItemGroup>

  <ItemGroup>
    <Compile Remove="WeatherForecast.cs" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="AutoMapper" Version="10.1.1" />
    <PackageReference Include="AutoMapper.Extensions.Microsoft.DependencyInjection" Version="8.1.1" />
    <PackageReference Include="Dapper" Version="2.0.123" />
    <PackageReference Include="Inv.Framework.Api.Core.Logger" Version="5.0.1" />
    <PackageReference Include="Inv.Framework.Api.DTO" Version="1.0.1" />
    <PackageReference Include="Inv.Fwk.Helper.DateTimeZone" Version="1.1.1" />
    <PackageReference Include="Invitalia.Misure.FiltriAPI" Version="5.0.7" />
    <PackageReference Include="Invitalia.Misure.Standard2.Utilities" Version="1.0.1" />
    <PackageReference Include="Invitalia.Misure.Utilities.WebApi.IdentityUserContext" Version="5.1.0" />
    <PackageReference Include="Invitalia.Misure.Utilities.WebApi.OAuthTokenOtherApi" Version="5.0.5" />
    <PackageReference Include="Invitalia.Misure.Utilities.WebApi.Swagger" Version="5.0.0" />
    <PackageReference Include="Microsoft.ApplicationInsights.AspNetCore" Version="2.17.0" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="5.0.5" />
    <PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.10.13" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.1" />
    <PackageReference Include="System.Text.Encodings.Web" Version="4.5.0" />
  </ItemGroup>

  <ProjectExtensions><VisualStudio><UserProperties appsettings_1json__JsonSchema="" /></VisualStudio></ProjectExtensions>

</Project>
