﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <packageSources>
    <add key="nuget.org" value="https://api.nuget.org/v3/index.json" protocolVersion="3" />
    <add key="InvitaliaArtifactsMisure" value="https://pkgs.dev.azure.com/Invitalia/_packaging/InvitaliaArtifactsMisure/nuget/v3/index.json" />
    <add key="InvitaliaArtifacts" value="https://pkgs.dev.azure.com/Invitalia/_packaging/InvitaliaArtifacts/nuget/v3/index.json" />
  </packageSources>
  <packageSourceCredentials>
    <InvitaliaArtifacts>
		<add key="Username" value="<EMAIL>" />
		<add key="ClearTextPassword" value="6jzdveoeopf2537zpj45mm3bw4ulukvhhge74wrcvcocydj5skxa" />
    </InvitaliaArtifacts>
	<InvitaliaArtifactsMisure>
		<add key="Username" value="<EMAIL>" />
		<add key="ClearTextPassword" value="6jzdveoeopf2537zpj45mm3bw4ulukvhhge74wrcvcocydj5skxa" />
    </InvitaliaArtifactsMisure>
  </packageSourceCredentials>
</configuration>