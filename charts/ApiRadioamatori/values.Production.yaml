image:
  tag: 12
settingsEnv:  
  Logging__AdapterType: "EventHub"
  Logging__EventHub__serviceUri: "prodeventhubmisure.servicebus.windows.net"
  Logging__EventHub__key: "Write_Event"
  Logging__EventHub__secret: "yC7AlO+qDm2Dhpikdiq2BY7slI20Ki2CyIqi7inuFDM="
  Logging__EventHub__path: "arcsightlogging"
  Logging__LogInfo: "False"
  ApiStorageManager: "http://storagemanagerapiv1.microservicesapi.svc.cluster.local"
  ApiConfiguration: "http://feconfigurationapiv1.microservicesapi.svc.cluster.local"
  ApiTipologiche: "http://apitipologichev1.microservicesapi.svc.cluster.local"
  ApiBlobStorage: "http://apiblobstoragev1.microservicesapi.svc.cluster.local/"
  Environment: "Produzione"
  TimeZoneId: "CET"
  ConnectionStrings__MainConnection: "Server=tcp:prosqlmisure.database.windows.net,1433;Initial Catalog=RadioamatoriDgat;Persist Security Info=False;User ID=radioamatori;Password=************;MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;"
  
  APPINSIGHTS_INSTRUMENTATIONKEY: "89e05b04-722e-43b4-aea1-83bcdf711efc"
  HttpClient__TimeoutSecs: "240"

replicas:
  min: 2
  max: 128
requests:
  cpu: "20m"
  memory: "512Mi"
limits:
  cpu: "500m"
  memory: "1Gi"
averageutilization:
  memory: 70
  cpu: 70

#helm upgrade --namespace microservicesapi --install --reset-values --force --wait --timeout 600s --set api.version=v1 --set api.name=apiradioamatori --set buildnumber=10758 --set ingressdomain.external=apiradioamatoriv1.aksingr.invitalia.it --set ingressdomain.internal=apiradioamatoriv1.aksingr-internal.invitalia.it -f ApiRadioamatori/values.Production.yaml apiradioamatoriv1 Radioamatori