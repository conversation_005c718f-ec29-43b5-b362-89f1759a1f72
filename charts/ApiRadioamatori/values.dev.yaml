image:
  tag: 12
settingsEnv:  
  Logging__AdapterType: "EventHub"
  Logging__EventHub__serviceUri: "svilogging-lab.servicebus.windows.net"
  Logging__EventHub__key: "Write_Event"
  Logging__EventHub__secret: "YhgBgUal3yNwp0uLef4EGqwF5yOmBdXXBDmSqmLW7M0="
  Logging__EventHub__path: "arcsightlogging"
  Logging__LogInfo: "True"
  ApiStorageManager: "http://devstoragemanagerapiv1.microservicesdev.svc.cluster.local"
  ApiConfiguration: "http://devfeconfigurationapiv1.microservicesdev.svc.cluster.local"  
  ApiTipologiche: "http://devapitipologichev1.microservicesdev.svc.cluster.local"
  ApiBlobStorage: "http://devapiblobstoragev1.microservicesdev.svc.cluster.local"
  Environment: "Sviluppo"
  TimeZoneId: "CET"
  ConnectionStrings__MainConnection: "Server=tcp:invsqlsvi001.database.windows.net,1433;Initial Catalog=RadioamatoriDgat-Dev;Persist Security Info=False;User ID=radioamatori_col;Password=****************;MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;"
  
replicas:
  min: 1
  max: 2
requests:
  cpu: "20m"
  memory: "300Mi"
limits:
  cpu: "200m"
  memory: "500Mi"
averageutilization:
  memory: 90
  cpu: 90
  
  
  
  
  
  