# Deploy to Azure Kubernetes Service
# Build and push image to Azure Container Registry; Deploy to Azure Kubernetes Service
# https://docs.microsoft.com/azure/devops/pipelines/languages/docker

trigger:
  branches:
    include:
    - 01-Rilascio
  paths:
    include:
      - MicroSV-Radioamatori

resources:
 repositories:
   - repository: apimbuildtemplate
     type: git
     name: AKSClusterInfrastructure/DevOps
     ref: refs/heads/master
   - repository: apimpolicyscripts
     type: git
     name: 'Microservizi Misure/API-MANAGEMENT'
     ref: refs/heads/master
variables:

  # Container registry service connection established during pipeline creation
  tag: '$(Build.BuildId)'
  imagePullSecret: 'invitaliasviluppo491f-auth'
  ClusterIngressUrl: 'aksingr.lab.invitalia.it'
  ApiManagementServiceName: 'dev-apim-misure'
  ApiManagementResourceGroup: 'DEV-RG-AKS-Misure'
  ApiVersion: 'v1'
  ApiName: 'apiradioamatori'
  BackendCertThumbprint: '88ECCF72C21B0551C7AD660A49705277B17D4239'
  AKSConnectionName: 'Dev-AKS-Misure-Cluster-microservicesdev-01'
  ChartArtifactPath: 'ApiRadioamatori'
  AKSNamespace: 'microservicesdev'
  AzureRMConnectionName: 'ARM DEV-RG-AKS-Misure'
   # VARIABILI COLLAUDO
  AKSConnectionNameCollaudo: 'Dev-AKS-Misure-Cluster-microservicescoll-01'
  AKSNamespaceCollaudo: 'microservicescoll'
  # VARIABILI PRODUZIONE
  AKSConnectionNameProduzione: 'AKSCluster-Production-Prod-AKS-Misure-Cluster-microservicesapi'
  AKSNamespaceProduzione: 'microservicesapi'
  AzureRMConnectionNameProduzione: 'ARM PROD-RG-AKS-Misure'

stages:
- template: pipelinetemplates/api-template/template-pipeline-apim.yaml@apimbuildtemplate  # Template reference
  parameters:
    Dockerfile: 'MicroSV-Radioamatori'
    # Container registry service connection established during pipeline creation
    tag: '$(Build.BuildId)'
    imagePullSecret: 'invitaliasviluppo491f-auth'
    ClusterIngressUrl: 'aksingr.lab.invitalia.it'
    ApiManagementServiceName: 'dev-apim-misure'
    ApiManagementResourceGroup: 'DEV-RG-AKS-Misure'
    ApiVersion: 'v1'
    ApiName: 'apiradioamatori'
    SetPolicyApimPs1: 'apim-policy-set-utentecorrentenotmandatory-in-singledeploy.ps1'
    BackendCertThumbprint: '88ECCF72C21B0551C7AD660A49705277B17D4239'
    AKSConnectionName: 'Dev-AKS-Misure-Cluster-microservicesdev-01'
    ChartArtifactPath: 'ApiRadioamatori'
    AKSNamespace: 'microservicesdev'
    AzureRMConnectionName: 'ARM DEV-RG-AKS-Misure'
    # VARIABILI COLLAUDO
    AKSConnectionNameCollaudo: 'Dev-AKS-Misure-Cluster-microservicescoll-01'
    AKSNamespaceCollaudo: 'microservicescoll'
    # VARIABILI PRODUZIONE
    AKSConnectionNameProduzione: 'AKSCluster-Production-Prod-AKS-Misure-Cluster-microservicesapi'
    AKSNamespaceProduzione: 'microservicesapi'
    AzureRMConnectionNameProduzione: 'ARM PROD-RG-AKS-Misure'